{"name": "UI Execution 28/06/2025, 23:13:53", "testCases": [{"name": "Postcode Flow_NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3346ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "8086ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1151ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1956ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-<PERSON>-Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "1749ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2118ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4329ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3248ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"8053\"", "status": "passed", "duration": "3171ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "2859ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3117ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "10915ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with text=\"Papanui\" exists", "status": "passed", "duration": "17641ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3868ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2405ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1783ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3545ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "3724ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3109ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"0616\"", "status": "passed", "duration": "3114ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "2850ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3134ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Check if element with text=\"<PERSON>\" exists", "status": "passed", "duration": "13479ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2256ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2003ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3770ms", "action_id": "b06OvcntcY", "screenshot_filename": "b06OvcntcY.png", "report_screenshot": "b06OvcntcY.png", "resolved_screenshot": "screenshots/b06OvcntcY.png", "clean_action_id": "b06OvcntcY", "prefixed_action_id": "al_b06OvcntcY", "action_id_screenshot": "screenshots/b06OvcntcY.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "3691ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3247ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"8053\"", "status": "passed", "duration": "3603ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "2879ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3119ms", "action_id": "fDgFGQYpCw", "screenshot_filename": "fDgFGQYpCw.png", "report_screenshot": "fDgFGQYpCw.png", "resolved_screenshot": "screenshots/fDgFGQYpCw.png", "clean_action_id": "fDgFGQYpCw", "prefixed_action_id": "al_fDgFGQYpCw", "action_id_screenshot": "screenshots/fDgFGQYpCw.png"}, {"name": "Check if element with text=\"Papanui\" exists", "status": "passed", "duration": "11165ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5517ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1565ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2467ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1689ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Collect\"", "status": "passed", "duration": "3188ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "1790ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3458ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "4348ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"0616\"", "status": "passed", "duration": "5228ms", "action_id": "M3dXqigqRv", "screenshot_filename": "M3dXqigqRv.png", "report_screenshot": "M3dXqigqRv.png", "resolved_screenshot": "screenshots/M3dXqigqRv.png", "clean_action_id": "M3dXqigqRv", "prefixed_action_id": "al_M3dXqigqRv", "action_id_screenshot": "screenshots/M3dXqigqRv.png"}, {"name": "Tap on Text: \"AUCKLAND\"", "status": "passed", "duration": "3372ms", "action_id": "kDpsm2D3xt", "screenshot_filename": "kDpsm2D3xt.png", "report_screenshot": "kDpsm2D3xt.png", "resolved_screenshot": "screenshots/kDpsm2D3xt.png", "clean_action_id": "kDpsm2D3xt", "prefixed_action_id": "al_kDpsm2D3xt", "action_id_screenshot": "screenshots/kDpsm2D3xt.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4254ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3392ms", "action_id": "pKjXoj4mNg", "screenshot_filename": "pKjXoj4mNg.png", "report_screenshot": "pKjXoj4mNg.png", "resolved_screenshot": "screenshots/pKjXoj4mNg.png", "clean_action_id": "pKjXoj4mNg", "prefixed_action_id": "al_pKjXoj4mNg", "action_id_screenshot": "screenshots/pKjXoj4mNg.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2217ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "failed", "duration": "1501ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "unknown", "duration": "1909ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"<PERSON>\" exists", "status": "unknown", "duration": "30448ms", "action_id": "OKCHAK6HCJ", "screenshot_filename": "OKCHAK6HCJ.png", "report_screenshot": "OKCHAK6HCJ.png", "resolved_screenshot": "screenshots/OKCHAK6HCJ.png", "clean_action_id": "OKCHAK6HCJ", "prefixed_action_id": "al_OKCHAK6HCJ", "action_id_screenshot": "screenshots/OKCHAK6HCJ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "2451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "2606ms", "action_id": "3hOTINBVMf", "screenshot_filename": "3hOTINBVMf.png", "report_screenshot": "3hOTINBVMf.png", "resolved_screenshot": "screenshots/3hOTINBVMf.png", "clean_action_id": "3hOTINBVMf", "prefixed_action_id": "al_3hOTINBVMf", "action_id_screenshot": "screenshots/3hOTINBVMf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "2052ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Browse & PDP NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            37 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3292ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2712ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[@name=\"txtShopMenuTitle\"]", "status": "passed", "duration": "1459ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2715ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2776ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2784ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*", "status": "passed", "duration": "1779ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2262ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1828ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2315ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "1701ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if image \"product-share-logo.png\" exists on screen", "status": "passed", "duration": "11501ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "3054ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Add to bag\"]", "status": "passed", "duration": "2121ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "3640ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"more\"", "status": "passed", "duration": "3188ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2142ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1965ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2412ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Check if image \"search-result-test-se.png\" exists on screen", "status": "passed", "duration": "21438ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2314ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2231ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1977ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1308ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1296ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2282ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3214ms", "action_id": "b06OvcntcY", "screenshot_filename": "b06OvcntcY.png", "report_screenshot": "b06OvcntcY.png", "resolved_screenshot": "screenshots/b06OvcntcY.png", "clean_action_id": "b06OvcntcY", "prefixed_action_id": "al_b06OvcntcY", "action_id_screenshot": "screenshots/b06OvcntcY.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3996ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "iOS Function: text - Text: \"notebook\"", "status": "passed", "duration": "2365ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1813ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2282ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2292ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "1641ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2082ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2062ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1062ms", "action_id": "fDgFGQYpCw", "screenshot_filename": "fDgFGQYpCw.png", "report_screenshot": "fDgFGQYpCw.png", "resolved_screenshot": "screenshots/fDgFGQYpCw.png", "clean_action_id": "fDgFGQYpCw", "prefixed_action_id": "al_fDgFGQYpCw", "action_id_screenshot": "screenshots/fDgFGQYpCw.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Delivery & CNC- NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            27 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3210ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5475ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1146ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1920ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-<PERSON>-Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4236ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "iOS Function: text - Text: \"P_43250042\"", "status": "passed", "duration": "2393ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1623ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2082ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1895ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2075ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if image \"cnc-tab-se.png\" exists on screen", "status": "passed", "duration": "11177ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on Text: \"Collect\"", "status": "passed", "duration": "3086ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2845ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2166ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2223ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2091ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5123ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2062ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2120ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3863ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "iOS Function: text - Text: \"P_43250042\"", "status": "passed", "duration": "2405ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1631ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2095ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2095ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery Buy Step NZ (33 steps)", "status": "passed", "duration": "0ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "NZ- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3224ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5018ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4812ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1156ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-<PERSON>-Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "4089ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"txtMy orders\"]", "status": "passed", "duration": "1590ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy orders\"]", "status": "passed", "duration": "2080ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5021ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"main\"]//XCUIElementTypeLink)[4]", "status": "passed", "duration": "2166ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "5420ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2075ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "3878ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "2411ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2951ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "3141ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2861ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2092ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2850ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2196ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2828ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2223ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2778ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2138ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2142ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2762ms", "action_id": "b06OvcntcY", "screenshot_filename": "b06OvcntcY.png", "report_screenshot": "b06OvcntcY.png", "resolved_screenshot": "screenshots/b06OvcntcY.png", "clean_action_id": "b06OvcntcY", "prefixed_action_id": "al_b06OvcntcY", "action_id_screenshot": "screenshots/b06OvcntcY.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "2770ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3466ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"Allow While Using App\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Allow While Using App\"]", "status": "passed", "duration": "20180ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap and Type at (29, 262): \"0616\"", "status": "passed", "duration": "5236ms", "action_id": "fDgFGQYpCw", "screenshot_filename": "fDgFGQYpCw.png", "report_screenshot": "fDgFGQYpCw.png", "resolved_screenshot": "screenshots/fDgFGQYpCw.png", "clean_action_id": "fDgFGQYpCw", "prefixed_action_id": "al_fDgFGQYpCw", "action_id_screenshot": "screenshots/fDgFGQYpCw.png"}, {"name": "Tap on Text: \"AUCKLAND\"", "status": "passed", "duration": "3447ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "2349ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2251ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2764ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2189ms", "action_id": "M3dXqigqRv", "screenshot_filename": "M3dXqigqRv.png", "report_screenshot": "M3dXqigqRv.png", "resolved_screenshot": "screenshots/M3dXqigqRv.png", "clean_action_id": "M3dXqigqRv", "prefixed_action_id": "al_M3dXqigqRv", "action_id_screenshot": "screenshots/M3dXqigqRv.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2777ms", "action_id": "kDpsm2D3xt", "screenshot_filename": "kDpsm2D3xt.png", "report_screenshot": "kDpsm2D3xt.png", "resolved_screenshot": "screenshots/kDpsm2D3xt.png", "clean_action_id": "kDpsm2D3xt", "prefixed_action_id": "al_kDpsm2D3xt", "action_id_screenshot": "screenshots/kDpsm2D3xt.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            28 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3213ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4839ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1181ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1859ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-NZ-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2146ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2604ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2085ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2085ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "1950ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1220ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1937ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-NZ-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2020ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "1612ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2055ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2702ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2087ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3216ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2164ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2716ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1156ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-NZ-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2149ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2630ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2166ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5019ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod Sign in NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3215ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4888ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1148ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2660ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2445ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3017ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2456ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "2775ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1285ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2079ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible", "status": "passed", "duration": "4351ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2068ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1768ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3789ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1139ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1905ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "3197ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10021ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "1980ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1673ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1588ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1913ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "5708ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2083ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible", "status": "passed", "duration": "4958ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2078ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1776ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3781ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1158ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1946ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Google\"", "status": "passed", "duration": "3136ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2624ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2075ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible", "status": "passed", "duration": "4362ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2077ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            46 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3198ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5481ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1168ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1919ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-<PERSON>-Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3913ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2428ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1739ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1733ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2937ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2007ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2501ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2226ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1984ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "failed", "duration": "2107ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2061ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "2222ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "1695ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "unknown", "duration": "3591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "3187ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1345ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1953ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "unknown", "duration": "0ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"Move\"", "status": "unknown", "duration": "2732ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "unknown", "duration": "0ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2559ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "unknown", "duration": "1936ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "1976ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "2423ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Move to wishlist\"]\" is visible", "status": "unknown", "duration": "4445ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "1647ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "2782ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "2175ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "2467ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "3208ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2741ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "3204ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2930ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "1952ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "2605ms", "action_id": "b06OvcntcY", "screenshot_filename": "b06OvcntcY.png", "report_screenshot": "b06OvcntcY.png", "resolved_screenshot": "screenshots/b06OvcntcY.png", "clean_action_id": "b06OvcntcY", "prefixed_action_id": "al_b06OvcntcY", "action_id_screenshot": "screenshots/b06OvcntcY.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "2046ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 10%)", "status": "unknown", "duration": "2129ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3207ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "2094ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1177ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-<PERSON>-Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "1227ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1242ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2936ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "941ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3220ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "259ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "804ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "227ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "666ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "213ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "631ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "193ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "123ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "768ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5022ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3270ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2058ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2620ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2898ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "3228ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1809ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart nz\"", "status": "passed", "duration": "2281ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.co.nz\"]", "status": "failed", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "unknown", "duration": "724ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "unknown", "duration": "0ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "iOS Function: text - Text: \"notebook\"", "status": "unknown", "duration": "0ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "unknown", "duration": "0ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "unknown", "duration": "0ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "unknown", "duration": "681ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "NZ- Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3307ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3343ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "iOS Function: text - Text: \"P_43515028\"", "status": "passed", "duration": "2492ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2273ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "15017ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]", "status": "passed", "duration": "13740ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Done\"", "status": "passed", "duration": "34656ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3242ms", "action_id": "2mhi7GOrlS", "screenshot_filename": "2mhi7GOrlS.png", "report_screenshot": "2mhi7GOrlS.png", "resolved_screenshot": "screenshots/2mhi7GOrlS.png", "clean_action_id": "2mhi7GOrlS", "prefixed_action_id": "al_2mhi7GOrlS", "action_id_screenshot": "screenshots/2mhi7GOrlS.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2032ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4030ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "2381ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Execute Test Case: Click_Paginations (10 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3436ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "1989ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2773ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2794ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2848ms", "action_id": "eHvkAVake5", "screenshot_filename": "eHvkAVake5.png", "report_screenshot": "eHvkAVake5.png", "resolved_screenshot": "screenshots/eHvkAVake5.png", "clean_action_id": "eHvkAVake5", "prefixed_action_id": "al_eHvkAVake5", "action_id_screenshot": "screenshots/eHvkAVake5.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3771ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3607ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1950ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3749ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1164ms", "action_id": "b06OvcntcY", "screenshot_filename": "b06OvcntcY.png", "report_screenshot": "b06OvcntcY.png", "resolved_screenshot": "screenshots/b06OvcntcY.png", "clean_action_id": "b06OvcntcY", "prefixed_action_id": "al_b06OvcntcY", "action_id_screenshot": "screenshots/b06OvcntcY.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "3169ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3061ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2440ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "2834ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1304ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3908ms", "action_id": "fDgFGQYpCw", "screenshot_filename": "fDgFGQYpCw.png", "report_screenshot": "fDgFGQYpCw.png", "resolved_screenshot": "screenshots/fDgFGQYpCw.png", "clean_action_id": "fDgFGQYpCw", "prefixed_action_id": "al_fDgFGQYpCw", "action_id_screenshot": "screenshots/fDgFGQYpCw.png"}, {"name": "iOS Function: text - Text: \"enn[cooker-id]\"", "status": "passed", "duration": "3119ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1736ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2196ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2079ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2558ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2574ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "1864ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "1847ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1894ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1896ms", "action_id": "M3dXqigqRv", "screenshot_filename": "M3dXqigqRv.png", "report_screenshot": "M3dXqigqRv.png", "resolved_screenshot": "screenshots/M3dXqigqRv.png", "clean_action_id": "M3dXqigqRv", "prefixed_action_id": "al_M3dXqigqRv", "action_id_screenshot": "screenshots/M3dXqigqRv.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2755ms", "action_id": "kDpsm2D3xt", "screenshot_filename": "kDpsm2D3xt.png", "report_screenshot": "kDpsm2D3xt.png", "resolved_screenshot": "screenshots/kDpsm2D3xt.png", "clean_action_id": "kDpsm2D3xt", "prefixed_action_id": "al_kDpsm2D3xt", "action_id_screenshot": "screenshots/kDpsm2D3xt.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "2907ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}], "passed": 6, "failed": 3, "skipped": 0, "status": "failed"}