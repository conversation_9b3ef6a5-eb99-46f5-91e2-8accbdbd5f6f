Action Log - 2025-06-28 23:13:53
================================================================================

[[23:13:53]] [INFO] Generating execution report...
[[23:13:53]] [WARNING] 3 tests failed.
[[23:13:53]] [SUCCESS] Screenshot refreshed
[[23:13:53]] [INFO] Refreshing screenshot...
[[23:13:52]] [SUCCESS] Screenshot refreshed
[[23:13:52]] [INFO] Refreshing screenshot...
[[23:13:51]] [SUCCESS] Screenshot refreshed successfully
[[23:13:51]] [SUCCESS] Screenshot refreshed successfully
[[23:13:51]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[23:13:50]] [SUCCESS] Screenshot refreshed
[[23:13:50]] [INFO] Refreshing screenshot...
[[23:13:38]] [SUCCESS] Screenshot refreshed successfully
[[23:13:38]] [SUCCESS] Screenshot refreshed successfully
[[23:13:38]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[23:13:38]] [SUCCESS] Screenshot refreshed
[[23:13:38]] [INFO] Refreshing screenshot...
[[23:13:34]] [SUCCESS] Screenshot refreshed successfully
[[23:13:34]] [SUCCESS] Screenshot refreshed successfully
[[23:13:34]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[23:13:34]] [SUCCESS] Screenshot refreshed
[[23:13:34]] [INFO] Refreshing screenshot...
[[23:13:30]] [SUCCESS] Screenshot refreshed successfully
[[23:13:30]] [SUCCESS] Screenshot refreshed successfully
[[23:13:30]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[23:13:29]] [SUCCESS] Screenshot refreshed
[[23:13:29]] [INFO] Refreshing screenshot...
[[23:13:23]] [SUCCESS] Screenshot refreshed successfully
[[23:13:23]] [SUCCESS] Screenshot refreshed successfully
[[23:13:22]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[23:13:22]] [SUCCESS] Screenshot refreshed
[[23:13:22]] [INFO] Refreshing screenshot...
[[23:13:15]] [SUCCESS] Screenshot refreshed successfully
[[23:13:15]] [SUCCESS] Screenshot refreshed successfully
[[23:13:15]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[23:13:15]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[23:13:15]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[23:13:14]] [INFO] A2P2SB1bbN=running
[[23:13:14]] [INFO] Executing action 351/351: cleanupSteps action
[[23:13:14]] [SUCCESS] Screenshot refreshed
[[23:13:14]] [INFO] Refreshing screenshot...
[[23:13:14]] [INFO] arH1CZCPXh=pass
[[23:13:10]] [SUCCESS] Screenshot refreshed successfully
[[23:13:10]] [SUCCESS] Screenshot refreshed successfully
[[23:13:09]] [INFO] arH1CZCPXh=running
[[23:13:09]] [INFO] Executing action 350/351: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[23:13:09]] [SUCCESS] Screenshot refreshed
[[23:13:09]] [INFO] Refreshing screenshot...
[[23:13:09]] [INFO] RbD937Xbte=pass
[[23:13:04]] [SUCCESS] Screenshot refreshed successfully
[[23:13:04]] [SUCCESS] Screenshot refreshed successfully
[[23:13:04]] [INFO] RbD937Xbte=running
[[23:13:04]] [INFO] Executing action 349/351: Tap on Text: "out"
[[23:13:03]] [SUCCESS] Screenshot refreshed
[[23:13:03]] [INFO] Refreshing screenshot...
[[23:13:03]] [INFO] ylslyLAYKb=pass
[[23:13:00]] [SUCCESS] Screenshot refreshed successfully
[[23:13:00]] [SUCCESS] Screenshot refreshed successfully
[[23:13:00]] [INFO] ylslyLAYKb=running
[[23:13:00]] [INFO] Executing action 348/351: Swipe from (50%, 70%) to (50%, 30%)
[[23:12:59]] [SUCCESS] Screenshot refreshed
[[23:12:59]] [INFO] Refreshing screenshot...
[[23:12:59]] [INFO] wguGCt7OoB=pass
[[23:12:56]] [SUCCESS] Screenshot refreshed successfully
[[23:12:56]] [SUCCESS] Screenshot refreshed successfully
[[23:12:56]] [INFO] wguGCt7OoB=running
[[23:12:56]] [INFO] Executing action 347/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[23:12:55]] [SUCCESS] Screenshot refreshed
[[23:12:55]] [INFO] Refreshing screenshot...
[[23:12:55]] [INFO] RDQCFIxjA0=pass
[[23:12:52]] [SUCCESS] Screenshot refreshed successfully
[[23:12:52]] [SUCCESS] Screenshot refreshed successfully
[[23:12:52]] [INFO] RDQCFIxjA0=running
[[23:12:52]] [INFO] Executing action 346/351: Swipe from (90%, 20%) to (30%, 20%)
[[23:12:51]] [SUCCESS] Screenshot refreshed
[[23:12:51]] [INFO] Refreshing screenshot...
[[23:12:51]] [INFO] x4Mid4HQ0Z=pass
[[23:12:48]] [SUCCESS] Screenshot refreshed successfully
[[23:12:48]] [SUCCESS] Screenshot refreshed successfully
[[23:12:48]] [INFO] x4Mid4HQ0Z=running
[[23:12:48]] [INFO] Executing action 345/351: Swipe from (90%, 20%) to (30%, 20%)
[[23:12:47]] [SUCCESS] Screenshot refreshed
[[23:12:47]] [INFO] Refreshing screenshot...
[[23:12:47]] [INFO] OKCHAK6HCJ=pass
[[23:12:43]] [SUCCESS] Screenshot refreshed successfully
[[23:12:43]] [SUCCESS] Screenshot refreshed successfully
[[23:12:43]] [INFO] OKCHAK6HCJ=running
[[23:12:43]] [INFO] Executing action 344/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[23:12:42]] [SUCCESS] Screenshot refreshed
[[23:12:42]] [INFO] Refreshing screenshot...
[[23:12:42]] [INFO] Ef6OumM2eS=pass
[[23:12:38]] [SUCCESS] Screenshot refreshed successfully
[[23:12:38]] [SUCCESS] Screenshot refreshed successfully
[[23:12:38]] [INFO] Ef6OumM2eS=running
[[23:12:38]] [INFO] Executing action 343/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[23:12:37]] [SUCCESS] Screenshot refreshed
[[23:12:37]] [INFO] Refreshing screenshot...
[[23:12:37]] [INFO] 0bnBNoqPt8=pass
[[23:12:34]] [SUCCESS] Screenshot refreshed successfully
[[23:12:34]] [SUCCESS] Screenshot refreshed successfully
[[23:12:33]] [INFO] 0bnBNoqPt8=running
[[23:12:33]] [INFO] Executing action 342/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[23:12:33]] [SUCCESS] Screenshot refreshed
[[23:12:33]] [INFO] Refreshing screenshot...
[[23:12:33]] [INFO] xmelRkcdVx=pass
[[23:12:29]] [SUCCESS] Screenshot refreshed successfully
[[23:12:29]] [SUCCESS] Screenshot refreshed successfully
[[23:12:28]] [INFO] xmelRkcdVx=running
[[23:12:28]] [INFO] Executing action 341/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[23:12:28]] [SUCCESS] Screenshot refreshed
[[23:12:28]] [INFO] Refreshing screenshot...
[[23:12:28]] [INFO] ksCBjJiwHZ=pass
[[23:12:25]] [SUCCESS] Screenshot refreshed successfully
[[23:12:25]] [SUCCESS] Screenshot refreshed successfully
[[23:12:24]] [INFO] ksCBjJiwHZ=running
[[23:12:24]] [INFO] Executing action 340/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[23:12:24]] [SUCCESS] Screenshot refreshed
[[23:12:24]] [INFO] Refreshing screenshot...
[[23:12:24]] [INFO] RuPGkdCdah=pass
[[23:12:19]] [SUCCESS] Screenshot refreshed successfully
[[23:12:19]] [SUCCESS] Screenshot refreshed successfully
[[23:12:19]] [INFO] RuPGkdCdah=running
[[23:12:19]] [INFO] Executing action 339/351: iOS Function: text - Text: "enn[cooker-id]"
[[23:12:18]] [SUCCESS] Screenshot refreshed
[[23:12:18]] [INFO] Refreshing screenshot...
[[23:12:18]] [INFO] ewuLtuqVuo=pass
[[23:12:13]] [SUCCESS] Screenshot refreshed successfully
[[23:12:13]] [SUCCESS] Screenshot refreshed successfully
[[23:12:13]] [INFO] ewuLtuqVuo=running
[[23:12:13]] [INFO] Executing action 338/351: Tap on Text: "Find"
[[23:12:12]] [SUCCESS] Screenshot refreshed
[[23:12:12]] [INFO] Refreshing screenshot...
[[23:12:12]] [INFO] GTXmST3hEA=pass
[[23:12:09]] [SUCCESS] Screenshot refreshed successfully
[[23:12:09]] [SUCCESS] Screenshot refreshed successfully
[[23:12:09]] [INFO] GTXmST3hEA=running
[[23:12:09]] [INFO] Executing action 337/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[23:12:08]] [SUCCESS] Screenshot refreshed
[[23:12:08]] [INFO] Refreshing screenshot...
[[23:12:08]] [INFO] qkZ5KShdEU=pass
[[23:12:04]] [SUCCESS] Screenshot refreshed successfully
[[23:12:04]] [SUCCESS] Screenshot refreshed successfully
[[23:12:03]] [INFO] qkZ5KShdEU=running
[[23:12:03]] [INFO] Executing action 336/351: iOS Function: text - Text: "env[pwd]"
[[23:12:03]] [SUCCESS] Screenshot refreshed
[[23:12:03]] [INFO] Refreshing screenshot...
[[23:12:03]] [INFO] 7g2LmvjtEZ=pass
[[23:11:59]] [SUCCESS] Screenshot refreshed successfully
[[23:11:59]] [SUCCESS] Screenshot refreshed successfully
[[23:11:59]] [INFO] 7g2LmvjtEZ=running
[[23:11:59]] [INFO] Executing action 335/351: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[23:11:58]] [SUCCESS] Screenshot refreshed
[[23:11:58]] [INFO] Refreshing screenshot...
[[23:11:58]] [INFO] OUT2ASweb6=pass
[[23:11:54]] [SUCCESS] Screenshot refreshed successfully
[[23:11:54]] [SUCCESS] Screenshot refreshed successfully
[[23:11:54]] [INFO] OUT2ASweb6=running
[[23:11:54]] [INFO] Executing action 334/351: iOS Function: text - Text: "env[uname]"
[[23:11:53]] [SUCCESS] Screenshot refreshed
[[23:11:53]] [INFO] Refreshing screenshot...
[[23:11:53]] [INFO] TV4kJIIV9v=pass
[[23:11:48]] [SUCCESS] Screenshot refreshed successfully
[[23:11:48]] [SUCCESS] Screenshot refreshed successfully
[[23:11:48]] [INFO] TV4kJIIV9v=running
[[23:11:48]] [INFO] Executing action 333/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[23:11:48]] [SUCCESS] Screenshot refreshed
[[23:11:48]] [INFO] Refreshing screenshot...
[[23:11:48]] [INFO] kQJbqm7uCi=pass
[[23:11:46]] [SUCCESS] Screenshot refreshed successfully
[[23:11:46]] [SUCCESS] Screenshot refreshed successfully
[[23:11:45]] [INFO] kQJbqm7uCi=running
[[23:11:45]] [INFO] Executing action 332/351: iOS Function: alert_accept
[[23:11:45]] [SUCCESS] Screenshot refreshed
[[23:11:45]] [INFO] Refreshing screenshot...
[[23:11:45]] [INFO] SPE01N6pyp=pass
[[23:11:39]] [SUCCESS] Screenshot refreshed successfully
[[23:11:39]] [SUCCESS] Screenshot refreshed successfully
[[23:11:39]] [INFO] SPE01N6pyp=running
[[23:11:39]] [INFO] Executing action 331/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[23:11:38]] [SUCCESS] Screenshot refreshed
[[23:11:38]] [INFO] Refreshing screenshot...
[[23:11:38]] [INFO] WEB5St2Mb7=pass
[[23:11:34]] [SUCCESS] Screenshot refreshed successfully
[[23:11:34]] [SUCCESS] Screenshot refreshed successfully
[[23:11:34]] [INFO] WEB5St2Mb7=running
[[23:11:34]] [INFO] Executing action 330/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[23:11:34]] [SUCCESS] Screenshot refreshed
[[23:11:34]] [INFO] Refreshing screenshot...
[[23:11:34]] [INFO] To7bij5MnF=pass
[[23:11:28]] [INFO] To7bij5MnF=running
[[23:11:28]] [INFO] Executing action 329/351: Swipe from (5%, 50%) to (90%, 50%)
[[23:11:28]] [SUCCESS] Screenshot refreshed successfully
[[23:11:28]] [SUCCESS] Screenshot refreshed successfully
[[23:11:28]] [SUCCESS] Screenshot refreshed
[[23:11:28]] [INFO] Refreshing screenshot...
[[23:11:28]] [INFO] NkybTKfs2U=pass
[[23:11:23]] [SUCCESS] Screenshot refreshed successfully
[[23:11:23]] [SUCCESS] Screenshot refreshed successfully
[[23:11:23]] [INFO] NkybTKfs2U=running
[[23:11:23]] [INFO] Executing action 328/351: Swipe from (5%, 50%) to (90%, 50%)
[[23:11:22]] [SUCCESS] Screenshot refreshed
[[23:11:22]] [INFO] Refreshing screenshot...
[[23:11:22]] [INFO] dYEtjrv6lz=pass
[[23:11:17]] [SUCCESS] Screenshot refreshed successfully
[[23:11:17]] [SUCCESS] Screenshot refreshed successfully
[[23:11:17]] [INFO] dYEtjrv6lz=running
[[23:11:17]] [INFO] Executing action 327/351: Tap on Text: "Months"
[[23:11:17]] [SUCCESS] Screenshot refreshed
[[23:11:17]] [INFO] Refreshing screenshot...
[[23:11:17]] [INFO] eGQ7VrKUSo=pass
[[23:11:13]] [SUCCESS] Screenshot refreshed successfully
[[23:11:13]] [SUCCESS] Screenshot refreshed successfully
[[23:11:13]] [INFO] eGQ7VrKUSo=running
[[23:11:13]] [INFO] Executing action 326/351: Tap on Text: "Age"
[[23:11:12]] [SUCCESS] Screenshot refreshed
[[23:11:12]] [INFO] Refreshing screenshot...
[[23:11:12]] [INFO] zNRPvs2cC4=pass
[[23:11:08]] [SUCCESS] Screenshot refreshed successfully
[[23:11:08]] [SUCCESS] Screenshot refreshed successfully
[[23:11:08]] [INFO] zNRPvs2cC4=running
[[23:11:08]] [INFO] Executing action 325/351: Tap on Text: "Toys"
[[23:11:07]] [SUCCESS] Screenshot refreshed
[[23:11:07]] [INFO] Refreshing screenshot...
[[23:11:07]] [INFO] KyyS139agr=pass
[[23:11:04]] [SUCCESS] Screenshot refreshed successfully
[[23:11:04]] [SUCCESS] Screenshot refreshed successfully
[[23:11:04]] [INFO] KyyS139agr=running
[[23:11:04]] [INFO] Executing action 324/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[23:11:03]] [SUCCESS] Screenshot refreshed
[[23:11:03]] [INFO] Refreshing screenshot...
[[23:11:03]] [INFO] 5e4LeoW1YU=pass
[[23:10:59]] [SUCCESS] Screenshot refreshed successfully
[[23:10:59]] [SUCCESS] Screenshot refreshed successfully
[[23:10:58]] [INFO] 5e4LeoW1YU=running
[[23:10:58]] [INFO] Executing action 323/351: Restart app: env[appid]
[[23:10:58]] [SUCCESS] Screenshot refreshed
[[23:10:58]] [INFO] Refreshing screenshot...
[[23:10:57]] [SUCCESS] Screenshot refreshed
[[23:10:57]] [INFO] Refreshing screenshot...
[[23:10:40]] [SUCCESS] Screenshot refreshed successfully
[[23:10:40]] [SUCCESS] Screenshot refreshed successfully
[[23:10:39]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[23:10:39]] [SUCCESS] Screenshot refreshed
[[23:10:39]] [INFO] Refreshing screenshot...
[[23:09:55]] [SUCCESS] Screenshot refreshed successfully
[[23:09:55]] [SUCCESS] Screenshot refreshed successfully
[[23:09:55]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[23:09:54]] [SUCCESS] Screenshot refreshed
[[23:09:54]] [INFO] Refreshing screenshot...
[[23:09:38]] [SUCCESS] Screenshot refreshed successfully
[[23:09:38]] [SUCCESS] Screenshot refreshed successfully
[[23:09:37]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[23:09:37]] [SUCCESS] Screenshot refreshed
[[23:09:37]] [INFO] Refreshing screenshot...
[[23:08:53]] [SUCCESS] Screenshot refreshed successfully
[[23:08:53]] [SUCCESS] Screenshot refreshed successfully
[[23:08:52]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[23:08:52]] [SUCCESS] Screenshot refreshed
[[23:08:52]] [INFO] Refreshing screenshot...
[[23:08:36]] [SUCCESS] Screenshot refreshed successfully
[[23:08:36]] [SUCCESS] Screenshot refreshed successfully
[[23:08:36]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[23:08:35]] [SUCCESS] Screenshot refreshed
[[23:08:35]] [INFO] Refreshing screenshot...
[[23:07:51]] [SUCCESS] Screenshot refreshed successfully
[[23:07:51]] [SUCCESS] Screenshot refreshed successfully
[[23:07:51]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[23:07:51]] [SUCCESS] Screenshot refreshed
[[23:07:51]] [INFO] Refreshing screenshot...
[[23:07:35]] [SUCCESS] Screenshot refreshed successfully
[[23:07:35]] [SUCCESS] Screenshot refreshed successfully
[[23:07:34]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[23:07:34]] [SUCCESS] Screenshot refreshed
[[23:07:34]] [INFO] Refreshing screenshot...
[[23:06:49]] [SUCCESS] Screenshot refreshed successfully
[[23:06:49]] [SUCCESS] Screenshot refreshed successfully
[[23:06:49]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[23:06:49]] [SUCCESS] Screenshot refreshed
[[23:06:49]] [INFO] Refreshing screenshot...
[[23:06:32]] [SUCCESS] Screenshot refreshed successfully
[[23:06:32]] [SUCCESS] Screenshot refreshed successfully
[[23:06:32]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[23:06:32]] [SUCCESS] Screenshot refreshed
[[23:06:32]] [INFO] Refreshing screenshot...
[[23:05:45]] [SUCCESS] Screenshot refreshed successfully
[[23:05:45]] [SUCCESS] Screenshot refreshed successfully
[[23:05:45]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[23:05:45]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[23:05:45]] [INFO] Loading steps for multiStep action: Click_Paginations
[[23:05:45]] [INFO] aqs7O0Yq2p=running
[[23:05:45]] [INFO] Executing action 322/351: Execute Test Case: Click_Paginations (10 steps)
[[23:05:44]] [SUCCESS] Screenshot refreshed
[[23:05:44]] [INFO] Refreshing screenshot...
[[23:05:44]] [INFO] IL6kON0uQ9=pass
[[23:05:40]] [SUCCESS] Screenshot refreshed successfully
[[23:05:40]] [SUCCESS] Screenshot refreshed successfully
[[23:05:40]] [INFO] IL6kON0uQ9=running
[[23:05:40]] [INFO] Executing action 321/351: iOS Function: text - Text: "kids toys"
[[23:05:39]] [SUCCESS] Screenshot refreshed
[[23:05:39]] [INFO] Refreshing screenshot...
[[23:05:39]] [INFO] 6G6P3UE7Uy=pass
[[23:05:34]] [SUCCESS] Screenshot refreshed successfully
[[23:05:34]] [SUCCESS] Screenshot refreshed successfully
[[23:05:34]] [INFO] 6G6P3UE7Uy=running
[[23:05:34]] [INFO] Executing action 320/351: Tap on Text: "Find"
[[23:05:33]] [SUCCESS] Screenshot refreshed
[[23:05:33]] [INFO] Refreshing screenshot...
[[23:05:33]] [INFO] 7xs3GiydGF=pass
[[23:05:30]] [SUCCESS] Screenshot refreshed successfully
[[23:05:30]] [SUCCESS] Screenshot refreshed successfully
[[23:05:29]] [INFO] 7xs3GiydGF=running
[[23:05:29]] [INFO] Executing action 319/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[23:05:29]] [SUCCESS] Screenshot refreshed
[[23:05:29]] [INFO] Refreshing screenshot...
[[23:05:29]] [INFO] OR0SKKnFxy=pass
[[23:05:24]] [SUCCESS] Screenshot refreshed successfully
[[23:05:24]] [SUCCESS] Screenshot refreshed successfully
[[23:05:23]] [INFO] OR0SKKnFxy=running
[[23:05:23]] [INFO] Executing action 318/351: Restart app: env[appid]
[[23:05:23]] [SUCCESS] Screenshot refreshed
[[23:05:23]] [INFO] Refreshing screenshot...
[[23:05:23]] [INFO] Vy3WZ0LTJF=pass
[[23:04:46]] [SUCCESS] Screenshot refreshed successfully
[[23:04:46]] [SUCCESS] Screenshot refreshed successfully
[[23:04:46]] [INFO] Vy3WZ0LTJF=running
[[23:04:46]] [INFO] Executing action 317/351: Tap on Text: "Done"
[[23:04:45]] [SUCCESS] Screenshot refreshed
[[23:04:45]] [INFO] Refreshing screenshot...
[[23:04:45]] [INFO] iDtcdR3nSL=pass
[[23:04:30]] [SUCCESS] Screenshot refreshed successfully
[[23:04:30]] [SUCCESS] Screenshot refreshed successfully
[[23:04:30]] [INFO] iDtcdR3nSL=running
[[23:04:30]] [INFO] Executing action 316/351: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[23:04:29]] [SUCCESS] Screenshot refreshed
[[23:04:29]] [INFO] Refreshing screenshot...
[[23:04:29]] [INFO] bQrT7FZsxl=pass
[[23:04:13]] [SUCCESS] Screenshot refreshed successfully
[[23:04:13]] [SUCCESS] Screenshot refreshed successfully
[[23:04:12]] [INFO] bQrT7FZsxl=running
[[23:04:12]] [INFO] Executing action 315/351: Swipe from (50%, 70%) to (50%, 30%)
[[23:04:12]] [SUCCESS] Screenshot refreshed
[[23:04:12]] [INFO] Refreshing screenshot...
[[23:04:12]] [INFO] 9Jhn4eWZwR=pass
[[23:04:08]] [SUCCESS] Screenshot refreshed successfully
[[23:04:08]] [SUCCESS] Screenshot refreshed successfully
[[23:04:08]] [INFO] 9Jhn4eWZwR=running
[[23:04:08]] [INFO] Executing action 314/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[23:04:07]] [SUCCESS] Screenshot refreshed
[[23:04:07]] [INFO] Refreshing screenshot...
[[23:04:07]] [INFO] yNAxs8bgMy=pass
[[23:04:03]] [INFO] yNAxs8bgMy=running
[[23:04:03]] [INFO] Executing action 313/351: iOS Function: text - Text: "P_43515028"
[[23:04:03]] [SUCCESS] Screenshot refreshed successfully
[[23:04:03]] [SUCCESS] Screenshot refreshed successfully
[[23:04:03]] [SUCCESS] Screenshot refreshed
[[23:04:03]] [INFO] Refreshing screenshot...
[[23:04:03]] [INFO] 6G6P3UE7Uy=pass
[[23:03:58]] [SUCCESS] Screenshot refreshed successfully
[[23:03:58]] [SUCCESS] Screenshot refreshed successfully
[[23:03:58]] [INFO] 6G6P3UE7Uy=running
[[23:03:58]] [INFO] Executing action 312/351: Tap on Text: "Find"
[[23:03:57]] [SUCCESS] Screenshot refreshed
[[23:03:57]] [INFO] Refreshing screenshot...
[[23:03:57]] [INFO] OR0SKKnFxy=pass
[[23:03:43]] [SUCCESS] Screenshot refreshed successfully
[[23:03:43]] [SUCCESS] Screenshot refreshed successfully
[[23:03:42]] [INFO] OR0SKKnFxy=running
[[23:03:42]] [INFO] Executing action 311/351: Restart app: env[appid]
[[23:03:42]] [SUCCESS] Screenshot refreshed
[[23:03:42]] [INFO] Refreshing screenshot...
[[23:03:42]] [SUCCESS] Screenshot refreshed
[[23:03:42]] [INFO] Refreshing screenshot...
[[23:03:40]] [SUCCESS] Screenshot refreshed successfully
[[23:03:40]] [SUCCESS] Screenshot refreshed successfully
[[23:03:40]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[23:03:40]] [SUCCESS] Screenshot refreshed
[[23:03:40]] [INFO] Refreshing screenshot...
[[23:03:28]] [SUCCESS] Screenshot refreshed successfully
[[23:03:28]] [SUCCESS] Screenshot refreshed successfully
[[23:03:28]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[23:03:27]] [SUCCESS] Screenshot refreshed
[[23:03:27]] [INFO] Refreshing screenshot...
[[23:03:24]] [SUCCESS] Screenshot refreshed successfully
[[23:03:24]] [SUCCESS] Screenshot refreshed successfully
[[23:03:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[23:03:23]] [SUCCESS] Screenshot refreshed
[[23:03:23]] [INFO] Refreshing screenshot...
[[23:03:19]] [SUCCESS] Screenshot refreshed successfully
[[23:03:19]] [SUCCESS] Screenshot refreshed successfully
[[23:03:19]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[23:03:18]] [SUCCESS] Screenshot refreshed
[[23:03:18]] [INFO] Refreshing screenshot...
[[23:03:12]] [SUCCESS] Screenshot refreshed successfully
[[23:03:12]] [SUCCESS] Screenshot refreshed successfully
[[23:03:11]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[23:03:11]] [SUCCESS] Screenshot refreshed
[[23:03:11]] [INFO] Refreshing screenshot...
[[23:03:04]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[23:03:04]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[23:03:04]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[23:03:04]] [INFO] bUr5BliEHt=running
[[23:03:04]] [INFO] Executing action 310/351: cleanupSteps action
[[23:03:04]] [INFO] Skipping remaining steps in failed test case (moving from action 298 to 309), but preserving cleanup steps
[[23:03:04]] [INFO] fTdGMJ3NH3=fail
[[23:03:04]] [ERROR] Action 298 failed: Element not found or not tappable: xpath='//XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"]'
[[23:02:50]] [SUCCESS] Screenshot refreshed successfully
[[23:02:50]] [SUCCESS] Screenshot refreshed successfully
[[23:02:50]] [INFO] fTdGMJ3NH3=running
[[23:02:50]] [INFO] Executing action 298/351: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"]
[[23:02:50]] [SUCCESS] Screenshot refreshed
[[23:02:50]] [INFO] Refreshing screenshot...
[[23:02:50]] [INFO] ISpNHH3V9g=pass
[[23:02:46]] [INFO] ISpNHH3V9g=running
[[23:02:46]] [INFO] Executing action 297/351: iOS Function: text - Text: "kmart nz"
[[23:02:46]] [SUCCESS] Screenshot refreshed successfully
[[23:02:46]] [SUCCESS] Screenshot refreshed successfully
[[23:02:45]] [SUCCESS] Screenshot refreshed
[[23:02:45]] [INFO] Refreshing screenshot...
[[23:02:45]] [INFO] 0Q0fm6OTij=pass
[[23:02:42]] [SUCCESS] Screenshot refreshed successfully
[[23:02:42]] [SUCCESS] Screenshot refreshed successfully
[[23:02:42]] [INFO] 0Q0fm6OTij=running
[[23:02:42]] [INFO] Executing action 296/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[23:02:41]] [SUCCESS] Screenshot refreshed
[[23:02:41]] [INFO] Refreshing screenshot...
[[23:02:41]] [INFO] xVuuejtCFA=pass
[[23:02:37]] [SUCCESS] Screenshot refreshed successfully
[[23:02:37]] [SUCCESS] Screenshot refreshed successfully
[[23:02:36]] [INFO] xVuuejtCFA=running
[[23:02:36]] [INFO] Executing action 295/351: Restart app: com.apple.mobilesafari
[[23:02:36]] [SUCCESS] Screenshot refreshed
[[23:02:36]] [INFO] Refreshing screenshot...
[[23:02:36]] [INFO] LcYLwUffqj=pass
[[23:02:31]] [SUCCESS] Screenshot refreshed successfully
[[23:02:31]] [SUCCESS] Screenshot refreshed successfully
[[23:02:31]] [INFO] LcYLwUffqj=running
[[23:02:31]] [INFO] Executing action 294/351: Tap on Text: "out"
[[23:02:30]] [SUCCESS] Screenshot refreshed
[[23:02:30]] [INFO] Refreshing screenshot...
[[23:02:30]] [INFO] oqTdx3vL0d=pass
[[23:02:26]] [SUCCESS] Screenshot refreshed successfully
[[23:02:26]] [SUCCESS] Screenshot refreshed successfully
[[23:02:26]] [INFO] oqTdx3vL0d=running
[[23:02:26]] [INFO] Executing action 293/351: Swipe from (50%, 70%) to (50%, 30%)
[[23:02:26]] [SUCCESS] Screenshot refreshed
[[23:02:26]] [INFO] Refreshing screenshot...
[[23:02:26]] [INFO] UpUSVInizv=pass
[[23:02:23]] [SUCCESS] Screenshot refreshed successfully
[[23:02:23]] [SUCCESS] Screenshot refreshed successfully
[[23:02:22]] [INFO] UpUSVInizv=running
[[23:02:22]] [INFO] Executing action 292/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[23:02:22]] [SUCCESS] Screenshot refreshed
[[23:02:22]] [INFO] Refreshing screenshot...
[[23:02:22]] [INFO] hCCEvRtj1A=pass
[[23:02:16]] [INFO] hCCEvRtj1A=running
[[23:02:16]] [INFO] Executing action 291/351: Restart app: env[appid]
[[23:02:16]] [SUCCESS] Screenshot refreshed successfully
[[23:02:16]] [SUCCESS] Screenshot refreshed successfully
[[23:02:16]] [SUCCESS] Screenshot refreshed
[[23:02:16]] [INFO] Refreshing screenshot...
[[23:02:16]] [INFO] V42eHtTRYW=pass
[[23:02:09]] [INFO] V42eHtTRYW=running
[[23:02:09]] [INFO] Executing action 290/351: Wait for 5 ms
[[23:02:09]] [SUCCESS] Screenshot refreshed successfully
[[23:02:09]] [SUCCESS] Screenshot refreshed successfully
[[23:02:09]] [SUCCESS] Screenshot refreshed
[[23:02:09]] [INFO] Refreshing screenshot...
[[23:02:09]] [INFO] GRwHMVK4sA=pass
[[23:02:06]] [INFO] GRwHMVK4sA=running
[[23:02:06]] [INFO] Executing action 289/351: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[23:02:06]] [SUCCESS] Screenshot refreshed successfully
[[23:02:06]] [SUCCESS] Screenshot refreshed successfully
[[23:02:06]] [SUCCESS] Screenshot refreshed
[[23:02:06]] [INFO] Refreshing screenshot...
[[23:02:06]] [INFO] LfyQctrEJn=pass
[[23:02:04]] [SUCCESS] Screenshot refreshed successfully
[[23:02:04]] [SUCCESS] Screenshot refreshed successfully
[[23:02:04]] [INFO] LfyQctrEJn=running
[[23:02:04]] [INFO] Executing action 288/351: Launch app: com.apple.Preferences
[[23:02:04]] [SUCCESS] Screenshot refreshed
[[23:02:04]] [INFO] Refreshing screenshot...
[[23:02:04]] [INFO] seQcUKjkSU=pass
[[23:02:02]] [SUCCESS] Screenshot refreshed successfully
[[23:02:02]] [SUCCESS] Screenshot refreshed successfully
[[23:02:02]] [INFO] seQcUKjkSU=running
[[23:02:02]] [INFO] Executing action 287/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[23:02:01]] [SUCCESS] Screenshot refreshed
[[23:02:01]] [INFO] Refreshing screenshot...
[[23:02:01]] [INFO] UpUSVInizv=pass
[[23:01:59]] [SUCCESS] Screenshot refreshed successfully
[[23:01:59]] [SUCCESS] Screenshot refreshed successfully
[[23:01:59]] [INFO] UpUSVInizv=running
[[23:01:59]] [INFO] Executing action 286/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[23:01:59]] [SUCCESS] Screenshot refreshed
[[23:01:59]] [INFO] Refreshing screenshot...
[[23:01:59]] [INFO] WoymrHdtrO=pass
[[23:01:57]] [SUCCESS] Screenshot refreshed successfully
[[23:01:57]] [SUCCESS] Screenshot refreshed successfully
[[23:01:57]] [INFO] WoymrHdtrO=running
[[23:01:57]] [INFO] Executing action 285/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[23:01:56]] [SUCCESS] Screenshot refreshed
[[23:01:56]] [INFO] Refreshing screenshot...
[[23:01:56]] [INFO] 6xgrAWyfZ4=pass
[[23:01:54]] [SUCCESS] Screenshot refreshed successfully
[[23:01:54]] [SUCCESS] Screenshot refreshed successfully
[[23:01:54]] [INFO] 6xgrAWyfZ4=running
[[23:01:54]] [INFO] Executing action 284/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[23:01:54]] [SUCCESS] Screenshot refreshed
[[23:01:54]] [INFO] Refreshing screenshot...
[[23:01:54]] [INFO] eSr9EFlJek=pass
[[23:01:52]] [SUCCESS] Screenshot refreshed successfully
[[23:01:52]] [SUCCESS] Screenshot refreshed successfully
[[23:01:52]] [INFO] eSr9EFlJek=running
[[23:01:52]] [INFO] Executing action 283/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[23:01:51]] [SUCCESS] Screenshot refreshed
[[23:01:51]] [INFO] Refreshing screenshot...
[[23:01:51]] [INFO] 3KNqlNy6Bj=pass
[[23:01:49]] [SUCCESS] Screenshot refreshed successfully
[[23:01:49]] [SUCCESS] Screenshot refreshed successfully
[[23:01:49]] [INFO] 3KNqlNy6Bj=running
[[23:01:49]] [INFO] Executing action 282/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[23:01:48]] [SUCCESS] Screenshot refreshed
[[23:01:48]] [INFO] Refreshing screenshot...
[[23:01:48]] [INFO] cokvFXhj4c=pass
[[23:01:46]] [SUCCESS] Screenshot refreshed successfully
[[23:01:46]] [SUCCESS] Screenshot refreshed successfully
[[23:01:46]] [INFO] cokvFXhj4c=running
[[23:01:46]] [INFO] Executing action 281/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[23:01:46]] [SUCCESS] Screenshot refreshed
[[23:01:46]] [INFO] Refreshing screenshot...
[[23:01:46]] [INFO] oSQ8sPdVOJ=pass
[[23:01:41]] [INFO] oSQ8sPdVOJ=running
[[23:01:41]] [INFO] Executing action 280/351: Restart app: env[appid]
[[23:01:41]] [SUCCESS] Screenshot refreshed successfully
[[23:01:41]] [SUCCESS] Screenshot refreshed successfully
[[23:01:40]] [SUCCESS] Screenshot refreshed
[[23:01:40]] [INFO] Refreshing screenshot...
[[23:01:40]] [INFO] jUCAk6GJc4=pass
[[23:01:38]] [INFO] jUCAk6GJc4=running
[[23:01:38]] [INFO] Executing action 279/351: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[23:01:38]] [SUCCESS] Screenshot refreshed successfully
[[23:01:38]] [SUCCESS] Screenshot refreshed successfully
[[23:01:38]] [SUCCESS] Screenshot refreshed
[[23:01:38]] [INFO] Refreshing screenshot...
[[23:01:38]] [INFO] w1RV76df9x=pass
[[23:01:33]] [INFO] w1RV76df9x=running
[[23:01:33]] [INFO] Executing action 278/351: Tap on Text: "Wi-Fi"
[[23:01:33]] [SUCCESS] Screenshot refreshed successfully
[[23:01:33]] [SUCCESS] Screenshot refreshed successfully
[[23:01:33]] [SUCCESS] Screenshot refreshed
[[23:01:33]] [INFO] Refreshing screenshot...
[[23:01:33]] [INFO] LfyQctrEJn=pass
[[23:01:30]] [SUCCESS] Screenshot refreshed successfully
[[23:01:30]] [SUCCESS] Screenshot refreshed successfully
[[23:01:30]] [INFO] LfyQctrEJn=running
[[23:01:30]] [INFO] Executing action 277/351: Launch app: com.apple.Preferences
[[23:01:29]] [SUCCESS] Screenshot refreshed
[[23:01:29]] [INFO] Refreshing screenshot...
[[23:01:29]] [INFO] mIKA85kXaW=pass
[[23:01:28]] [SUCCESS] Screenshot refreshed successfully
[[23:01:28]] [SUCCESS] Screenshot refreshed successfully
[[23:01:26]] [INFO] mIKA85kXaW=running
[[23:01:26]] [INFO] Executing action 276/351: Terminate app: com.apple.Preferences
[[23:01:26]] [SUCCESS] Screenshot refreshed
[[23:01:26]] [INFO] Refreshing screenshot...
[[23:01:26]] [SUCCESS] Screenshot refreshed
[[23:01:26]] [INFO] Refreshing screenshot...
[[23:01:21]] [SUCCESS] Screenshot refreshed successfully
[[23:01:21]] [SUCCESS] Screenshot refreshed successfully
[[23:01:21]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[23:01:20]] [SUCCESS] Screenshot refreshed
[[23:01:20]] [INFO] Refreshing screenshot...
[[23:01:15]] [SUCCESS] Screenshot refreshed successfully
[[23:01:15]] [SUCCESS] Screenshot refreshed successfully
[[23:01:15]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[23:01:15]] [SUCCESS] Screenshot refreshed
[[23:01:15]] [INFO] Refreshing screenshot...
[[23:01:10]] [SUCCESS] Screenshot refreshed successfully
[[23:01:10]] [SUCCESS] Screenshot refreshed successfully
[[23:01:10]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[23:01:10]] [SUCCESS] Screenshot refreshed
[[23:01:10]] [INFO] Refreshing screenshot...
[[23:01:06]] [SUCCESS] Screenshot refreshed successfully
[[23:01:06]] [SUCCESS] Screenshot refreshed successfully
[[23:01:06]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[23:01:05]] [SUCCESS] Screenshot refreshed
[[23:01:05]] [INFO] Refreshing screenshot...
[[23:01:00]] [SUCCESS] Screenshot refreshed successfully
[[23:01:00]] [SUCCESS] Screenshot refreshed successfully
[[23:01:00]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[23:01:00]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[23:01:00]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[23:01:00]] [INFO] a12JYjIm66=running
[[23:01:00]] [INFO] Executing action 275/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[23:00:59]] [SUCCESS] Screenshot refreshed
[[23:00:59]] [INFO] Refreshing screenshot...
[[23:00:59]] [INFO] rJ86z4njuR=pass
[[23:00:57]] [SUCCESS] Screenshot refreshed successfully
[[23:00:57]] [SUCCESS] Screenshot refreshed successfully
[[23:00:56]] [INFO] rJ86z4njuR=running
[[23:00:56]] [INFO] Executing action 274/351: iOS Function: alert_accept
[[23:00:56]] [SUCCESS] Screenshot refreshed
[[23:00:56]] [INFO] Refreshing screenshot...
[[23:00:56]] [INFO] veukWo4573=pass
[[23:00:52]] [SUCCESS] Screenshot refreshed successfully
[[23:00:52]] [SUCCESS] Screenshot refreshed successfully
[[23:00:52]] [INFO] veukWo4573=running
[[23:00:52]] [INFO] Executing action 273/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[23:00:51]] [SUCCESS] Screenshot refreshed
[[23:00:51]] [INFO] Refreshing screenshot...
[[23:00:51]] [INFO] XEbZHdi0GT=pass
[[23:00:37]] [SUCCESS] Screenshot refreshed successfully
[[23:00:37]] [SUCCESS] Screenshot refreshed successfully
[[23:00:36]] [INFO] XEbZHdi0GT=running
[[23:00:36]] [INFO] Executing action 272/351: Restart app: env[appid]
[[23:00:36]] [SUCCESS] Screenshot refreshed
[[23:00:36]] [INFO] Refreshing screenshot...
[[23:00:35]] [SUCCESS] Screenshot refreshed
[[23:00:35]] [INFO] Refreshing screenshot...
[[23:00:34]] [SUCCESS] Screenshot refreshed successfully
[[23:00:34]] [SUCCESS] Screenshot refreshed successfully
[[23:00:33]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[23:00:33]] [SUCCESS] Screenshot refreshed
[[23:00:33]] [INFO] Refreshing screenshot...
[[23:00:28]] [SUCCESS] Screenshot refreshed successfully
[[23:00:28]] [SUCCESS] Screenshot refreshed successfully
[[23:00:28]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[23:00:27]] [SUCCESS] Screenshot refreshed
[[23:00:27]] [INFO] Refreshing screenshot...
[[23:00:24]] [SUCCESS] Screenshot refreshed successfully
[[23:00:24]] [SUCCESS] Screenshot refreshed successfully
[[23:00:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[23:00:23]] [SUCCESS] Screenshot refreshed
[[23:00:23]] [INFO] Refreshing screenshot...
[[23:00:19]] [SUCCESS] Screenshot refreshed successfully
[[23:00:19]] [SUCCESS] Screenshot refreshed successfully
[[23:00:19]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[23:00:18]] [SUCCESS] Screenshot refreshed
[[23:00:18]] [INFO] Refreshing screenshot...
[[23:00:12]] [SUCCESS] Screenshot refreshed successfully
[[23:00:12]] [SUCCESS] Screenshot refreshed successfully
[[23:00:11]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[23:00:11]] [SUCCESS] Screenshot refreshed
[[23:00:11]] [INFO] Refreshing screenshot...
[[23:00:04]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[23:00:04]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[23:00:04]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[23:00:04]] [INFO] 2L5CHzd7qs=running
[[23:00:04]] [INFO] Executing action 271/351: cleanupSteps action
[[23:00:04]] [INFO] Skipping remaining steps in failed test case (moving from action 241 to 270), but preserving cleanup steps
[[23:00:04]] [INFO] alaozIePOy=fail
[[23:00:04]] [ERROR] Action 241 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
[[22:59:49]] [SUCCESS] Screenshot refreshed successfully
[[22:59:49]] [SUCCESS] Screenshot refreshed successfully
[[22:59:48]] [INFO] alaozIePOy=running
[[22:59:48]] [INFO] Executing action 241/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[22:59:48]] [SUCCESS] Screenshot refreshed
[[22:59:48]] [INFO] Refreshing screenshot...
[[22:59:48]] [INFO] BzTvnSrykE=pass
[[22:59:44]] [SUCCESS] Screenshot refreshed successfully
[[22:59:44]] [SUCCESS] Screenshot refreshed successfully
[[22:59:43]] [INFO] BzTvnSrykE=running
[[22:59:43]] [INFO] Executing action 240/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[22:59:43]] [SUCCESS] Screenshot refreshed
[[22:59:43]] [INFO] Refreshing screenshot...
[[22:59:43]] [INFO] oWLIFhrzr1=pass
[[22:59:39]] [SUCCESS] Screenshot refreshed successfully
[[22:59:39]] [SUCCESS] Screenshot refreshed successfully
[[22:59:38]] [INFO] oWLIFhrzr1=running
[[22:59:38]] [INFO] Executing action 239/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]
[[22:59:38]] [SUCCESS] Screenshot refreshed
[[22:59:38]] [INFO] Refreshing screenshot...
[[22:59:38]] [INFO] zzd5ufNDih=pass
[[22:59:34]] [SUCCESS] Screenshot refreshed successfully
[[22:59:34]] [SUCCESS] Screenshot refreshed successfully
[[22:59:33]] [INFO] zzd5ufNDih=running
[[22:59:33]] [INFO] Executing action 238/351: Tap on image: env[device-back-img]
[[22:59:33]] [SUCCESS] Screenshot refreshed
[[22:59:33]] [INFO] Refreshing screenshot...
[[22:59:33]] [INFO] WbxRVpWtjw=pass
[[22:59:29]] [SUCCESS] Screenshot refreshed successfully
[[22:59:29]] [SUCCESS] Screenshot refreshed successfully
[[22:59:28]] [INFO] WbxRVpWtjw=running
[[22:59:28]] [INFO] Executing action 237/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[22:59:28]] [SUCCESS] Screenshot refreshed
[[22:59:28]] [INFO] Refreshing screenshot...
[[22:59:28]] [INFO] ITHvSyXXmu=pass
[[22:59:24]] [SUCCESS] Screenshot refreshed successfully
[[22:59:24]] [SUCCESS] Screenshot refreshed successfully
[[22:59:24]] [INFO] ITHvSyXXmu=running
[[22:59:24]] [INFO] Executing action 236/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[22:59:23]] [SUCCESS] Screenshot refreshed
[[22:59:23]] [INFO] Refreshing screenshot...
[[22:59:23]] [INFO] eLxHVWKeDQ=pass
[[22:59:19]] [SUCCESS] Screenshot refreshed successfully
[[22:59:19]] [SUCCESS] Screenshot refreshed successfully
[[22:59:19]] [INFO] eLxHVWKeDQ=running
[[22:59:19]] [INFO] Executing action 235/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[22:59:18]] [SUCCESS] Screenshot refreshed
[[22:59:18]] [INFO] Refreshing screenshot...
[[22:59:18]] [INFO] eLxHVWKeDQ=pass
[[22:59:15]] [SUCCESS] Screenshot refreshed successfully
[[22:59:15]] [SUCCESS] Screenshot refreshed successfully
[[22:59:14]] [INFO] eLxHVWKeDQ=running
[[22:59:14]] [INFO] Executing action 234/351: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[22:59:14]] [SUCCESS] Screenshot refreshed
[[22:59:14]] [INFO] Refreshing screenshot...
[[22:59:14]] [INFO] nAB6Q8LAdv=pass
[[22:59:10]] [SUCCESS] Screenshot refreshed successfully
[[22:59:10]] [SUCCESS] Screenshot refreshed successfully
[[22:59:10]] [INFO] nAB6Q8LAdv=running
[[22:59:10]] [INFO] Executing action 233/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[22:59:10]] [SUCCESS] Screenshot refreshed
[[22:59:10]] [INFO] Refreshing screenshot...
[[22:59:10]] [INFO] sc2KH9bG6H=pass
[[22:59:05]] [SUCCESS] Screenshot refreshed successfully
[[22:59:05]] [SUCCESS] Screenshot refreshed successfully
[[22:59:05]] [INFO] sc2KH9bG6H=running
[[22:59:05]] [INFO] Executing action 232/351: iOS Function: text - Text: "Uno card"
[[22:59:04]] [SUCCESS] Screenshot refreshed
[[22:59:04]] [INFO] Refreshing screenshot...
[[22:59:04]] [INFO] rqLJpAP0mA=pass
[[22:59:00]] [SUCCESS] Screenshot refreshed successfully
[[22:59:00]] [SUCCESS] Screenshot refreshed successfully
[[22:58:59]] [INFO] rqLJpAP0mA=running
[[22:58:59]] [INFO] Executing action 231/351: Tap on Text: "Find"
[[22:58:59]] [SUCCESS] Screenshot refreshed
[[22:58:59]] [INFO] Refreshing screenshot...
[[22:58:58]] [SUCCESS] Screenshot refreshed
[[22:58:58]] [INFO] Refreshing screenshot...
[[22:58:54]] [SUCCESS] Screenshot refreshed successfully
[[22:58:54]] [SUCCESS] Screenshot refreshed successfully
[[22:58:54]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:58:53]] [SUCCESS] Screenshot refreshed
[[22:58:53]] [INFO] Refreshing screenshot...
[[22:58:49]] [SUCCESS] Screenshot refreshed successfully
[[22:58:49]] [SUCCESS] Screenshot refreshed successfully
[[22:58:48]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:58:48]] [SUCCESS] Screenshot refreshed
[[22:58:48]] [INFO] Refreshing screenshot...
[[22:58:43]] [SUCCESS] Screenshot refreshed successfully
[[22:58:43]] [SUCCESS] Screenshot refreshed successfully
[[22:58:43]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:58:43]] [SUCCESS] Screenshot refreshed
[[22:58:43]] [INFO] Refreshing screenshot...
[[22:58:39]] [SUCCESS] Screenshot refreshed successfully
[[22:58:39]] [SUCCESS] Screenshot refreshed successfully
[[22:58:39]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:58:38]] [SUCCESS] Screenshot refreshed
[[22:58:38]] [INFO] Refreshing screenshot...
[[22:58:33]] [SUCCESS] Screenshot refreshed successfully
[[22:58:33]] [SUCCESS] Screenshot refreshed successfully
[[22:58:33]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:58:33]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:58:33]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:58:33]] [INFO] w8dueydByT=running
[[22:58:33]] [INFO] Executing action 230/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[22:58:32]] [SUCCESS] Screenshot refreshed
[[22:58:32]] [INFO] Refreshing screenshot...
[[22:58:32]] [INFO] 3caMBvQX7k=pass
[[22:58:29]] [SUCCESS] Screenshot refreshed successfully
[[22:58:29]] [SUCCESS] Screenshot refreshed successfully
[[22:58:29]] [INFO] 3caMBvQX7k=running
[[22:58:29]] [INFO] Executing action 229/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:58:28]] [SUCCESS] Screenshot refreshed
[[22:58:28]] [INFO] Refreshing screenshot...
[[22:58:28]] [INFO] yUJyVO5Wev=pass
[[22:58:26]] [SUCCESS] Screenshot refreshed successfully
[[22:58:26]] [SUCCESS] Screenshot refreshed successfully
[[22:58:25]] [INFO] yUJyVO5Wev=running
[[22:58:25]] [INFO] Executing action 228/351: iOS Function: alert_accept
[[22:58:25]] [SUCCESS] Screenshot refreshed
[[22:58:25]] [INFO] Refreshing screenshot...
[[22:58:25]] [INFO] rkL0oz4kiL=pass
[[22:58:18]] [SUCCESS] Screenshot refreshed successfully
[[22:58:18]] [SUCCESS] Screenshot refreshed successfully
[[22:58:17]] [INFO] rkL0oz4kiL=running
[[22:58:17]] [INFO] Executing action 227/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:58:17]] [SUCCESS] Screenshot refreshed
[[22:58:17]] [INFO] Refreshing screenshot...
[[22:58:17]] [INFO] HotUJOd6oB=pass
[[22:58:02]] [SUCCESS] Screenshot refreshed successfully
[[22:58:02]] [SUCCESS] Screenshot refreshed successfully
[[22:58:02]] [INFO] HotUJOd6oB=running
[[22:58:02]] [INFO] Executing action 226/351: Restart app: env[appid]
[[22:58:02]] [SUCCESS] Screenshot refreshed
[[22:58:02]] [INFO] Refreshing screenshot...
[[22:58:01]] [SUCCESS] Screenshot refreshed
[[22:58:01]] [INFO] Refreshing screenshot...
[[22:58:00]] [SUCCESS] Screenshot refreshed successfully
[[22:58:00]] [SUCCESS] Screenshot refreshed successfully
[[22:58:00]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:57:59]] [SUCCESS] Screenshot refreshed
[[22:57:59]] [INFO] Refreshing screenshot...
[[22:57:47]] [SUCCESS] Screenshot refreshed successfully
[[22:57:47]] [SUCCESS] Screenshot refreshed successfully
[[22:57:47]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:57:47]] [SUCCESS] Screenshot refreshed
[[22:57:47]] [INFO] Refreshing screenshot...
[[22:57:43]] [SUCCESS] Screenshot refreshed successfully
[[22:57:43]] [SUCCESS] Screenshot refreshed successfully
[[22:57:43]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:57:42]] [SUCCESS] Screenshot refreshed
[[22:57:42]] [INFO] Refreshing screenshot...
[[22:57:39]] [SUCCESS] Screenshot refreshed successfully
[[22:57:39]] [SUCCESS] Screenshot refreshed successfully
[[22:57:39]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:57:38]] [SUCCESS] Screenshot refreshed
[[22:57:38]] [INFO] Refreshing screenshot...
[[22:57:32]] [SUCCESS] Screenshot refreshed successfully
[[22:57:32]] [SUCCESS] Screenshot refreshed successfully
[[22:57:31]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:57:31]] [SUCCESS] Screenshot refreshed
[[22:57:31]] [INFO] Refreshing screenshot...
[[22:57:24]] [SUCCESS] Screenshot refreshed successfully
[[22:57:24]] [SUCCESS] Screenshot refreshed successfully
[[22:57:24]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:57:24]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:57:24]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:57:24]] [INFO] DcZCTgLVW9=running
[[22:57:24]] [INFO] Executing action 225/351: cleanupSteps action
[[22:57:23]] [SUCCESS] Screenshot refreshed
[[22:57:23]] [INFO] Refreshing screenshot...
[[22:57:23]] [INFO] w0CWlknXmX=pass
[[22:57:16]] [SUCCESS] Screenshot refreshed successfully
[[22:57:16]] [SUCCESS] Screenshot refreshed successfully
[[22:57:16]] [INFO] w0CWlknXmX=running
[[22:57:16]] [INFO] Executing action 224/351: Wait for 5 ms
[[22:57:16]] [SUCCESS] Screenshot refreshed
[[22:57:16]] [INFO] Refreshing screenshot...
[[22:57:16]] [INFO] Bdhe5AoUlM=pass
[[22:57:11]] [SUCCESS] Screenshot refreshed successfully
[[22:57:11]] [SUCCESS] Screenshot refreshed successfully
[[22:57:11]] [INFO] Bdhe5AoUlM=running
[[22:57:11]] [INFO] Executing action 223/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:57:11]] [SUCCESS] Screenshot refreshed
[[22:57:11]] [INFO] Refreshing screenshot...
[[22:57:11]] [INFO] ISfUFUnvFh=pass
[[22:57:05]] [SUCCESS] Screenshot refreshed successfully
[[22:57:05]] [SUCCESS] Screenshot refreshed successfully
[[22:57:05]] [INFO] ISfUFUnvFh=running
[[22:57:05]] [INFO] Executing action 222/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[22:57:04]] [SUCCESS] Screenshot refreshed
[[22:57:04]] [INFO] Refreshing screenshot...
[[22:57:04]] [INFO] FARWZvOj0x=pass
[[22:57:01]] [SUCCESS] Screenshot refreshed successfully
[[22:57:01]] [SUCCESS] Screenshot refreshed successfully
[[22:57:01]] [INFO] FARWZvOj0x=running
[[22:57:01]] [INFO] Executing action 221/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:57:00]] [SUCCESS] Screenshot refreshed
[[22:57:00]] [INFO] Refreshing screenshot...
[[22:57:00]] [INFO] bZCkx4U9Gk=pass
[[22:56:56]] [SUCCESS] Screenshot refreshed successfully
[[22:56:56]] [SUCCESS] Screenshot refreshed successfully
[[22:56:56]] [INFO] bZCkx4U9Gk=running
[[22:56:56]] [INFO] Executing action 220/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[22:56:55]] [SUCCESS] Screenshot refreshed
[[22:56:55]] [INFO] Refreshing screenshot...
[[22:56:55]] [INFO] vwFwkK6ydQ=pass
[[22:56:51]] [SUCCESS] Screenshot refreshed successfully
[[22:56:51]] [SUCCESS] Screenshot refreshed successfully
[[22:56:51]] [INFO] vwFwkK6ydQ=running
[[22:56:51]] [INFO] Executing action 219/351: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[22:56:50]] [SUCCESS] Screenshot refreshed
[[22:56:50]] [INFO] Refreshing screenshot...
[[22:56:50]] [INFO] xLGm9FefWE=pass
[[22:56:45]] [SUCCESS] Screenshot refreshed successfully
[[22:56:45]] [SUCCESS] Screenshot refreshed successfully
[[22:56:45]] [INFO] xLGm9FefWE=running
[[22:56:45]] [INFO] Executing action 218/351: Tap on Text: "Google"
[[22:56:44]] [SUCCESS] Screenshot refreshed
[[22:56:44]] [INFO] Refreshing screenshot...
[[22:56:44]] [INFO] SDtskxyVpg=pass
[[22:56:41]] [SUCCESS] Screenshot refreshed successfully
[[22:56:41]] [SUCCESS] Screenshot refreshed successfully
[[22:56:41]] [INFO] SDtskxyVpg=running
[[22:56:41]] [INFO] Executing action 217/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:56:40]] [SUCCESS] Screenshot refreshed
[[22:56:40]] [INFO] Refreshing screenshot...
[[22:56:40]] [INFO] 6HhScBaqQp=pass
[[22:56:38]] [SUCCESS] Screenshot refreshed successfully
[[22:56:38]] [SUCCESS] Screenshot refreshed successfully
[[22:56:37]] [INFO] 6HhScBaqQp=running
[[22:56:37]] [INFO] Executing action 216/351: iOS Function: alert_accept
[[22:56:37]] [SUCCESS] Screenshot refreshed
[[22:56:37]] [INFO] Refreshing screenshot...
[[22:56:37]] [INFO] quzlwPw42x=pass
[[22:56:31]] [SUCCESS] Screenshot refreshed successfully
[[22:56:31]] [SUCCESS] Screenshot refreshed successfully
[[22:56:31]] [INFO] quzlwPw42x=running
[[22:56:31]] [INFO] Executing action 215/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:56:30]] [SUCCESS] Screenshot refreshed
[[22:56:30]] [INFO] Refreshing screenshot...
[[22:56:30]] [INFO] jQYHQIvQ8l=pass
[[22:56:27]] [SUCCESS] Screenshot refreshed successfully
[[22:56:27]] [SUCCESS] Screenshot refreshed successfully
[[22:56:27]] [INFO] jQYHQIvQ8l=running
[[22:56:27]] [INFO] Executing action 214/351: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[22:56:26]] [SUCCESS] Screenshot refreshed
[[22:56:26]] [INFO] Refreshing screenshot...
[[22:56:26]] [INFO] ts3qyFxyMf=pass
[[22:56:22]] [SUCCESS] Screenshot refreshed successfully
[[22:56:22]] [SUCCESS] Screenshot refreshed successfully
[[22:56:22]] [INFO] ts3qyFxyMf=running
[[22:56:22]] [INFO] Executing action 213/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:56:22]] [SUCCESS] Screenshot refreshed
[[22:56:22]] [INFO] Refreshing screenshot...
[[22:56:22]] [INFO] uuatVQwQFW=pass
[[22:56:15]] [SUCCESS] Screenshot refreshed successfully
[[22:56:15]] [SUCCESS] Screenshot refreshed successfully
[[22:56:15]] [INFO] uuatVQwQFW=running
[[22:56:15]] [INFO] Executing action 212/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[22:56:14]] [SUCCESS] Screenshot refreshed
[[22:56:14]] [INFO] Refreshing screenshot...
[[22:56:14]] [INFO] CWkqGp5ndO=pass
[[22:56:11]] [SUCCESS] Screenshot refreshed successfully
[[22:56:11]] [SUCCESS] Screenshot refreshed successfully
[[22:56:11]] [INFO] CWkqGp5ndO=running
[[22:56:11]] [INFO] Executing action 211/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:56:10]] [SUCCESS] Screenshot refreshed
[[22:56:10]] [INFO] Refreshing screenshot...
[[22:56:10]] [INFO] KfMHchi8cx=pass
[[22:56:03]] [INFO] KfMHchi8cx=running
[[22:56:03]] [INFO] Executing action 210/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[22:56:02]] [SUCCESS] Screenshot refreshed successfully
[[22:56:02]] [SUCCESS] Screenshot refreshed successfully
[[22:56:02]] [SUCCESS] Screenshot refreshed
[[22:56:02]] [INFO] Refreshing screenshot...
[[22:56:02]] [INFO] zsVeGHiIgX=pass
[[22:55:59]] [INFO] zsVeGHiIgX=running
[[22:55:59]] [INFO] Executing action 209/351: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[22:55:59]] [SUCCESS] Screenshot refreshed successfully
[[22:55:59]] [SUCCESS] Screenshot refreshed successfully
[[22:55:58]] [SUCCESS] Screenshot refreshed
[[22:55:58]] [INFO] Refreshing screenshot...
[[22:55:58]] [INFO] 5nsUXQ5L7u=pass
[[22:55:55]] [INFO] 5nsUXQ5L7u=running
[[22:55:55]] [INFO] Executing action 208/351: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[22:55:55]] [SUCCESS] Screenshot refreshed successfully
[[22:55:55]] [SUCCESS] Screenshot refreshed successfully
[[22:55:55]] [SUCCESS] Screenshot refreshed
[[22:55:55]] [INFO] Refreshing screenshot...
[[22:55:55]] [INFO] iSckENpXrN=pass
[[22:55:52]] [INFO] iSckENpXrN=running
[[22:55:52]] [INFO] Executing action 207/351: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[22:55:52]] [SUCCESS] Screenshot refreshed successfully
[[22:55:52]] [SUCCESS] Screenshot refreshed successfully
[[22:55:51]] [SUCCESS] Screenshot refreshed
[[22:55:51]] [INFO] Refreshing screenshot...
[[22:55:51]] [INFO] J7BPGVnRJI=pass
[[22:55:48]] [INFO] J7BPGVnRJI=running
[[22:55:48]] [INFO] Executing action 206/351: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[22:55:48]] [SUCCESS] Screenshot refreshed successfully
[[22:55:48]] [SUCCESS] Screenshot refreshed successfully
[[22:55:48]] [SUCCESS] Screenshot refreshed
[[22:55:48]] [INFO] Refreshing screenshot...
[[22:55:48]] [INFO] 0pwZCYAtOv=pass
[[22:55:44]] [INFO] 0pwZCYAtOv=running
[[22:55:44]] [INFO] Executing action 205/351: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[22:55:44]] [SUCCESS] Screenshot refreshed successfully
[[22:55:44]] [SUCCESS] Screenshot refreshed successfully
[[22:55:44]] [SUCCESS] Screenshot refreshed
[[22:55:44]] [INFO] Refreshing screenshot...
[[22:55:44]] [INFO] soKM0KayFJ=pass
[[22:55:41]] [INFO] soKM0KayFJ=running
[[22:55:41]] [INFO] Executing action 204/351: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[22:55:41]] [SUCCESS] Screenshot refreshed successfully
[[22:55:41]] [SUCCESS] Screenshot refreshed successfully
[[22:55:40]] [SUCCESS] Screenshot refreshed
[[22:55:40]] [INFO] Refreshing screenshot...
[[22:55:40]] [INFO] hnH3ayslCh=pass
[[22:55:37]] [INFO] hnH3ayslCh=running
[[22:55:37]] [INFO] Executing action 203/351: Tap on Text: "Passcode"
[[22:55:37]] [SUCCESS] Screenshot refreshed successfully
[[22:55:37]] [SUCCESS] Screenshot refreshed successfully
[[22:55:36]] [SUCCESS] Screenshot refreshed
[[22:55:36]] [INFO] Refreshing screenshot...
[[22:55:36]] [INFO] CzVeOTdAX9=pass
[[22:55:25]] [SUCCESS] Screenshot refreshed successfully
[[22:55:25]] [SUCCESS] Screenshot refreshed successfully
[[22:55:25]] [INFO] CzVeOTdAX9=running
[[22:55:25]] [INFO] Executing action 202/351: Wait for 10 ms
[[22:55:24]] [SUCCESS] Screenshot refreshed
[[22:55:24]] [INFO] Refreshing screenshot...
[[22:55:24]] [INFO] NL2gtj6qIu=pass
[[22:55:20]] [SUCCESS] Screenshot refreshed successfully
[[22:55:20]] [SUCCESS] Screenshot refreshed successfully
[[22:55:20]] [INFO] NL2gtj6qIu=running
[[22:55:20]] [INFO] Executing action 201/351: Tap on Text: "Apple"
[[22:55:19]] [SUCCESS] Screenshot refreshed
[[22:55:19]] [INFO] Refreshing screenshot...
[[22:55:19]] [INFO] CJ88OgjKXp=pass
[[22:55:16]] [SUCCESS] Screenshot refreshed successfully
[[22:55:16]] [SUCCESS] Screenshot refreshed successfully
[[22:55:15]] [INFO] CJ88OgjKXp=running
[[22:55:15]] [INFO] Executing action 200/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:55:15]] [SUCCESS] Screenshot refreshed
[[22:55:15]] [INFO] Refreshing screenshot...
[[22:55:15]] [INFO] 2kwu2VBmuZ=pass
[[22:55:13]] [SUCCESS] Screenshot refreshed successfully
[[22:55:13]] [SUCCESS] Screenshot refreshed successfully
[[22:55:12]] [INFO] 2kwu2VBmuZ=running
[[22:55:12]] [INFO] Executing action 199/351: iOS Function: alert_accept
[[22:55:12]] [SUCCESS] Screenshot refreshed
[[22:55:12]] [INFO] Refreshing screenshot...
[[22:55:12]] [INFO] cJDpd7aK3d=pass
[[22:55:06]] [SUCCESS] Screenshot refreshed successfully
[[22:55:06]] [SUCCESS] Screenshot refreshed successfully
[[22:55:06]] [INFO] cJDpd7aK3d=running
[[22:55:06]] [INFO] Executing action 198/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:55:05]] [SUCCESS] Screenshot refreshed
[[22:55:05]] [INFO] Refreshing screenshot...
[[22:55:05]] [INFO] FlEukNkjlS=pass
[[22:55:02]] [SUCCESS] Screenshot refreshed successfully
[[22:55:02]] [SUCCESS] Screenshot refreshed successfully
[[22:55:02]] [INFO] FlEukNkjlS=running
[[22:55:02]] [INFO] Executing action 197/351: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[22:55:01]] [SUCCESS] Screenshot refreshed
[[22:55:01]] [INFO] Refreshing screenshot...
[[22:55:01]] [INFO] LlRfimKPrn=pass
[[22:54:57]] [SUCCESS] Screenshot refreshed successfully
[[22:54:57]] [SUCCESS] Screenshot refreshed successfully
[[22:54:57]] [INFO] LlRfimKPrn=running
[[22:54:57]] [INFO] Executing action 196/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:54:56]] [SUCCESS] Screenshot refreshed
[[22:54:56]] [INFO] Refreshing screenshot...
[[22:54:56]] [INFO] xqHGFj3tDd=pass
[[22:54:51]] [SUCCESS] Screenshot refreshed successfully
[[22:54:51]] [SUCCESS] Screenshot refreshed successfully
[[22:54:51]] [INFO] xqHGFj3tDd=running
[[22:54:51]] [INFO] Executing action 195/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[22:54:50]] [SUCCESS] Screenshot refreshed
[[22:54:50]] [INFO] Refreshing screenshot...
[[22:54:50]] [INFO] 08NzsvhQXK=pass
[[22:54:47]] [SUCCESS] Screenshot refreshed successfully
[[22:54:47]] [SUCCESS] Screenshot refreshed successfully
[[22:54:46]] [INFO] 08NzsvhQXK=running
[[22:54:46]] [INFO] Executing action 194/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:54:46]] [SUCCESS] Screenshot refreshed
[[22:54:46]] [INFO] Refreshing screenshot...
[[22:54:46]] [INFO] IsGWxLFpIn=pass
[[22:54:43]] [SUCCESS] Screenshot refreshed successfully
[[22:54:43]] [SUCCESS] Screenshot refreshed successfully
[[22:54:43]] [INFO] IsGWxLFpIn=running
[[22:54:43]] [INFO] Executing action 193/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[22:54:42]] [SUCCESS] Screenshot refreshed
[[22:54:42]] [INFO] Refreshing screenshot...
[[22:54:42]] [INFO] CtWhaVwbJC=pass
[[22:54:37]] [SUCCESS] Screenshot refreshed successfully
[[22:54:37]] [SUCCESS] Screenshot refreshed successfully
[[22:54:37]] [INFO] CtWhaVwbJC=running
[[22:54:37]] [INFO] Executing action 192/351: iOS Function: text - Text: "env[pwd]"
[[22:54:37]] [SUCCESS] Screenshot refreshed
[[22:54:37]] [INFO] Refreshing screenshot...
[[22:54:37]] [INFO] I5bRbYY1hD=pass
[[22:54:33]] [SUCCESS] Screenshot refreshed successfully
[[22:54:33]] [SUCCESS] Screenshot refreshed successfully
[[22:54:33]] [INFO] I5bRbYY1hD=running
[[22:54:33]] [INFO] Executing action 191/351: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:54:32]] [SUCCESS] Screenshot refreshed
[[22:54:32]] [INFO] Refreshing screenshot...
[[22:54:32]] [INFO] rSxM47lUdy=pass
[[22:54:28]] [SUCCESS] Screenshot refreshed successfully
[[22:54:28]] [SUCCESS] Screenshot refreshed successfully
[[22:54:27]] [INFO] rSxM47lUdy=running
[[22:54:27]] [INFO] Executing action 190/351: iOS Function: text - Text: "env[uname]"
[[22:54:27]] [SUCCESS] Screenshot refreshed
[[22:54:27]] [INFO] Refreshing screenshot...
[[22:54:27]] [INFO] 8OsQmoVYqW=pass
[[22:54:23]] [SUCCESS] Screenshot refreshed successfully
[[22:54:23]] [SUCCESS] Screenshot refreshed successfully
[[22:54:23]] [INFO] 8OsQmoVYqW=running
[[22:54:23]] [INFO] Executing action 189/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:54:22]] [SUCCESS] Screenshot refreshed
[[22:54:22]] [INFO] Refreshing screenshot...
[[22:54:22]] [INFO] ImienLpJEN=pass
[[22:54:18]] [SUCCESS] Screenshot refreshed successfully
[[22:54:18]] [SUCCESS] Screenshot refreshed successfully
[[22:54:18]] [INFO] ImienLpJEN=running
[[22:54:18]] [INFO] Executing action 188/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:54:18]] [SUCCESS] Screenshot refreshed
[[22:54:18]] [INFO] Refreshing screenshot...
[[22:54:18]] [INFO] q4hPXCBtx4=pass
[[22:54:15]] [SUCCESS] Screenshot refreshed successfully
[[22:54:15]] [SUCCESS] Screenshot refreshed successfully
[[22:54:15]] [INFO] q4hPXCBtx4=running
[[22:54:15]] [INFO] Executing action 187/351: iOS Function: alert_accept
[[22:54:14]] [SUCCESS] Screenshot refreshed
[[22:54:14]] [INFO] Refreshing screenshot...
[[22:54:14]] [INFO] 2cTZvK1psn=pass
[[22:54:08]] [SUCCESS] Screenshot refreshed successfully
[[22:54:08]] [SUCCESS] Screenshot refreshed successfully
[[22:54:07]] [INFO] 2cTZvK1psn=running
[[22:54:07]] [INFO] Executing action 186/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:54:07]] [SUCCESS] Screenshot refreshed
[[22:54:07]] [INFO] Refreshing screenshot...
[[22:54:07]] [INFO] Vxt7QOYeDD=pass
[[22:53:52]] [SUCCESS] Screenshot refreshed successfully
[[22:53:52]] [SUCCESS] Screenshot refreshed successfully
[[22:53:52]] [INFO] Vxt7QOYeDD=running
[[22:53:52]] [INFO] Executing action 185/351: Restart app: env[appid]
[[22:53:52]] [SUCCESS] Screenshot refreshed
[[22:53:52]] [INFO] Refreshing screenshot...
[[22:53:51]] [SUCCESS] Screenshot refreshed
[[22:53:51]] [INFO] Refreshing screenshot...
[[22:53:50]] [SUCCESS] Screenshot refreshed successfully
[[22:53:50]] [SUCCESS] Screenshot refreshed successfully
[[22:53:49]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:53:49]] [SUCCESS] Screenshot refreshed
[[22:53:49]] [INFO] Refreshing screenshot...
[[22:53:37]] [SUCCESS] Screenshot refreshed successfully
[[22:53:37]] [SUCCESS] Screenshot refreshed successfully
[[22:53:37]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:53:36]] [SUCCESS] Screenshot refreshed
[[22:53:36]] [INFO] Refreshing screenshot...
[[22:53:33]] [SUCCESS] Screenshot refreshed successfully
[[22:53:33]] [SUCCESS] Screenshot refreshed successfully
[[22:53:33]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:53:32]] [SUCCESS] Screenshot refreshed
[[22:53:32]] [INFO] Refreshing screenshot...
[[22:53:29]] [SUCCESS] Screenshot refreshed successfully
[[22:53:29]] [SUCCESS] Screenshot refreshed successfully
[[22:53:28]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:53:28]] [SUCCESS] Screenshot refreshed
[[22:53:28]] [INFO] Refreshing screenshot...
[[22:53:21]] [SUCCESS] Screenshot refreshed successfully
[[22:53:21]] [SUCCESS] Screenshot refreshed successfully
[[22:53:21]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:53:20]] [SUCCESS] Screenshot refreshed
[[22:53:20]] [INFO] Refreshing screenshot...
[[22:53:14]] [SUCCESS] Screenshot refreshed successfully
[[22:53:14]] [SUCCESS] Screenshot refreshed successfully
[[22:53:13]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:53:13]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:53:13]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:53:13]] [INFO] DsDODm7uZx=running
[[22:53:13]] [INFO] Executing action 184/351: cleanupSteps action
[[22:53:13]] [SUCCESS] Screenshot refreshed
[[22:53:13]] [INFO] Refreshing screenshot...
[[22:53:13]] [INFO] Vyrkv4wK1v=pass
[[22:53:06]] [SUCCESS] Screenshot refreshed successfully
[[22:53:06]] [SUCCESS] Screenshot refreshed successfully
[[22:53:06]] [INFO] Vyrkv4wK1v=running
[[22:53:06]] [INFO] Executing action 183/351: Wait for 5 ms
[[22:53:05]] [SUCCESS] Screenshot refreshed
[[22:53:05]] [INFO] Refreshing screenshot...
[[22:53:05]] [INFO] 7WYExJTqjp=pass
[[22:53:01]] [SUCCESS] Screenshot refreshed successfully
[[22:53:01]] [SUCCESS] Screenshot refreshed successfully
[[22:53:01]] [INFO] 7WYExJTqjp=running
[[22:53:01]] [INFO] Executing action 182/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:53:00]] [SUCCESS] Screenshot refreshed
[[22:53:00]] [INFO] Refreshing screenshot...
[[22:53:00]] [INFO] NQGIFb5O7u=pass
[[22:52:56]] [SUCCESS] Screenshot refreshed successfully
[[22:52:56]] [SUCCESS] Screenshot refreshed successfully
[[22:52:56]] [INFO] NQGIFb5O7u=running
[[22:52:56]] [INFO] Executing action 181/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:52:56]] [SUCCESS] Screenshot refreshed
[[22:52:56]] [INFO] Refreshing screenshot...
[[22:52:56]] [INFO] NurQsFoMkE=pass
[[22:52:53]] [SUCCESS] Screenshot refreshed successfully
[[22:52:53]] [SUCCESS] Screenshot refreshed successfully
[[22:52:52]] [INFO] NurQsFoMkE=running
[[22:52:52]] [INFO] Executing action 180/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:52:52]] [SUCCESS] Screenshot refreshed
[[22:52:52]] [INFO] Refreshing screenshot...
[[22:52:51]] [SUCCESS] Screenshot refreshed
[[22:52:51]] [INFO] Refreshing screenshot...
[[22:52:47]] [SUCCESS] Screenshot refreshed successfully
[[22:52:47]] [SUCCESS] Screenshot refreshed successfully
[[22:52:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:52:46]] [SUCCESS] Screenshot refreshed
[[22:52:46]] [INFO] Refreshing screenshot...
[[22:52:43]] [SUCCESS] Screenshot refreshed successfully
[[22:52:43]] [SUCCESS] Screenshot refreshed successfully
[[22:52:42]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:52:42]] [SUCCESS] Screenshot refreshed
[[22:52:42]] [INFO] Refreshing screenshot...
[[22:52:37]] [SUCCESS] Screenshot refreshed successfully
[[22:52:37]] [SUCCESS] Screenshot refreshed successfully
[[22:52:37]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:52:37]] [SUCCESS] Screenshot refreshed
[[22:52:37]] [INFO] Refreshing screenshot...
[[22:52:33]] [SUCCESS] Screenshot refreshed successfully
[[22:52:33]] [SUCCESS] Screenshot refreshed successfully
[[22:52:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:52:32]] [SUCCESS] Screenshot refreshed
[[22:52:32]] [INFO] Refreshing screenshot...
[[22:52:27]] [SUCCESS] Screenshot refreshed successfully
[[22:52:27]] [SUCCESS] Screenshot refreshed successfully
[[22:52:27]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:52:27]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:52:27]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:52:27]] [INFO] 6imys82Dy0=running
[[22:52:27]] [INFO] Executing action 179/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[22:52:26]] [SUCCESS] Screenshot refreshed
[[22:52:26]] [INFO] Refreshing screenshot...
[[22:52:26]] [INFO] XuLgjNG74w=pass
[[22:52:24]] [SUCCESS] Screenshot refreshed successfully
[[22:52:24]] [SUCCESS] Screenshot refreshed successfully
[[22:52:23]] [INFO] XuLgjNG74w=running
[[22:52:23]] [INFO] Executing action 178/351: iOS Function: alert_accept
[[22:52:23]] [SUCCESS] Screenshot refreshed
[[22:52:23]] [INFO] Refreshing screenshot...
[[22:52:23]] [INFO] L6wTorOX8B=pass
[[22:52:19]] [SUCCESS] Screenshot refreshed successfully
[[22:52:19]] [SUCCESS] Screenshot refreshed successfully
[[22:52:19]] [INFO] L6wTorOX8B=running
[[22:52:19]] [INFO] Executing action 177/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[22:52:18]] [SUCCESS] Screenshot refreshed
[[22:52:18]] [INFO] Refreshing screenshot...
[[22:52:18]] [INFO] ydRnBBO1vR=pass
[[22:52:15]] [SUCCESS] Screenshot refreshed successfully
[[22:52:15]] [SUCCESS] Screenshot refreshed successfully
[[22:52:14]] [INFO] ydRnBBO1vR=running
[[22:52:14]] [INFO] Executing action 176/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:52:14]] [SUCCESS] Screenshot refreshed
[[22:52:14]] [INFO] Refreshing screenshot...
[[22:52:14]] [INFO] lCSewtjn1z=pass
[[22:52:09]] [SUCCESS] Screenshot refreshed successfully
[[22:52:09]] [SUCCESS] Screenshot refreshed successfully
[[22:52:09]] [INFO] lCSewtjn1z=running
[[22:52:09]] [INFO] Executing action 175/351: Restart app: env[appid]
[[22:52:08]] [SUCCESS] Screenshot refreshed
[[22:52:08]] [INFO] Refreshing screenshot...
[[22:52:08]] [INFO] A1Wz7p1iVG=pass
[[22:52:04]] [SUCCESS] Screenshot refreshed successfully
[[22:52:04]] [SUCCESS] Screenshot refreshed successfully
[[22:52:04]] [INFO] A1Wz7p1iVG=running
[[22:52:04]] [INFO] Executing action 174/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:52:03]] [SUCCESS] Screenshot refreshed
[[22:52:03]] [INFO] Refreshing screenshot...
[[22:52:03]] [INFO] BCM1sS8SGA=pass
[[22:51:59]] [SUCCESS] Screenshot refreshed successfully
[[22:51:59]] [SUCCESS] Screenshot refreshed successfully
[[22:51:59]] [INFO] BCM1sS8SGA=running
[[22:51:59]] [INFO] Executing action 173/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:51:59]] [SUCCESS] Screenshot refreshed
[[22:51:59]] [INFO] Refreshing screenshot...
[[22:51:59]] [INFO] ydRnBBO1vR=pass
[[22:51:55]] [SUCCESS] Screenshot refreshed successfully
[[22:51:55]] [SUCCESS] Screenshot refreshed successfully
[[22:51:55]] [INFO] ydRnBBO1vR=running
[[22:51:55]] [INFO] Executing action 172/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:51:54]] [SUCCESS] Screenshot refreshed
[[22:51:54]] [INFO] Refreshing screenshot...
[[22:51:54]] [INFO] quZwUwj3a8=pass
[[22:51:51]] [SUCCESS] Screenshot refreshed successfully
[[22:51:51]] [SUCCESS] Screenshot refreshed successfully
[[22:51:51]] [INFO] quZwUwj3a8=running
[[22:51:51]] [INFO] Executing action 171/351: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[22:51:50]] [SUCCESS] Screenshot refreshed
[[22:51:50]] [INFO] Refreshing screenshot...
[[22:51:50]] [INFO] FHRlQXe58T=pass
[[22:51:47]] [SUCCESS] Screenshot refreshed successfully
[[22:51:47]] [SUCCESS] Screenshot refreshed successfully
[[22:51:46]] [INFO] FHRlQXe58T=running
[[22:51:46]] [INFO] Executing action 170/351: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[22:51:46]] [SUCCESS] Screenshot refreshed
[[22:51:46]] [INFO] Refreshing screenshot...
[[22:51:45]] [SUCCESS] Screenshot refreshed
[[22:51:45]] [INFO] Refreshing screenshot...
[[22:51:41]] [SUCCESS] Screenshot refreshed successfully
[[22:51:41]] [SUCCESS] Screenshot refreshed successfully
[[22:51:41]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:51:40]] [SUCCESS] Screenshot refreshed
[[22:51:40]] [INFO] Refreshing screenshot...
[[22:51:36]] [SUCCESS] Screenshot refreshed successfully
[[22:51:36]] [SUCCESS] Screenshot refreshed successfully
[[22:51:36]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:51:36]] [SUCCESS] Screenshot refreshed
[[22:51:36]] [INFO] Refreshing screenshot...
[[22:51:31]] [SUCCESS] Screenshot refreshed successfully
[[22:51:31]] [SUCCESS] Screenshot refreshed successfully
[[22:51:31]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:51:31]] [SUCCESS] Screenshot refreshed
[[22:51:31]] [INFO] Refreshing screenshot...
[[22:51:27]] [SUCCESS] Screenshot refreshed successfully
[[22:51:27]] [SUCCESS] Screenshot refreshed successfully
[[22:51:27]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:51:26]] [SUCCESS] Screenshot refreshed
[[22:51:26]] [INFO] Refreshing screenshot...
[[22:51:21]] [SUCCESS] Screenshot refreshed successfully
[[22:51:21]] [SUCCESS] Screenshot refreshed successfully
[[22:51:21]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:51:21]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:51:21]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:51:21]] [INFO] 3KXtlKCIes=running
[[22:51:21]] [INFO] Executing action 169/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[22:51:20]] [SUCCESS] Screenshot refreshed
[[22:51:20]] [INFO] Refreshing screenshot...
[[22:51:20]] [INFO] 6mHVWI3j5e=pass
[[22:51:16]] [SUCCESS] Screenshot refreshed successfully
[[22:51:16]] [SUCCESS] Screenshot refreshed successfully
[[22:51:16]] [INFO] 6mHVWI3j5e=running
[[22:51:16]] [INFO] Executing action 168/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:51:16]] [SUCCESS] Screenshot refreshed
[[22:51:16]] [INFO] Refreshing screenshot...
[[22:51:16]] [INFO] rJVGLpLWM3=pass
[[22:51:13]] [SUCCESS] Screenshot refreshed successfully
[[22:51:13]] [SUCCESS] Screenshot refreshed successfully
[[22:51:13]] [INFO] rJVGLpLWM3=running
[[22:51:13]] [INFO] Executing action 167/351: iOS Function: alert_accept
[[22:51:12]] [SUCCESS] Screenshot refreshed
[[22:51:12]] [INFO] Refreshing screenshot...
[[22:51:12]] [INFO] WlISsMf9QA=pass
[[22:51:09]] [SUCCESS] Screenshot refreshed successfully
[[22:51:09]] [SUCCESS] Screenshot refreshed successfully
[[22:51:09]] [INFO] WlISsMf9QA=running
[[22:51:09]] [INFO] Executing action 166/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[22:51:08]] [SUCCESS] Screenshot refreshed
[[22:51:08]] [INFO] Refreshing screenshot...
[[22:51:08]] [INFO] IvqPpScAJa=pass
[[22:51:05]] [SUCCESS] Screenshot refreshed successfully
[[22:51:05]] [SUCCESS] Screenshot refreshed successfully
[[22:51:05]] [INFO] IvqPpScAJa=running
[[22:51:05]] [INFO] Executing action 165/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[22:51:04]] [SUCCESS] Screenshot refreshed
[[22:51:04]] [INFO] Refreshing screenshot...
[[22:51:04]] [INFO] bGo3feCwBQ=pass
[[22:51:00]] [SUCCESS] Screenshot refreshed successfully
[[22:51:00]] [SUCCESS] Screenshot refreshed successfully
[[22:51:00]] [INFO] bGo3feCwBQ=running
[[22:51:00]] [INFO] Executing action 164/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:50:59]] [SUCCESS] Screenshot refreshed
[[22:50:59]] [INFO] Refreshing screenshot...
[[22:50:59]] [INFO] GHH3xhNGgr=pass
[[22:50:55]] [SUCCESS] Screenshot refreshed successfully
[[22:50:55]] [SUCCESS] Screenshot refreshed successfully
[[22:50:55]] [INFO] GHH3xhNGgr=running
[[22:50:55]] [INFO] Executing action 163/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:50:55]] [SUCCESS] Screenshot refreshed
[[22:50:55]] [INFO] Refreshing screenshot...
[[22:50:55]] [INFO] F0gZF1jEnT=pass
[[22:50:53]] [SUCCESS] Screenshot refreshed successfully
[[22:50:53]] [SUCCESS] Screenshot refreshed successfully
[[22:50:51]] [INFO] F0gZF1jEnT=running
[[22:50:51]] [INFO] Executing action 162/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:50:51]] [SUCCESS] Screenshot refreshed
[[22:50:51]] [INFO] Refreshing screenshot...
[[22:50:51]] [SUCCESS] Screenshot refreshed
[[22:50:51]] [INFO] Refreshing screenshot...
[[22:50:45]] [SUCCESS] Screenshot refreshed successfully
[[22:50:45]] [SUCCESS] Screenshot refreshed successfully
[[22:50:45]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:50:45]] [SUCCESS] Screenshot refreshed
[[22:50:45]] [INFO] Refreshing screenshot...
[[22:50:41]] [SUCCESS] Screenshot refreshed successfully
[[22:50:41]] [SUCCESS] Screenshot refreshed successfully
[[22:50:41]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:50:40]] [SUCCESS] Screenshot refreshed
[[22:50:40]] [INFO] Refreshing screenshot...
[[22:50:36]] [SUCCESS] Screenshot refreshed successfully
[[22:50:36]] [SUCCESS] Screenshot refreshed successfully
[[22:50:36]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:50:35]] [SUCCESS] Screenshot refreshed
[[22:50:35]] [INFO] Refreshing screenshot...
[[22:50:31]] [SUCCESS] Screenshot refreshed successfully
[[22:50:31]] [SUCCESS] Screenshot refreshed successfully
[[22:50:31]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:50:31]] [SUCCESS] Screenshot refreshed
[[22:50:31]] [INFO] Refreshing screenshot...
[[22:50:25]] [SUCCESS] Screenshot refreshed successfully
[[22:50:25]] [SUCCESS] Screenshot refreshed successfully
[[22:50:25]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:50:25]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:50:25]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:50:25]] [INFO] f71lbL6wHw=running
[[22:50:25]] [INFO] Executing action 161/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[22:50:25]] [SUCCESS] Screenshot refreshed
[[22:50:25]] [INFO] Refreshing screenshot...
[[22:50:25]] [INFO] eJnHS9n9VL=pass
[[22:50:21]] [SUCCESS] Screenshot refreshed successfully
[[22:50:21]] [SUCCESS] Screenshot refreshed successfully
[[22:50:21]] [INFO] eJnHS9n9VL=running
[[22:50:21]] [INFO] Executing action 160/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:50:21]] [SUCCESS] Screenshot refreshed
[[22:50:21]] [INFO] Refreshing screenshot...
[[22:50:21]] [INFO] XuLgjNG74w=pass
[[22:50:18]] [SUCCESS] Screenshot refreshed successfully
[[22:50:18]] [SUCCESS] Screenshot refreshed successfully
[[22:50:18]] [INFO] XuLgjNG74w=running
[[22:50:18]] [INFO] Executing action 159/351: iOS Function: alert_accept
[[22:50:17]] [SUCCESS] Screenshot refreshed
[[22:50:17]] [INFO] Refreshing screenshot...
[[22:50:17]] [INFO] qA1ap4n1m4=pass
[[22:50:11]] [SUCCESS] Screenshot refreshed successfully
[[22:50:11]] [SUCCESS] Screenshot refreshed successfully
[[22:50:10]] [INFO] qA1ap4n1m4=running
[[22:50:10]] [INFO] Executing action 158/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:50:10]] [SUCCESS] Screenshot refreshed
[[22:50:10]] [INFO] Refreshing screenshot...
[[22:50:10]] [INFO] JXFxYCr98V=pass
[[22:49:55]] [SUCCESS] Screenshot refreshed successfully
[[22:49:55]] [SUCCESS] Screenshot refreshed successfully
[[22:49:55]] [INFO] JXFxYCr98V=running
[[22:49:55]] [INFO] Executing action 157/351: Restart app: env[appid]
[[22:49:55]] [SUCCESS] Screenshot refreshed
[[22:49:55]] [INFO] Refreshing screenshot...
[[22:49:54]] [SUCCESS] Screenshot refreshed
[[22:49:54]] [INFO] Refreshing screenshot...
[[22:49:53]] [SUCCESS] Screenshot refreshed successfully
[[22:49:53]] [SUCCESS] Screenshot refreshed successfully
[[22:49:53]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:49:52]] [SUCCESS] Screenshot refreshed
[[22:49:52]] [INFO] Refreshing screenshot...
[[22:49:40]] [SUCCESS] Screenshot refreshed successfully
[[22:49:40]] [SUCCESS] Screenshot refreshed successfully
[[22:49:40]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:49:40]] [SUCCESS] Screenshot refreshed
[[22:49:40]] [INFO] Refreshing screenshot...
[[22:49:36]] [SUCCESS] Screenshot refreshed successfully
[[22:49:36]] [SUCCESS] Screenshot refreshed successfully
[[22:49:36]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:49:36]] [SUCCESS] Screenshot refreshed
[[22:49:36]] [INFO] Refreshing screenshot...
[[22:49:32]] [SUCCESS] Screenshot refreshed successfully
[[22:49:32]] [SUCCESS] Screenshot refreshed successfully
[[22:49:32]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:49:31]] [SUCCESS] Screenshot refreshed
[[22:49:31]] [INFO] Refreshing screenshot...
[[22:49:25]] [SUCCESS] Screenshot refreshed successfully
[[22:49:25]] [SUCCESS] Screenshot refreshed successfully
[[22:49:24]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:49:24]] [SUCCESS] Screenshot refreshed
[[22:49:24]] [INFO] Refreshing screenshot...
[[22:49:17]] [SUCCESS] Screenshot refreshed successfully
[[22:49:17]] [SUCCESS] Screenshot refreshed successfully
[[22:49:17]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:49:17]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:49:17]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:49:17]] [INFO] jSY75QGPr8=running
[[22:49:17]] [INFO] Executing action 156/351: cleanupSteps action
[[22:49:16]] [SUCCESS] Screenshot refreshed
[[22:49:16]] [INFO] Refreshing screenshot...
[[22:49:16]] [INFO] BracBsfa3Y=pass
[[22:49:11]] [SUCCESS] Screenshot refreshed successfully
[[22:49:11]] [SUCCESS] Screenshot refreshed successfully
[[22:49:11]] [INFO] BracBsfa3Y=running
[[22:49:11]] [INFO] Executing action 155/351: Tap on Text: "out"
[[22:49:11]] [SUCCESS] Screenshot refreshed
[[22:49:11]] [INFO] Refreshing screenshot...
[[22:49:11]] [INFO] P2OkZzbCB3=pass
[[22:49:07]] [SUCCESS] Screenshot refreshed successfully
[[22:49:07]] [SUCCESS] Screenshot refreshed successfully
[[22:49:07]] [INFO] P2OkZzbCB3=running
[[22:49:07]] [INFO] Executing action 154/351: Tap on image: env[device-back-img]
[[22:49:06]] [SUCCESS] Screenshot refreshed
[[22:49:06]] [INFO] Refreshing screenshot...
[[22:49:06]] [INFO] BracBsfa3Y=pass
[[22:49:02]] [SUCCESS] Screenshot refreshed successfully
[[22:49:02]] [SUCCESS] Screenshot refreshed successfully
[[22:49:02]] [INFO] BracBsfa3Y=running
[[22:49:02]] [INFO] Executing action 153/351: Tap on Text: "Customer"
[[22:49:02]] [SUCCESS] Screenshot refreshed
[[22:49:02]] [INFO] Refreshing screenshot...
[[22:49:02]] [INFO] q70JSbqKNk=pass
[[22:48:58]] [SUCCESS] Screenshot refreshed successfully
[[22:48:58]] [SUCCESS] Screenshot refreshed successfully
[[22:48:58]] [INFO] q70JSbqKNk=running
[[22:48:58]] [INFO] Executing action 152/351: Tap on image: env[device-back-img]
[[22:48:57]] [SUCCESS] Screenshot refreshed
[[22:48:57]] [INFO] Refreshing screenshot...
[[22:48:57]] [INFO] YuuQe2KupX=pass
[[22:48:53]] [SUCCESS] Screenshot refreshed successfully
[[22:48:53]] [SUCCESS] Screenshot refreshed successfully
[[22:48:53]] [INFO] YuuQe2KupX=running
[[22:48:53]] [INFO] Executing action 151/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[22:48:53]] [SUCCESS] Screenshot refreshed
[[22:48:53]] [INFO] Refreshing screenshot...
[[22:48:53]] [INFO] nVWzLauG8N=pass
[[22:48:48]] [SUCCESS] Screenshot refreshed successfully
[[22:48:48]] [SUCCESS] Screenshot refreshed successfully
[[22:48:48]] [INFO] nVWzLauG8N=running
[[22:48:48]] [INFO] Executing action 150/351: Tap on Text: "AUCKLAND"
[[22:48:47]] [SUCCESS] Screenshot refreshed
[[22:48:47]] [INFO] Refreshing screenshot...
[[22:48:47]] [INFO] 3CTsyFe28F=pass
[[22:48:40]] [SUCCESS] Screenshot refreshed successfully
[[22:48:40]] [SUCCESS] Screenshot refreshed successfully
[[22:48:40]] [INFO] 3CTsyFe28F=running
[[22:48:40]] [INFO] Executing action 149/351: Tap and Type at (29, 262): "0616"
[[22:48:40]] [SUCCESS] Screenshot refreshed
[[22:48:40]] [INFO] Refreshing screenshot...
[[22:48:40]] [INFO] 2FnAZDskt1=pass
[[22:48:18]] [SUCCESS] Screenshot refreshed successfully
[[22:48:18]] [SUCCESS] Screenshot refreshed successfully
[[22:48:18]] [INFO] 2FnAZDskt1=running
[[22:48:18]] [INFO] Executing action 148/351: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"]
[[22:48:18]] [SUCCESS] Screenshot refreshed
[[22:48:18]] [INFO] Refreshing screenshot...
[[22:48:18]] [INFO] BracBsfa3Y=pass
[[22:48:13]] [SUCCESS] Screenshot refreshed successfully
[[22:48:13]] [SUCCESS] Screenshot refreshed successfully
[[22:48:13]] [INFO] BracBsfa3Y=running
[[22:48:13]] [INFO] Executing action 147/351: Tap on Text: "Nearby"
[[22:48:12]] [SUCCESS] Screenshot refreshed
[[22:48:12]] [INFO] Refreshing screenshot...
[[22:48:12]] [INFO] BracBsfa3Y=pass
[[22:48:08]] [SUCCESS] Screenshot refreshed successfully
[[22:48:08]] [SUCCESS] Screenshot refreshed successfully
[[22:48:08]] [INFO] BracBsfa3Y=running
[[22:48:08]] [INFO] Executing action 146/351: Tap on Text: "locator"
[[22:48:07]] [SUCCESS] Screenshot refreshed
[[22:48:07]] [INFO] Refreshing screenshot...
[[22:48:07]] [INFO] EJkHvEQccu=pass
[[22:48:03]] [SUCCESS] Screenshot refreshed successfully
[[22:48:03]] [SUCCESS] Screenshot refreshed successfully
[[22:48:03]] [INFO] EJkHvEQccu=running
[[22:48:03]] [INFO] Executing action 145/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:48:02]] [SUCCESS] Screenshot refreshed
[[22:48:02]] [INFO] Refreshing screenshot...
[[22:48:02]] [INFO] oETU9DzLi4=pass
[[22:47:59]] [SUCCESS] Screenshot refreshed successfully
[[22:47:59]] [SUCCESS] Screenshot refreshed successfully
[[22:47:59]] [INFO] oETU9DzLi4=running
[[22:47:59]] [INFO] Executing action 144/351: Tap on image: env[device-back-img]
[[22:47:58]] [SUCCESS] Screenshot refreshed
[[22:47:58]] [INFO] Refreshing screenshot...
[[22:47:58]] [INFO] M1IXnYddFx=pass
[[22:47:54]] [SUCCESS] Screenshot refreshed successfully
[[22:47:54]] [SUCCESS] Screenshot refreshed successfully
[[22:47:54]] [INFO] M1IXnYddFx=running
[[22:47:54]] [INFO] Executing action 143/351: Tap on image: env[device-back-img]
[[22:47:54]] [SUCCESS] Screenshot refreshed
[[22:47:54]] [INFO] Refreshing screenshot...
[[22:47:54]] [INFO] napKDohf3Z=pass
[[22:47:50]] [SUCCESS] Screenshot refreshed successfully
[[22:47:50]] [SUCCESS] Screenshot refreshed successfully
[[22:47:50]] [INFO] napKDohf3Z=running
[[22:47:50]] [INFO] Executing action 142/351: Tap on Text: "payment"
[[22:47:49]] [SUCCESS] Screenshot refreshed
[[22:47:49]] [INFO] Refreshing screenshot...
[[22:47:49]] [INFO] 9MqlsILCgk=pass
[[22:47:45]] [SUCCESS] Screenshot refreshed successfully
[[22:47:45]] [SUCCESS] Screenshot refreshed successfully
[[22:47:45]] [INFO] 9MqlsILCgk=running
[[22:47:45]] [INFO] Executing action 141/351: Tap on image: env[device-back-img]
[[22:47:45]] [SUCCESS] Screenshot refreshed
[[22:47:45]] [INFO] Refreshing screenshot...
[[22:47:45]] [INFO] 20qUCJgpE9=pass
[[22:47:40]] [SUCCESS] Screenshot refreshed successfully
[[22:47:40]] [SUCCESS] Screenshot refreshed successfully
[[22:47:40]] [INFO] 20qUCJgpE9=running
[[22:47:40]] [INFO] Executing action 140/351: Tap on Text: "address"
[[22:47:40]] [SUCCESS] Screenshot refreshed
[[22:47:40]] [INFO] Refreshing screenshot...
[[22:47:40]] [INFO] WwIZzJEW9W=pass
[[22:47:36]] [SUCCESS] Screenshot refreshed successfully
[[22:47:36]] [SUCCESS] Screenshot refreshed successfully
[[22:47:36]] [INFO] WwIZzJEW9W=running
[[22:47:36]] [INFO] Executing action 139/351: Tap on image: env[device-back-img]
[[22:47:36]] [SUCCESS] Screenshot refreshed
[[22:47:36]] [INFO] Refreshing screenshot...
[[22:47:36]] [INFO] 3hOTINBVMf=pass
[[22:47:31]] [SUCCESS] Screenshot refreshed successfully
[[22:47:31]] [SUCCESS] Screenshot refreshed successfully
[[22:47:31]] [INFO] 3hOTINBVMf=running
[[22:47:31]] [INFO] Executing action 138/351: Tap on Text: "details"
[[22:47:31]] [SUCCESS] Screenshot refreshed
[[22:47:31]] [INFO] Refreshing screenshot...
[[22:47:31]] [INFO] V59u3l1wkM=pass
[[22:47:27]] [SUCCESS] Screenshot refreshed successfully
[[22:47:27]] [SUCCESS] Screenshot refreshed successfully
[[22:47:27]] [INFO] V59u3l1wkM=running
[[22:47:27]] [INFO] Executing action 137/351: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[22:47:27]] [SUCCESS] Screenshot refreshed
[[22:47:27]] [INFO] Refreshing screenshot...
[[22:47:27]] [INFO] PbfHAtFQPP=pass
[[22:47:24]] [SUCCESS] Screenshot refreshed successfully
[[22:47:24]] [SUCCESS] Screenshot refreshed successfully
[[22:47:23]] [INFO] PbfHAtFQPP=running
[[22:47:23]] [INFO] Executing action 136/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:47:23]] [SUCCESS] Screenshot refreshed
[[22:47:23]] [INFO] Refreshing screenshot...
[[22:47:23]] [INFO] 6qZnk86hGg=pass
[[22:47:18]] [SUCCESS] Screenshot refreshed successfully
[[22:47:18]] [SUCCESS] Screenshot refreshed successfully
[[22:47:18]] [INFO] 6qZnk86hGg=running
[[22:47:18]] [INFO] Executing action 135/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[22:47:17]] [SUCCESS] Screenshot refreshed
[[22:47:17]] [INFO] Refreshing screenshot...
[[22:47:17]] [INFO] FAvQgIuHc1=pass
[[22:47:13]] [SUCCESS] Screenshot refreshed successfully
[[22:47:13]] [SUCCESS] Screenshot refreshed successfully
[[22:47:13]] [INFO] FAvQgIuHc1=running
[[22:47:13]] [INFO] Executing action 134/351: Tap on Text: "Return"
[[22:47:12]] [SUCCESS] Screenshot refreshed
[[22:47:12]] [INFO] Refreshing screenshot...
[[22:47:12]] [INFO] EJkHvEQccu=pass
[[22:47:08]] [SUCCESS] Screenshot refreshed successfully
[[22:47:08]] [SUCCESS] Screenshot refreshed successfully
[[22:47:07]] [INFO] EJkHvEQccu=running
[[22:47:07]] [INFO] Executing action 133/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:47:07]] [SUCCESS] Screenshot refreshed
[[22:47:07]] [INFO] Refreshing screenshot...
[[22:47:07]] [INFO] YuuQe2KupX=pass
[[22:47:03]] [INFO] YuuQe2KupX=running
[[22:47:03]] [INFO] Executing action 132/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[22:47:03]] [SUCCESS] Screenshot refreshed successfully
[[22:47:03]] [SUCCESS] Screenshot refreshed successfully
[[22:47:02]] [SUCCESS] Screenshot refreshed
[[22:47:02]] [INFO] Refreshing screenshot...
[[22:47:02]] [INFO] g0PE7Mofye=pass
[[22:46:57]] [SUCCESS] Screenshot refreshed successfully
[[22:46:57]] [SUCCESS] Screenshot refreshed successfully
[[22:46:57]] [INFO] g0PE7Mofye=running
[[22:46:57]] [INFO] Executing action 131/351: Tap on element with accessibility_id: Print order details
[[22:46:57]] [SUCCESS] Screenshot refreshed
[[22:46:57]] [INFO] Refreshing screenshot...
[[22:46:57]] [INFO] GgQaBLWYkb=pass
[[22:46:53]] [SUCCESS] Screenshot refreshed successfully
[[22:46:53]] [SUCCESS] Screenshot refreshed successfully
[[22:46:53]] [INFO] GgQaBLWYkb=running
[[22:46:53]] [INFO] Executing action 130/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[22:46:52]] [SUCCESS] Screenshot refreshed
[[22:46:52]] [INFO] Refreshing screenshot...
[[22:46:52]] [INFO] 0962MtId5t=pass
[[22:46:45]] [SUCCESS] Screenshot refreshed successfully
[[22:46:45]] [SUCCESS] Screenshot refreshed successfully
[[22:46:45]] [INFO] 0962MtId5t=running
[[22:46:45]] [INFO] Executing action 129/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[22:46:45]] [SUCCESS] Screenshot refreshed
[[22:46:45]] [INFO] Refreshing screenshot...
[[22:46:45]] [INFO] 7g6MFJSGIO=pass
[[22:46:41]] [SUCCESS] Screenshot refreshed successfully
[[22:46:41]] [SUCCESS] Screenshot refreshed successfully
[[22:46:41]] [INFO] 7g6MFJSGIO=running
[[22:46:41]] [INFO] Executing action 128/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4]
[[22:46:41]] [SUCCESS] Screenshot refreshed
[[22:46:41]] [INFO] Refreshing screenshot...
[[22:46:41]] [INFO] Z6g3sGuHTp=pass
[[22:46:34]] [SUCCESS] Screenshot refreshed successfully
[[22:46:34]] [SUCCESS] Screenshot refreshed successfully
[[22:46:34]] [INFO] Z6g3sGuHTp=running
[[22:46:34]] [INFO] Executing action 127/351: Wait for 5 ms
[[22:46:33]] [SUCCESS] Screenshot refreshed
[[22:46:33]] [INFO] Refreshing screenshot...
[[22:46:33]] [INFO] pFlYwTS53v=pass
[[22:46:30]] [SUCCESS] Screenshot refreshed successfully
[[22:46:30]] [SUCCESS] Screenshot refreshed successfully
[[22:46:30]] [INFO] pFlYwTS53v=running
[[22:46:30]] [INFO] Executing action 126/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"]
[[22:46:29]] [SUCCESS] Screenshot refreshed
[[22:46:29]] [INFO] Refreshing screenshot...
[[22:46:29]] [INFO] fDgFGQYpCw=pass
[[22:46:26]] [SUCCESS] Screenshot refreshed successfully
[[22:46:26]] [SUCCESS] Screenshot refreshed successfully
[[22:46:26]] [INFO] fDgFGQYpCw=running
[[22:46:26]] [INFO] Executing action 125/351: Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"]
[[22:46:26]] [SUCCESS] Screenshot refreshed
[[22:46:26]] [INFO] Refreshing screenshot...
[[22:46:26]] [INFO] V59u3l1wkM=pass
[[22:46:22]] [SUCCESS] Screenshot refreshed successfully
[[22:46:22]] [SUCCESS] Screenshot refreshed successfully
[[22:46:22]] [INFO] V59u3l1wkM=running
[[22:46:22]] [INFO] Executing action 124/351: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[22:46:22]] [SUCCESS] Screenshot refreshed
[[22:46:22]] [INFO] Refreshing screenshot...
[[22:46:22]] [INFO] sl3Wk1gK8X=pass
[[22:46:18]] [SUCCESS] Screenshot refreshed successfully
[[22:46:18]] [SUCCESS] Screenshot refreshed successfully
[[22:46:16]] [INFO] sl3Wk1gK8X=running
[[22:46:16]] [INFO] Executing action 123/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:46:16]] [SUCCESS] Screenshot refreshed
[[22:46:16]] [INFO] Refreshing screenshot...
[[22:46:16]] [SUCCESS] Screenshot refreshed
[[22:46:16]] [INFO] Refreshing screenshot...
[[22:46:11]] [SUCCESS] Screenshot refreshed successfully
[[22:46:11]] [SUCCESS] Screenshot refreshed successfully
[[22:46:11]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:46:10]] [SUCCESS] Screenshot refreshed
[[22:46:10]] [INFO] Refreshing screenshot...
[[22:46:06]] [SUCCESS] Screenshot refreshed successfully
[[22:46:06]] [SUCCESS] Screenshot refreshed successfully
[[22:46:06]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:46:06]] [SUCCESS] Screenshot refreshed
[[22:46:06]] [INFO] Refreshing screenshot...
[[22:46:01]] [SUCCESS] Screenshot refreshed successfully
[[22:46:01]] [SUCCESS] Screenshot refreshed successfully
[[22:46:01]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:46:01]] [SUCCESS] Screenshot refreshed
[[22:46:01]] [INFO] Refreshing screenshot...
[[22:45:57]] [SUCCESS] Screenshot refreshed successfully
[[22:45:57]] [SUCCESS] Screenshot refreshed successfully
[[22:45:57]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:45:56]] [SUCCESS] Screenshot refreshed
[[22:45:56]] [INFO] Refreshing screenshot...
[[22:45:51]] [SUCCESS] Screenshot refreshed successfully
[[22:45:51]] [SUCCESS] Screenshot refreshed successfully
[[22:45:51]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:45:51]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:45:51]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:45:51]] [INFO] RLvvFEJpgz=running
[[22:45:51]] [INFO] Executing action 122/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[22:45:50]] [SUCCESS] Screenshot refreshed
[[22:45:50]] [INFO] Refreshing screenshot...
[[22:45:50]] [INFO] ly2oT3zqmf=pass
[[22:45:47]] [SUCCESS] Screenshot refreshed successfully
[[22:45:47]] [SUCCESS] Screenshot refreshed successfully
[[22:45:47]] [INFO] ly2oT3zqmf=running
[[22:45:47]] [INFO] Executing action 121/351: iOS Function: alert_accept
[[22:45:46]] [SUCCESS] Screenshot refreshed
[[22:45:46]] [INFO] Refreshing screenshot...
[[22:45:46]] [INFO] xAPeBnVHrT=pass
[[22:45:40]] [SUCCESS] Screenshot refreshed successfully
[[22:45:40]] [SUCCESS] Screenshot refreshed successfully
[[22:45:39]] [INFO] xAPeBnVHrT=running
[[22:45:39]] [INFO] Executing action 120/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:45:39]] [SUCCESS] Screenshot refreshed
[[22:45:39]] [INFO] Refreshing screenshot...
[[22:45:39]] [INFO] u6bRYZZFAv=pass
[[22:45:32]] [SUCCESS] Screenshot refreshed successfully
[[22:45:32]] [SUCCESS] Screenshot refreshed successfully
[[22:45:32]] [INFO] u6bRYZZFAv=running
[[22:45:32]] [INFO] Executing action 119/351: Wait for 5 ms
[[22:45:31]] [SUCCESS] Screenshot refreshed
[[22:45:31]] [INFO] Refreshing screenshot...
[[22:45:31]] [INFO] pjFNt3w5Fr=pass
[[22:45:17]] [SUCCESS] Screenshot refreshed successfully
[[22:45:17]] [SUCCESS] Screenshot refreshed successfully
[[22:45:16]] [INFO] pjFNt3w5Fr=running
[[22:45:16]] [INFO] Executing action 118/351: Restart app: env[appid]
[[22:45:16]] [SUCCESS] Screenshot refreshed
[[22:45:16]] [INFO] Refreshing screenshot...
[[22:45:16]] [SUCCESS] Screenshot refreshed
[[22:45:16]] [INFO] Refreshing screenshot...
[[22:45:14]] [SUCCESS] Screenshot refreshed successfully
[[22:45:14]] [SUCCESS] Screenshot refreshed successfully
[[22:45:14]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:45:14]] [SUCCESS] Screenshot refreshed
[[22:45:14]] [INFO] Refreshing screenshot...
[[22:45:02]] [SUCCESS] Screenshot refreshed successfully
[[22:45:02]] [SUCCESS] Screenshot refreshed successfully
[[22:45:02]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:45:01]] [SUCCESS] Screenshot refreshed
[[22:45:01]] [INFO] Refreshing screenshot...
[[22:44:58]] [SUCCESS] Screenshot refreshed successfully
[[22:44:58]] [SUCCESS] Screenshot refreshed successfully
[[22:44:58]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:44:57]] [SUCCESS] Screenshot refreshed
[[22:44:57]] [INFO] Refreshing screenshot...
[[22:44:54]] [SUCCESS] Screenshot refreshed successfully
[[22:44:54]] [SUCCESS] Screenshot refreshed successfully
[[22:44:53]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:44:53]] [SUCCESS] Screenshot refreshed
[[22:44:53]] [INFO] Refreshing screenshot...
[[22:44:46]] [SUCCESS] Screenshot refreshed successfully
[[22:44:46]] [SUCCESS] Screenshot refreshed successfully
[[22:44:46]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:44:45]] [SUCCESS] Screenshot refreshed
[[22:44:45]] [INFO] Refreshing screenshot...
[[22:44:39]] [SUCCESS] Screenshot refreshed successfully
[[22:44:39]] [SUCCESS] Screenshot refreshed successfully
[[22:44:38]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:44:38]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:44:38]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:44:38]] [INFO] ynNeRrFTrg=running
[[22:44:38]] [INFO] Executing action 117/351: cleanupSteps action
[[22:44:38]] [SUCCESS] Screenshot refreshed
[[22:44:38]] [INFO] Refreshing screenshot...
[[22:44:38]] [SUCCESS] Screenshot refreshed
[[22:44:38]] [INFO] Refreshing screenshot...
[[22:44:34]] [INFO] Executing Multi Step action step 33/33: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[22:44:34]] [SUCCESS] Screenshot refreshed successfully
[[22:44:34]] [SUCCESS] Screenshot refreshed successfully
[[22:44:33]] [SUCCESS] Screenshot refreshed
[[22:44:33]] [INFO] Refreshing screenshot...
[[22:44:30]] [SUCCESS] Screenshot refreshed successfully
[[22:44:30]] [SUCCESS] Screenshot refreshed successfully
[[22:44:30]] [INFO] Executing Multi Step action step 32/33: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[22:44:29]] [SUCCESS] Screenshot refreshed
[[22:44:29]] [INFO] Refreshing screenshot...
[[22:44:26]] [SUCCESS] Screenshot refreshed successfully
[[22:44:26]] [SUCCESS] Screenshot refreshed successfully
[[22:44:25]] [INFO] Executing Multi Step action step 31/33: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[22:44:25]] [SUCCESS] Screenshot refreshed
[[22:44:25]] [INFO] Refreshing screenshot...
[[22:44:21]] [SUCCESS] Screenshot refreshed successfully
[[22:44:21]] [SUCCESS] Screenshot refreshed successfully
[[22:44:21]] [INFO] Executing Multi Step action step 30/33: Tap on image: banner-close-updated.png
[[22:44:20]] [SUCCESS] Screenshot refreshed
[[22:44:20]] [INFO] Refreshing screenshot...
[[22:44:16]] [SUCCESS] Screenshot refreshed successfully
[[22:44:16]] [SUCCESS] Screenshot refreshed successfully
[[22:44:16]] [INFO] Executing Multi Step action step 29/33: Tap on image: banner-close-updated.png
[[22:44:15]] [SUCCESS] Screenshot refreshed
[[22:44:15]] [INFO] Refreshing screenshot...
[[22:44:11]] [INFO] Executing Multi Step action step 28/33: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[22:44:11]] [SUCCESS] Screenshot refreshed successfully
[[22:44:11]] [SUCCESS] Screenshot refreshed successfully
[[22:44:11]] [SUCCESS] Screenshot refreshed
[[22:44:11]] [INFO] Refreshing screenshot...
[[22:44:07]] [SUCCESS] Screenshot refreshed successfully
[[22:44:07]] [SUCCESS] Screenshot refreshed successfully
[[22:44:07]] [INFO] Executing Multi Step action step 27/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[22:44:07]] [SUCCESS] Screenshot refreshed
[[22:44:07]] [INFO] Refreshing screenshot...
[[22:44:03]] [SUCCESS] Screenshot refreshed successfully
[[22:44:03]] [SUCCESS] Screenshot refreshed successfully
[[22:44:03]] [INFO] Executing Multi Step action step 26/33: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[22:44:02]] [SUCCESS] Screenshot refreshed
[[22:44:02]] [INFO] Refreshing screenshot...
[[22:43:59]] [SUCCESS] Screenshot refreshed successfully
[[22:43:59]] [SUCCESS] Screenshot refreshed successfully
[[22:43:58]] [INFO] Executing Multi Step action step 25/33: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[22:43:58]] [SUCCESS] Screenshot refreshed
[[22:43:58]] [INFO] Refreshing screenshot...
[[22:43:55]] [SUCCESS] Screenshot refreshed successfully
[[22:43:55]] [SUCCESS] Screenshot refreshed successfully
[[22:43:55]] [INFO] Executing Multi Step action step 24/33: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[22:43:54]] [SUCCESS] Screenshot refreshed
[[22:43:54]] [INFO] Refreshing screenshot...
[[22:43:51]] [SUCCESS] Screenshot refreshed successfully
[[22:43:51]] [SUCCESS] Screenshot refreshed successfully
[[22:43:51]] [INFO] Executing Multi Step action step 23/33: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[22:43:50]] [SUCCESS] Screenshot refreshed
[[22:43:50]] [INFO] Refreshing screenshot...
[[22:43:42]] [SUCCESS] Screenshot refreshed successfully
[[22:43:42]] [SUCCESS] Screenshot refreshed successfully
[[22:43:42]] [INFO] Executing Multi Step action step 22/33: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[22:43:41]] [SUCCESS] Screenshot refreshed
[[22:43:41]] [INFO] Refreshing screenshot...
[[22:43:38]] [SUCCESS] Screenshot refreshed successfully
[[22:43:38]] [SUCCESS] Screenshot refreshed successfully
[[22:43:38]] [INFO] Executing Multi Step action step 21/33: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[22:43:37]] [SUCCESS] Screenshot refreshed
[[22:43:37]] [INFO] Refreshing screenshot...
[[22:43:34]] [SUCCESS] Screenshot refreshed successfully
[[22:43:34]] [SUCCESS] Screenshot refreshed successfully
[[22:43:34]] [INFO] Executing Multi Step action step 20/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[22:43:33]] [SUCCESS] Screenshot refreshed
[[22:43:33]] [INFO] Refreshing screenshot...
[[22:43:26]] [SUCCESS] Screenshot refreshed successfully
[[22:43:26]] [SUCCESS] Screenshot refreshed successfully
[[22:43:26]] [INFO] Executing Multi Step action step 19/33: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[22:43:25]] [SUCCESS] Screenshot refreshed
[[22:43:25]] [INFO] Refreshing screenshot...
[[22:43:19]] [SUCCESS] Screenshot refreshed successfully
[[22:43:19]] [SUCCESS] Screenshot refreshed successfully
[[22:43:19]] [INFO] Executing Multi Step action step 18/33: Tap on Text: "Quay"
[[22:43:18]] [SUCCESS] Screenshot refreshed
[[22:43:18]] [INFO] Refreshing screenshot...
[[22:43:15]] [SUCCESS] Screenshot refreshed successfully
[[22:43:15]] [SUCCESS] Screenshot refreshed successfully
[[22:43:15]] [INFO] Executing Multi Step action step 17/33: Tap on image: keyboard_done_iphoneSE.png
[[22:43:14]] [SUCCESS] Screenshot refreshed
[[22:43:14]] [INFO] Refreshing screenshot...
[[22:43:07]] [SUCCESS] Screenshot refreshed successfully
[[22:43:07]] [SUCCESS] Screenshot refreshed successfully
[[22:43:07]] [INFO] Executing Multi Step action step 16/33: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "Quay Street"
[[22:43:07]] [SUCCESS] Screenshot refreshed
[[22:43:07]] [INFO] Refreshing screenshot...
[[22:43:02]] [SUCCESS] Screenshot refreshed successfully
[[22:43:02]] [SUCCESS] Screenshot refreshed successfully
[[22:43:02]] [INFO] Executing Multi Step action step 15/33: Tap on Text: "address"
[[22:43:01]] [SUCCESS] Screenshot refreshed
[[22:43:01]] [INFO] Refreshing screenshot...
[[22:42:50]] [SUCCESS] Screenshot refreshed successfully
[[22:42:50]] [SUCCESS] Screenshot refreshed successfully
[[22:42:50]] [INFO] Executing Multi Step action step 14/33: Wait for 10 ms
[[22:42:49]] [SUCCESS] Screenshot refreshed
[[22:42:49]] [INFO] Refreshing screenshot...
[[22:42:45]] [SUCCESS] Screenshot refreshed successfully
[[22:42:45]] [SUCCESS] Screenshot refreshed successfully
[[22:42:45]] [INFO] Executing Multi Step action step 13/33: iOS Function: text - Text: " "
[[22:42:45]] [SUCCESS] Screenshot refreshed
[[22:42:45]] [INFO] Refreshing screenshot...
[[22:42:41]] [SUCCESS] Screenshot refreshed successfully
[[22:42:41]] [SUCCESS] Screenshot refreshed successfully
[[22:42:40]] [INFO] Executing Multi Step action step 12/33: textClear action
[[22:42:40]] [SUCCESS] Screenshot refreshed
[[22:42:40]] [INFO] Refreshing screenshot...
[[22:42:36]] [SUCCESS] Screenshot refreshed successfully
[[22:42:36]] [SUCCESS] Screenshot refreshed successfully
[[22:42:36]] [INFO] Executing Multi Step action step 11/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[22:42:36]] [SUCCESS] Screenshot refreshed
[[22:42:36]] [INFO] Refreshing screenshot...
[[22:42:32]] [SUCCESS] Screenshot refreshed successfully
[[22:42:32]] [SUCCESS] Screenshot refreshed successfully
[[22:42:31]] [INFO] Executing Multi Step action step 10/33: textClear action
[[22:42:31]] [SUCCESS] Screenshot refreshed
[[22:42:31]] [INFO] Refreshing screenshot...
[[22:42:27]] [SUCCESS] Screenshot refreshed successfully
[[22:42:27]] [SUCCESS] Screenshot refreshed successfully
[[22:42:27]] [INFO] Executing Multi Step action step 9/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:42:27]] [SUCCESS] Screenshot refreshed
[[22:42:27]] [INFO] Refreshing screenshot...
[[22:42:23]] [SUCCESS] Screenshot refreshed successfully
[[22:42:23]] [SUCCESS] Screenshot refreshed successfully
[[22:42:23]] [INFO] Executing Multi Step action step 8/33: textClear action
[[22:42:22]] [SUCCESS] Screenshot refreshed
[[22:42:22]] [INFO] Refreshing screenshot...
[[22:42:18]] [SUCCESS] Screenshot refreshed successfully
[[22:42:18]] [SUCCESS] Screenshot refreshed successfully
[[22:42:18]] [INFO] Executing Multi Step action step 7/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[22:42:17]] [SUCCESS] Screenshot refreshed
[[22:42:17]] [INFO] Refreshing screenshot...
[[22:42:13]] [SUCCESS] Screenshot refreshed successfully
[[22:42:13]] [SUCCESS] Screenshot refreshed successfully
[[22:42:13]] [INFO] Executing Multi Step action step 6/33: textClear action
[[22:42:13]] [SUCCESS] Screenshot refreshed
[[22:42:13]] [INFO] Refreshing screenshot...
[[22:42:09]] [SUCCESS] Screenshot refreshed successfully
[[22:42:09]] [SUCCESS] Screenshot refreshed successfully
[[22:42:09]] [INFO] Executing Multi Step action step 5/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[22:42:08]] [SUCCESS] Screenshot refreshed
[[22:42:08]] [INFO] Refreshing screenshot...
[[22:42:05]] [SUCCESS] Screenshot refreshed successfully
[[22:42:05]] [SUCCESS] Screenshot refreshed successfully
[[22:42:05]] [INFO] Executing Multi Step action step 4/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[22:42:04]] [SUCCESS] Screenshot refreshed
[[22:42:04]] [INFO] Refreshing screenshot...
[[22:41:54]] [SUCCESS] Screenshot refreshed successfully
[[22:41:54]] [SUCCESS] Screenshot refreshed successfully
[[22:41:53]] [INFO] Executing Multi Step action step 3/33: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[22:41:53]] [SUCCESS] Screenshot refreshed
[[22:41:53]] [INFO] Refreshing screenshot...
[[22:41:49]] [SUCCESS] Screenshot refreshed successfully
[[22:41:49]] [SUCCESS] Screenshot refreshed successfully
[[22:41:49]] [INFO] Executing Multi Step action step 2/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[22:41:48]] [SUCCESS] Screenshot refreshed
[[22:41:48]] [INFO] Refreshing screenshot...
[[22:41:43]] [SUCCESS] Screenshot refreshed successfully
[[22:41:43]] [SUCCESS] Screenshot refreshed successfully
[[22:41:43]] [INFO] Executing Multi Step action step 1/33: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[22:41:43]] [INFO] Loaded 33 steps from test case: Delivery Buy Step NZ
[[22:41:43]] [INFO] Loading steps for multiStep action: Delivery Buy Step NZ
[[22:41:43]] [INFO] MuX1dfl3aB=running
[[22:41:43]] [INFO] Executing action 116/351: Execute Test Case: Delivery Buy Step NZ (33 steps)
[[22:41:42]] [SUCCESS] Screenshot refreshed
[[22:41:42]] [INFO] Refreshing screenshot...
[[22:41:42]] [INFO] jY0oPjKbuS=pass
[[22:41:39]] [SUCCESS] Screenshot refreshed successfully
[[22:41:39]] [SUCCESS] Screenshot refreshed successfully
[[22:41:39]] [INFO] jY0oPjKbuS=running
[[22:41:39]] [INFO] Executing action 115/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[22:41:38]] [SUCCESS] Screenshot refreshed
[[22:41:38]] [INFO] Refreshing screenshot...
[[22:41:38]] [INFO] FnrbyHq7bU=pass
[[22:41:34]] [SUCCESS] Screenshot refreshed successfully
[[22:41:34]] [SUCCESS] Screenshot refreshed successfully
[[22:41:34]] [INFO] FnrbyHq7bU=running
[[22:41:34]] [INFO] Executing action 114/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[22:41:34]] [SUCCESS] Screenshot refreshed
[[22:41:34]] [INFO] Refreshing screenshot...
[[22:41:34]] [INFO] nAB6Q8LAdv=pass
[[22:41:31]] [SUCCESS] Screenshot refreshed successfully
[[22:41:31]] [SUCCESS] Screenshot refreshed successfully
[[22:41:30]] [INFO] nAB6Q8LAdv=running
[[22:41:30]] [INFO] Executing action 113/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[22:41:30]] [SUCCESS] Screenshot refreshed
[[22:41:30]] [INFO] Refreshing screenshot...
[[22:41:30]] [INFO] 13YG4jrM9E=pass
[[22:41:26]] [SUCCESS] Screenshot refreshed successfully
[[22:41:26]] [SUCCESS] Screenshot refreshed successfully
[[22:41:26]] [INFO] 13YG4jrM9E=running
[[22:41:26]] [INFO] Executing action 112/351: iOS Function: text - Text: "P_43250042"
[[22:41:25]] [SUCCESS] Screenshot refreshed
[[22:41:25]] [INFO] Refreshing screenshot...
[[22:41:25]] [INFO] rqLJpAP0mA=pass
[[22:41:20]] [SUCCESS] Screenshot refreshed successfully
[[22:41:20]] [SUCCESS] Screenshot refreshed successfully
[[22:41:20]] [INFO] rqLJpAP0mA=running
[[22:41:20]] [INFO] Executing action 111/351: Tap on Text: "Find"
[[22:41:19]] [SUCCESS] Screenshot refreshed
[[22:41:19]] [INFO] Refreshing screenshot...
[[22:41:19]] [INFO] cKNu2QoRC1=pass
[[22:41:16]] [SUCCESS] Screenshot refreshed successfully
[[22:41:16]] [SUCCESS] Screenshot refreshed successfully
[[22:41:15]] [INFO] cKNu2QoRC1=running
[[22:41:15]] [INFO] Executing action 110/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[22:41:15]] [SUCCESS] Screenshot refreshed
[[22:41:15]] [INFO] Refreshing screenshot...
[[22:41:15]] [INFO] OyUowAaBzD=pass
[[22:41:11]] [SUCCESS] Screenshot refreshed successfully
[[22:41:11]] [SUCCESS] Screenshot refreshed successfully
[[22:41:11]] [INFO] OyUowAaBzD=running
[[22:41:11]] [INFO] Executing action 109/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[22:41:10]] [SUCCESS] Screenshot refreshed
[[22:41:10]] [INFO] Refreshing screenshot...
[[22:41:10]] [INFO] Ob26qqcA0p=pass
[[22:41:04]] [SUCCESS] Screenshot refreshed successfully
[[22:41:04]] [SUCCESS] Screenshot refreshed successfully
[[22:41:04]] [INFO] Ob26qqcA0p=running
[[22:41:04]] [INFO] Executing action 108/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:41:03]] [SUCCESS] Screenshot refreshed
[[22:41:03]] [INFO] Refreshing screenshot...
[[22:41:03]] [INFO] k3mu9Mt7Ec=pass
[[22:41:00]] [SUCCESS] Screenshot refreshed successfully
[[22:41:00]] [SUCCESS] Screenshot refreshed successfully
[[22:40:59]] [INFO] k3mu9Mt7Ec=running
[[22:40:59]] [INFO] Executing action 107/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:40:59]] [SUCCESS] Screenshot refreshed
[[22:40:59]] [INFO] Refreshing screenshot...
[[22:40:59]] [INFO] 8umPSX0vrr=pass
[[22:40:55]] [INFO] 8umPSX0vrr=running
[[22:40:55]] [INFO] Executing action 106/351: Tap on image: banner-close-updated.png
[[22:40:55]] [SUCCESS] Screenshot refreshed successfully
[[22:40:55]] [SUCCESS] Screenshot refreshed successfully
[[22:40:54]] [SUCCESS] Screenshot refreshed
[[22:40:54]] [INFO] Refreshing screenshot...
[[22:40:54]] [INFO] pr9o8Zsm5p=pass
[[22:40:51]] [SUCCESS] Screenshot refreshed successfully
[[22:40:51]] [SUCCESS] Screenshot refreshed successfully
[[22:40:51]] [INFO] pr9o8Zsm5p=running
[[22:40:51]] [INFO] Executing action 105/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[22:40:50]] [SUCCESS] Screenshot refreshed
[[22:40:50]] [INFO] Refreshing screenshot...
[[22:40:50]] [INFO] Qbg9bipTGs=pass
[[22:40:45]] [SUCCESS] Screenshot refreshed successfully
[[22:40:45]] [SUCCESS] Screenshot refreshed successfully
[[22:40:45]] [INFO] Qbg9bipTGs=running
[[22:40:45]] [INFO] Executing action 104/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:40:45]] [SUCCESS] Screenshot refreshed
[[22:40:45]] [INFO] Refreshing screenshot...
[[22:40:45]] [INFO] qjj0i3rcUh=pass
[[22:40:40]] [SUCCESS] Screenshot refreshed successfully
[[22:40:40]] [SUCCESS] Screenshot refreshed successfully
[[22:40:40]] [INFO] qjj0i3rcUh=running
[[22:40:40]] [INFO] Executing action 103/351: Tap on Text: "Collect"
[[22:40:40]] [SUCCESS] Screenshot refreshed
[[22:40:40]] [INFO] Refreshing screenshot...
[[22:40:40]] [INFO] uM5FOSrU5U=pass
[[22:40:27]] [SUCCESS] Screenshot refreshed successfully
[[22:40:27]] [SUCCESS] Screenshot refreshed successfully
[[22:40:27]] [INFO] uM5FOSrU5U=running
[[22:40:27]] [INFO] Executing action 102/351: Check if image "cnc-tab-se.png" exists on screen
[[22:40:26]] [SUCCESS] Screenshot refreshed
[[22:40:26]] [INFO] Refreshing screenshot...
[[22:40:26]] [INFO] F1olhgKhUt=pass
[[22:40:23]] [SUCCESS] Screenshot refreshed successfully
[[22:40:23]] [SUCCESS] Screenshot refreshed successfully
[[22:40:23]] [INFO] F1olhgKhUt=running
[[22:40:23]] [INFO] Executing action 101/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[22:40:22]] [SUCCESS] Screenshot refreshed
[[22:40:22]] [INFO] Refreshing screenshot...
[[22:40:22]] [INFO] jY0oPjKbuS=pass
[[22:40:19]] [SUCCESS] Screenshot refreshed successfully
[[22:40:19]] [SUCCESS] Screenshot refreshed successfully
[[22:40:19]] [INFO] jY0oPjKbuS=running
[[22:40:19]] [INFO] Executing action 100/351: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[22:40:18]] [SUCCESS] Screenshot refreshed
[[22:40:18]] [INFO] Refreshing screenshot...
[[22:40:18]] [INFO] FnrbyHq7bU=pass
[[22:40:14]] [SUCCESS] Screenshot refreshed successfully
[[22:40:14]] [SUCCESS] Screenshot refreshed successfully
[[22:40:14]] [INFO] FnrbyHq7bU=running
[[22:40:14]] [INFO] Executing action 99/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[22:40:14]] [SUCCESS] Screenshot refreshed
[[22:40:14]] [INFO] Refreshing screenshot...
[[22:40:14]] [INFO] nAB6Q8LAdv=pass
[[22:40:11]] [SUCCESS] Screenshot refreshed successfully
[[22:40:11]] [SUCCESS] Screenshot refreshed successfully
[[22:40:10]] [INFO] nAB6Q8LAdv=running
[[22:40:10]] [INFO] Executing action 98/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[22:40:10]] [SUCCESS] Screenshot refreshed
[[22:40:10]] [INFO] Refreshing screenshot...
[[22:40:10]] [INFO] 13YG4jrM9E=pass
[[22:40:06]] [SUCCESS] Screenshot refreshed successfully
[[22:40:06]] [SUCCESS] Screenshot refreshed successfully
[[22:40:06]] [INFO] 13YG4jrM9E=running
[[22:40:06]] [INFO] Executing action 97/351: iOS Function: text - Text: "P_43250042"
[[22:40:05]] [SUCCESS] Screenshot refreshed
[[22:40:05]] [INFO] Refreshing screenshot...
[[22:40:05]] [INFO] rqLJpAP0mA=pass
[[22:40:01]] [SUCCESS] Screenshot refreshed successfully
[[22:40:01]] [SUCCESS] Screenshot refreshed successfully
[[22:39:59]] [INFO] rqLJpAP0mA=running
[[22:39:59]] [INFO] Executing action 96/351: Tap on Text: "Find"
[[22:39:59]] [SUCCESS] Screenshot refreshed
[[22:39:59]] [INFO] Refreshing screenshot...
[[22:39:59]] [SUCCESS] Screenshot refreshed
[[22:39:59]] [INFO] Refreshing screenshot...
[[22:39:54]] [SUCCESS] Screenshot refreshed successfully
[[22:39:54]] [SUCCESS] Screenshot refreshed successfully
[[22:39:54]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:39:54]] [SUCCESS] Screenshot refreshed
[[22:39:54]] [INFO] Refreshing screenshot...
[[22:39:49]] [SUCCESS] Screenshot refreshed successfully
[[22:39:49]] [SUCCESS] Screenshot refreshed successfully
[[22:39:49]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:39:48]] [SUCCESS] Screenshot refreshed
[[22:39:48]] [INFO] Refreshing screenshot...
[[22:39:44]] [SUCCESS] Screenshot refreshed successfully
[[22:39:44]] [SUCCESS] Screenshot refreshed successfully
[[22:39:44]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:39:43]] [SUCCESS] Screenshot refreshed
[[22:39:43]] [INFO] Refreshing screenshot...
[[22:39:39]] [SUCCESS] Screenshot refreshed successfully
[[22:39:39]] [SUCCESS] Screenshot refreshed successfully
[[22:39:39]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:39:39]] [SUCCESS] Screenshot refreshed
[[22:39:39]] [INFO] Refreshing screenshot...
[[22:39:33]] [SUCCESS] Screenshot refreshed successfully
[[22:39:33]] [SUCCESS] Screenshot refreshed successfully
[[22:39:33]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:39:33]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:39:33]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:39:33]] [INFO] SVt620PG1t=running
[[22:39:33]] [INFO] Executing action 95/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[22:39:33]] [SUCCESS] Screenshot refreshed
[[22:39:33]] [INFO] Refreshing screenshot...
[[22:39:33]] [INFO] 3caMBvQX7k=pass
[[22:39:29]] [SUCCESS] Screenshot refreshed successfully
[[22:39:29]] [SUCCESS] Screenshot refreshed successfully
[[22:39:29]] [INFO] 3caMBvQX7k=running
[[22:39:29]] [INFO] Executing action 94/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:39:29]] [SUCCESS] Screenshot refreshed
[[22:39:29]] [INFO] Refreshing screenshot...
[[22:39:29]] [INFO] yUJyVO5Wev=pass
[[22:39:26]] [SUCCESS] Screenshot refreshed successfully
[[22:39:26]] [SUCCESS] Screenshot refreshed successfully
[[22:39:26]] [INFO] yUJyVO5Wev=running
[[22:39:26]] [INFO] Executing action 93/351: iOS Function: alert_accept
[[22:39:25]] [SUCCESS] Screenshot refreshed
[[22:39:25]] [INFO] Refreshing screenshot...
[[22:39:25]] [INFO] rkL0oz4kiL=pass
[[22:39:18]] [SUCCESS] Screenshot refreshed successfully
[[22:39:18]] [SUCCESS] Screenshot refreshed successfully
[[22:39:18]] [INFO] rkL0oz4kiL=running
[[22:39:18]] [INFO] Executing action 92/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:39:17]] [SUCCESS] Screenshot refreshed
[[22:39:17]] [INFO] Refreshing screenshot...
[[22:39:17]] [INFO] HotUJOd6oB=pass
[[22:39:03]] [SUCCESS] Screenshot refreshed successfully
[[22:39:03]] [SUCCESS] Screenshot refreshed successfully
[[22:39:02]] [INFO] HotUJOd6oB=running
[[22:39:02]] [INFO] Executing action 91/351: Restart app: env[appid]
[[22:39:02]] [SUCCESS] Screenshot refreshed
[[22:39:02]] [INFO] Refreshing screenshot...
[[22:39:02]] [SUCCESS] Screenshot refreshed
[[22:39:02]] [INFO] Refreshing screenshot...
[[22:39:00]] [SUCCESS] Screenshot refreshed successfully
[[22:39:00]] [SUCCESS] Screenshot refreshed successfully
[[22:39:00]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:39:00]] [SUCCESS] Screenshot refreshed
[[22:39:00]] [INFO] Refreshing screenshot...
[[22:38:48]] [SUCCESS] Screenshot refreshed successfully
[[22:38:48]] [SUCCESS] Screenshot refreshed successfully
[[22:38:48]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:38:47]] [SUCCESS] Screenshot refreshed
[[22:38:47]] [INFO] Refreshing screenshot...
[[22:38:43]] [SUCCESS] Screenshot refreshed successfully
[[22:38:43]] [SUCCESS] Screenshot refreshed successfully
[[22:38:43]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:38:43]] [SUCCESS] Screenshot refreshed
[[22:38:43]] [INFO] Refreshing screenshot...
[[22:38:40]] [SUCCESS] Screenshot refreshed successfully
[[22:38:40]] [SUCCESS] Screenshot refreshed successfully
[[22:38:39]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:38:39]] [SUCCESS] Screenshot refreshed
[[22:38:39]] [INFO] Refreshing screenshot...
[[22:38:32]] [SUCCESS] Screenshot refreshed successfully
[[22:38:32]] [SUCCESS] Screenshot refreshed successfully
[[22:38:32]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:38:31]] [SUCCESS] Screenshot refreshed
[[22:38:31]] [INFO] Refreshing screenshot...
[[22:38:26]] [SUCCESS] Screenshot refreshed successfully
[[22:38:26]] [SUCCESS] Screenshot refreshed successfully
[[22:38:25]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:38:25]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:38:25]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:38:25]] [INFO] 4SHkoWH0yY=running
[[22:38:25]] [INFO] Executing action 90/351: cleanupSteps action
[[22:38:25]] [SUCCESS] Screenshot refreshed
[[22:38:25]] [INFO] Refreshing screenshot...
[[22:38:25]] [INFO] x4yLCZHaCR=pass
[[22:38:21]] [INFO] x4yLCZHaCR=running
[[22:38:21]] [INFO] Executing action 89/351: Terminate app: env[appid]
[[22:38:21]] [SUCCESS] Screenshot refreshed successfully
[[22:38:21]] [SUCCESS] Screenshot refreshed successfully
[[22:38:21]] [SUCCESS] Screenshot refreshed
[[22:38:21]] [INFO] Refreshing screenshot...
[[22:38:21]] [INFO] 2p13JoJbbA=pass
[[22:38:17]] [SUCCESS] Screenshot refreshed successfully
[[22:38:17]] [SUCCESS] Screenshot refreshed successfully
[[22:38:17]] [INFO] 2p13JoJbbA=running
[[22:38:17]] [INFO] Executing action 88/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[22:38:17]] [SUCCESS] Screenshot refreshed
[[22:38:17]] [INFO] Refreshing screenshot...
[[22:38:17]] [INFO] 2p13JoJbbA=pass
[[22:38:13]] [SUCCESS] Screenshot refreshed successfully
[[22:38:13]] [SUCCESS] Screenshot refreshed successfully
[[22:38:13]] [INFO] 2p13JoJbbA=running
[[22:38:13]] [INFO] Executing action 87/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[22:38:12]] [SUCCESS] Screenshot refreshed
[[22:38:12]] [INFO] Refreshing screenshot...
[[22:38:12]] [INFO] ZCsqeOXrY1=pass
[[22:38:09]] [SUCCESS] Screenshot refreshed successfully
[[22:38:09]] [SUCCESS] Screenshot refreshed successfully
[[22:38:09]] [INFO] ZCsqeOXrY1=running
[[22:38:09]] [INFO] Executing action 86/351: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")]
[[22:38:09]] [SUCCESS] Screenshot refreshed
[[22:38:09]] [INFO] Refreshing screenshot...
[[22:38:09]] [INFO] F4NGh9HrLw=pass
[[22:38:05]] [SUCCESS] Screenshot refreshed successfully
[[22:38:05]] [SUCCESS] Screenshot refreshed successfully
[[22:38:05]] [INFO] F4NGh9HrLw=running
[[22:38:05]] [INFO] Executing action 85/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[22:38:04]] [SUCCESS] Screenshot refreshed
[[22:38:04]] [INFO] Refreshing screenshot...
[[22:38:04]] [INFO] kz9lnCdwoH=pass
[[22:38:01]] [SUCCESS] Screenshot refreshed successfully
[[22:38:01]] [SUCCESS] Screenshot refreshed successfully
[[22:38:00]] [INFO] kz9lnCdwoH=running
[[22:38:00]] [INFO] Executing action 84/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[22:38:00]] [SUCCESS] Screenshot refreshed
[[22:38:00]] [INFO] Refreshing screenshot...
[[22:38:00]] [INFO] KlfYmNjrq8=pass
[[22:37:56]] [SUCCESS] Screenshot refreshed successfully
[[22:37:56]] [SUCCESS] Screenshot refreshed successfully
[[22:37:56]] [INFO] KlfYmNjrq8=running
[[22:37:56]] [INFO] Executing action 83/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[22:37:55]] [SUCCESS] Screenshot refreshed
[[22:37:55]] [INFO] Refreshing screenshot...
[[22:37:55]] [INFO] iwqbVl90WJ=pass
[[22:37:51]] [SUCCESS] Screenshot refreshed successfully
[[22:37:51]] [SUCCESS] Screenshot refreshed successfully
[[22:37:51]] [INFO] iwqbVl90WJ=running
[[22:37:51]] [INFO] Executing action 82/351: iOS Function: text - Text: "notebook"
[[22:37:51]] [SUCCESS] Screenshot refreshed
[[22:37:51]] [INFO] Refreshing screenshot...
[[22:37:51]] [INFO] DY8MfL0wXI=pass
[[22:37:46]] [SUCCESS] Screenshot refreshed successfully
[[22:37:46]] [SUCCESS] Screenshot refreshed successfully
[[22:37:45]] [INFO] DY8MfL0wXI=running
[[22:37:45]] [INFO] Executing action 81/351: Tap on Text: "Find"
[[22:37:45]] [SUCCESS] Screenshot refreshed
[[22:37:45]] [INFO] Refreshing screenshot...
[[22:37:45]] [INFO] Ey8MUB57vM=pass
[[22:37:39]] [SUCCESS] Screenshot refreshed successfully
[[22:37:39]] [SUCCESS] Screenshot refreshed successfully
[[22:37:39]] [INFO] Ey8MUB57vM=running
[[22:37:39]] [INFO] Executing action 80/351: Restart app: env[appid]
[[22:37:39]] [SUCCESS] Screenshot refreshed
[[22:37:39]] [INFO] Refreshing screenshot...
[[22:37:39]] [INFO] 5Gj5mgIxVu=pass
[[22:37:35]] [SUCCESS] Screenshot refreshed successfully
[[22:37:35]] [SUCCESS] Screenshot refreshed successfully
[[22:37:35]] [INFO] 5Gj5mgIxVu=running
[[22:37:35]] [INFO] Executing action 79/351: Tap on image: env[device-back-img]
[[22:37:34]] [SUCCESS] Screenshot refreshed
[[22:37:34]] [INFO] Refreshing screenshot...
[[22:37:34]] [INFO] QPKR6jUF9O=pass
[[22:37:32]] [SUCCESS] Screenshot refreshed successfully
[[22:37:32]] [SUCCESS] Screenshot refreshed successfully
[[22:37:32]] [INFO] QPKR6jUF9O=running
[[22:37:32]] [INFO] Executing action 78/351: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[22:37:31]] [SUCCESS] Screenshot refreshed
[[22:37:31]] [INFO] Refreshing screenshot...
[[22:37:31]] [INFO] vfwUVEyq6X=pass
[[22:37:28]] [SUCCESS] Screenshot refreshed successfully
[[22:37:28]] [SUCCESS] Screenshot refreshed successfully
[[22:37:28]] [INFO] vfwUVEyq6X=running
[[22:37:28]] [INFO] Executing action 77/351: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[22:37:28]] [SUCCESS] Screenshot refreshed
[[22:37:28]] [INFO] Refreshing screenshot...
[[22:37:28]] [INFO] Xr6F8gdd8q=pass
[[22:37:24]] [SUCCESS] Screenshot refreshed successfully
[[22:37:24]] [SUCCESS] Screenshot refreshed successfully
[[22:37:24]] [INFO] Xr6F8gdd8q=running
[[22:37:24]] [INFO] Executing action 76/351: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[22:37:24]] [SUCCESS] Screenshot refreshed
[[22:37:24]] [INFO] Refreshing screenshot...
[[22:37:24]] [INFO] 92tKl3T5N8=pass
[[22:37:20]] [SUCCESS] Screenshot refreshed successfully
[[22:37:20]] [SUCCESS] Screenshot refreshed successfully
[[22:37:20]] [INFO] 92tKl3T5N8=running
[[22:37:20]] [INFO] Executing action 75/351: Tap on image: env[device-back-img]
[[22:37:19]] [SUCCESS] Screenshot refreshed
[[22:37:19]] [INFO] Refreshing screenshot...
[[22:37:19]] [INFO] ky6rfmPv0u=pass
[[22:37:16]] [SUCCESS] Screenshot refreshed successfully
[[22:37:16]] [SUCCESS] Screenshot refreshed successfully
[[22:37:15]] [INFO] ky6rfmPv0u=running
[[22:37:15]] [INFO] Executing action 74/351: Tap on image: env[device-back-img]
[[22:37:15]] [SUCCESS] Screenshot refreshed
[[22:37:15]] [INFO] Refreshing screenshot...
[[22:37:15]] [INFO] fPX582qHkp=pass
[[22:36:52]] [SUCCESS] Screenshot refreshed successfully
[[22:36:52]] [SUCCESS] Screenshot refreshed successfully
[[22:36:52]] [INFO] fPX582qHkp=running
[[22:36:52]] [INFO] Executing action 73/351: Check if image "search-result-test-se.png" exists on screen
[[22:36:51]] [SUCCESS] Screenshot refreshed
[[22:36:51]] [INFO] Refreshing screenshot...
[[22:36:51]] [INFO] JRheDTvpJf=pass
[[22:36:47]] [SUCCESS] Screenshot refreshed successfully
[[22:36:47]] [SUCCESS] Screenshot refreshed successfully
[[22:36:47]] [INFO] JRheDTvpJf=running
[[22:36:47]] [INFO] Executing action 72/351: iOS Function: text - Text: "Kid toy"
[[22:36:46]] [SUCCESS] Screenshot refreshed
[[22:36:46]] [INFO] Refreshing screenshot...
[[22:36:46]] [INFO] yEga5MkcRe=pass
[[22:36:43]] [SUCCESS] Screenshot refreshed successfully
[[22:36:43]] [SUCCESS] Screenshot refreshed successfully
[[22:36:43]] [INFO] yEga5MkcRe=running
[[22:36:43]] [INFO] Executing action 71/351: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[22:36:42]] [SUCCESS] Screenshot refreshed
[[22:36:42]] [INFO] Refreshing screenshot...
[[22:36:42]] [INFO] F4NGh9HrLw=pass
[[22:36:39]] [INFO] F4NGh9HrLw=running
[[22:36:39]] [INFO] Executing action 70/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[22:36:39]] [SUCCESS] Screenshot refreshed successfully
[[22:36:39]] [SUCCESS] Screenshot refreshed successfully
[[22:36:38]] [SUCCESS] Screenshot refreshed
[[22:36:38]] [INFO] Refreshing screenshot...
[[22:36:38]] [INFO] DhWa2PCBXE=pass
[[22:36:34]] [SUCCESS] Screenshot refreshed successfully
[[22:36:34]] [SUCCESS] Screenshot refreshed successfully
[[22:36:33]] [INFO] DhWa2PCBXE=running
[[22:36:33]] [INFO] Executing action 69/351: Tap on Text: "more"
[[22:36:33]] [SUCCESS] Screenshot refreshed
[[22:36:33]] [INFO] Refreshing screenshot...
[[22:36:33]] [INFO] pk2DLZFBmx=pass
[[22:36:28]] [SUCCESS] Screenshot refreshed successfully
[[22:36:28]] [SUCCESS] Screenshot refreshed successfully
[[22:36:28]] [INFO] pk2DLZFBmx=running
[[22:36:28]] [INFO] Executing action 68/351: Swipe from (50%, 70%) to (50%, 50%)
[[22:36:27]] [SUCCESS] Screenshot refreshed
[[22:36:27]] [INFO] Refreshing screenshot...
[[22:36:27]] [INFO] F9UfvzyNii=pass
[[22:36:24]] [SUCCESS] Screenshot refreshed successfully
[[22:36:24]] [SUCCESS] Screenshot refreshed successfully
[[22:36:23]] [INFO] F9UfvzyNii=running
[[22:36:23]] [INFO] Executing action 67/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"]
[[22:36:23]] [SUCCESS] Screenshot refreshed
[[22:36:23]] [INFO] Refreshing screenshot...
[[22:36:23]] [INFO] s0WyiD1w0B=pass
[[22:36:19]] [SUCCESS] Screenshot refreshed successfully
[[22:36:19]] [SUCCESS] Screenshot refreshed successfully
[[22:36:18]] [INFO] s0WyiD1w0B=running
[[22:36:18]] [INFO] Executing action 66/351: Tap on image: banner-close-updated.png
[[22:36:17]] [SUCCESS] Screenshot refreshed
[[22:36:17]] [INFO] Refreshing screenshot...
[[22:36:17]] [INFO] gekNSY5O2E=pass
[[22:36:04]] [SUCCESS] Screenshot refreshed successfully
[[22:36:04]] [SUCCESS] Screenshot refreshed successfully
[[22:36:04]] [INFO] gekNSY5O2E=running
[[22:36:04]] [INFO] Executing action 65/351: Check if image "product-share-logo.png" exists on screen
[[22:36:03]] [SUCCESS] Screenshot refreshed
[[22:36:03]] [INFO] Refreshing screenshot...
[[22:36:03]] [INFO] 83tV9A4NOn=pass
[[22:36:00]] [SUCCESS] Screenshot refreshed successfully
[[22:36:00]] [SUCCESS] Screenshot refreshed successfully
[[22:35:59]] [INFO] 83tV9A4NOn=running
[[22:35:59]] [INFO] Executing action 64/351: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[22:35:59]] [SUCCESS] Screenshot refreshed
[[22:35:59]] [INFO] Refreshing screenshot...
[[22:35:59]] [INFO] YbamBpASJi=pass
[[22:35:55]] [SUCCESS] Screenshot refreshed successfully
[[22:35:55]] [SUCCESS] Screenshot refreshed successfully
[[22:35:55]] [INFO] YbamBpASJi=running
[[22:35:55]] [INFO] Executing action 63/351: Tap on image: env[product-share-img]
[[22:35:54]] [SUCCESS] Screenshot refreshed
[[22:35:54]] [INFO] Refreshing screenshot...
[[22:35:54]] [INFO] zWrzEgdH3Q=pass
[[22:35:50]] [SUCCESS] Screenshot refreshed successfully
[[22:35:50]] [SUCCESS] Screenshot refreshed successfully
[[22:35:50]] [INFO] zWrzEgdH3Q=running
[[22:35:50]] [INFO] Executing action 62/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[22:35:50]] [SUCCESS] Screenshot refreshed
[[22:35:50]] [INFO] Refreshing screenshot...
[[22:35:50]] [INFO] kAQ1yIIw3h=pass
[[22:35:46]] [SUCCESS] Screenshot refreshed successfully
[[22:35:46]] [SUCCESS] Screenshot refreshed successfully
[[22:35:46]] [INFO] kAQ1yIIw3h=running
[[22:35:46]] [INFO] Executing action 61/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[22:35:45]] [SUCCESS] Screenshot refreshed
[[22:35:45]] [INFO] Refreshing screenshot...
[[22:35:45]] [INFO] OmKfD9iBjD=pass
[[22:35:42]] [SUCCESS] Screenshot refreshed successfully
[[22:35:42]] [SUCCESS] Screenshot refreshed successfully
[[22:35:42]] [INFO] OmKfD9iBjD=running
[[22:35:42]] [INFO] Executing action 60/351: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[22:35:41]] [SUCCESS] Screenshot refreshed
[[22:35:41]] [INFO] Refreshing screenshot...
[[22:35:41]] [INFO] eHLWiRoqqS=pass
[[22:35:37]] [SUCCESS] Screenshot refreshed successfully
[[22:35:37]] [SUCCESS] Screenshot refreshed successfully
[[22:35:37]] [INFO] eHLWiRoqqS=running
[[22:35:37]] [INFO] Executing action 59/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:35:36]] [SUCCESS] Screenshot refreshed
[[22:35:36]] [INFO] Refreshing screenshot...
[[22:35:36]] [INFO] xUbWFa8Ok2=pass
[[22:35:32]] [SUCCESS] Screenshot refreshed successfully
[[22:35:32]] [SUCCESS] Screenshot refreshed successfully
[[22:35:32]] [INFO] xUbWFa8Ok2=running
[[22:35:32]] [INFO] Executing action 58/351: Tap on Text: "Latest"
[[22:35:31]] [SUCCESS] Screenshot refreshed
[[22:35:31]] [INFO] Refreshing screenshot...
[[22:35:31]] [INFO] RbNtEW6N9T=pass
[[22:35:27]] [SUCCESS] Screenshot refreshed successfully
[[22:35:27]] [SUCCESS] Screenshot refreshed successfully
[[22:35:27]] [INFO] RbNtEW6N9T=running
[[22:35:27]] [INFO] Executing action 57/351: Tap on Text: "Toys"
[[22:35:26]] [SUCCESS] Screenshot refreshed
[[22:35:26]] [INFO] Refreshing screenshot...
[[22:35:26]] [INFO] eHvkAVake5=pass
[[22:35:23]] [SUCCESS] Screenshot refreshed successfully
[[22:35:23]] [SUCCESS] Screenshot refreshed successfully
[[22:35:23]] [INFO] eHvkAVake5=running
[[22:35:23]] [INFO] Executing action 56/351: Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"]
[[22:35:23]] [SUCCESS] Screenshot refreshed
[[22:35:23]] [INFO] Refreshing screenshot...
[[22:35:23]] [INFO] F4NGh9HrLw=pass
[[22:35:19]] [SUCCESS] Screenshot refreshed successfully
[[22:35:19]] [SUCCESS] Screenshot refreshed successfully
[[22:35:19]] [INFO] F4NGh9HrLw=running
[[22:35:19]] [INFO] Executing action 55/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[22:35:18]] [SUCCESS] Screenshot refreshed
[[22:35:18]] [INFO] Refreshing screenshot...
[[22:35:18]] [INFO] H9fy9qcFbZ=pass
[[22:35:04]] [SUCCESS] Screenshot refreshed successfully
[[22:35:04]] [SUCCESS] Screenshot refreshed successfully
[[22:35:03]] [INFO] H9fy9qcFbZ=running
[[22:35:03]] [INFO] Executing action 54/351: Restart app: env[appid]
[[22:35:03]] [SUCCESS] Screenshot refreshed
[[22:35:03]] [INFO] Refreshing screenshot...
[[22:35:02]] [SUCCESS] Screenshot refreshed
[[22:35:02]] [INFO] Refreshing screenshot...
[[22:35:01]] [SUCCESS] Screenshot refreshed successfully
[[22:35:01]] [SUCCESS] Screenshot refreshed successfully
[[22:35:00]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[22:35:00]] [SUCCESS] Screenshot refreshed
[[22:35:00]] [INFO] Refreshing screenshot...
[[22:34:55]] [SUCCESS] Screenshot refreshed successfully
[[22:34:55]] [SUCCESS] Screenshot refreshed successfully
[[22:34:55]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[22:34:54]] [SUCCESS] Screenshot refreshed
[[22:34:54]] [INFO] Refreshing screenshot...
[[22:34:51]] [SUCCESS] Screenshot refreshed successfully
[[22:34:51]] [SUCCESS] Screenshot refreshed successfully
[[22:34:51]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[22:34:50]] [SUCCESS] Screenshot refreshed
[[22:34:50]] [INFO] Refreshing screenshot...
[[22:34:47]] [SUCCESS] Screenshot refreshed successfully
[[22:34:47]] [SUCCESS] Screenshot refreshed successfully
[[22:34:46]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[22:34:46]] [SUCCESS] Screenshot refreshed
[[22:34:46]] [INFO] Refreshing screenshot...
[[22:34:39]] [SUCCESS] Screenshot refreshed successfully
[[22:34:39]] [SUCCESS] Screenshot refreshed successfully
[[22:34:39]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[22:34:38]] [SUCCESS] Screenshot refreshed
[[22:34:38]] [INFO] Refreshing screenshot...
[[22:34:31]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[22:34:31]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[22:34:31]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[22:34:31]] [INFO] rt1xZqSe89=running
[[22:34:31]] [INFO] Executing action 53/351: cleanupSteps action
[[22:34:31]] [INFO] Skipping remaining steps in failed test case (moving from action 47 to 52), but preserving cleanup steps
[[22:34:31]] [INFO] I4gwigwXSj=fail
[[22:34:31]] [ERROR] Action 47 failed: Element with xpath '//XCUIElementTypeStaticText[@name="Continue shopping"]' not found within timeout of 30.0 seconds
[[22:33:59]] [SUCCESS] Screenshot refreshed successfully
[[22:33:59]] [SUCCESS] Screenshot refreshed successfully
[[22:33:59]] [INFO] I4gwigwXSj=running
[[22:33:59]] [INFO] Executing action 47/351: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[22:33:58]] [SUCCESS] Screenshot refreshed
[[22:33:58]] [INFO] Refreshing screenshot...
[[22:33:58]] [INFO] eVytJrry9x=pass
[[22:33:54]] [SUCCESS] Screenshot refreshed successfully
[[22:33:54]] [SUCCESS] Screenshot refreshed successfully
[[22:33:54]] [INFO] eVytJrry9x=running
[[22:33:54]] [INFO] Executing action 46/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[22:33:54]] [SUCCESS] Screenshot refreshed
[[22:33:54]] [INFO] Refreshing screenshot...
[[22:33:54]] [INFO] s8h8VDUIOC=pass
[[22:33:49]] [SUCCESS] Screenshot refreshed successfully
[[22:33:49]] [SUCCESS] Screenshot refreshed successfully
[[22:33:49]] [INFO] s8h8VDUIOC=running
[[22:33:49]] [INFO] Executing action 45/351: Swipe from (50%, 70%) to (50%, 30%)
[[22:33:48]] [SUCCESS] Screenshot refreshed
[[22:33:48]] [INFO] Refreshing screenshot...
[[22:33:48]] [INFO] bkU728TrRF=pass
[[22:33:42]] [INFO] bkU728TrRF=running
[[22:33:42]] [INFO] Executing action 44/351: Tap on element with accessibility_id: Done
[[22:33:42]] [SUCCESS] Screenshot refreshed successfully
[[22:33:42]] [SUCCESS] Screenshot refreshed successfully
[[22:33:42]] [SUCCESS] Screenshot refreshed
[[22:33:42]] [INFO] Refreshing screenshot...
[[22:33:42]] [INFO] ZWpYNcpbFA=pass
[[22:33:37]] [SUCCESS] Screenshot refreshed successfully
[[22:33:37]] [SUCCESS] Screenshot refreshed successfully
[[22:33:37]] [INFO] ZWpYNcpbFA=running
[[22:33:37]] [INFO] Executing action 43/351: Tap on Text: "AUCKLAND"
[[22:33:36]] [SUCCESS] Screenshot refreshed
[[22:33:36]] [INFO] Refreshing screenshot...
[[22:33:36]] [INFO] KOLK6CUomC=pass
[[22:33:30]] [SUCCESS] Screenshot refreshed successfully
[[22:33:30]] [SUCCESS] Screenshot refreshed successfully
[[22:33:30]] [INFO] KOLK6CUomC=running
[[22:33:30]] [INFO] Executing action 42/351: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "0616"
[[22:33:29]] [SUCCESS] Screenshot refreshed
[[22:33:29]] [INFO] Refreshing screenshot...
[[22:33:29]] [INFO] QpBLC6BStn=pass
[[22:33:23]] [SUCCESS] Screenshot refreshed successfully
[[22:33:23]] [SUCCESS] Screenshot refreshed successfully
[[22:33:23]] [INFO] QpBLC6BStn=running
[[22:33:23]] [INFO] Executing action 41/351: Tap on element with accessibility_id: delete
[[22:33:23]] [SUCCESS] Screenshot refreshed
[[22:33:23]] [INFO] Refreshing screenshot...
[[22:33:23]] [INFO] G4A3KBlXHq=pass
[[22:33:18]] [SUCCESS] Screenshot refreshed successfully
[[22:33:18]] [SUCCESS] Screenshot refreshed successfully
[[22:33:18]] [INFO] G4A3KBlXHq=running
[[22:33:18]] [INFO] Executing action 40/351: Tap on Text: "Nearby"
[[22:33:17]] [SUCCESS] Screenshot refreshed
[[22:33:17]] [INFO] Refreshing screenshot...
[[22:33:17]] [INFO] uArzgeZYf7=pass
[[22:33:14]] [SUCCESS] Screenshot refreshed successfully
[[22:33:14]] [SUCCESS] Screenshot refreshed successfully
[[22:33:14]] [INFO] uArzgeZYf7=running
[[22:33:14]] [INFO] Executing action 39/351: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[22:33:13]] [SUCCESS] Screenshot refreshed
[[22:33:13]] [INFO] Refreshing screenshot...
[[22:33:13]] [INFO] 3gJsiap2Ds=pass
[[22:33:08]] [SUCCESS] Screenshot refreshed successfully
[[22:33:08]] [SUCCESS] Screenshot refreshed successfully
[[22:33:08]] [INFO] 3gJsiap2Ds=running
[[22:33:08]] [INFO] Executing action 38/351: Tap on Text: "Collect"
[[22:33:08]] [SUCCESS] Screenshot refreshed
[[22:33:08]] [INFO] Refreshing screenshot...
[[22:33:08]] [INFO] dF3hpprg71=pass
[[22:33:05]] [SUCCESS] Screenshot refreshed successfully
[[22:33:05]] [SUCCESS] Screenshot refreshed successfully
[[22:33:04]] [INFO] dF3hpprg71=running
[[22:33:04]] [INFO] Executing action 37/351: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[22:33:04]] [SUCCESS] Screenshot refreshed
[[22:33:04]] [INFO] Refreshing screenshot...
[[22:33:04]] [INFO] 94ikwhIEE2=pass
[[22:33:01]] [SUCCESS] Screenshot refreshed successfully
[[22:33:01]] [SUCCESS] Screenshot refreshed successfully
[[22:33:00]] [INFO] 94ikwhIEE2=running
[[22:33:00]] [INFO] Executing action 36/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[22:33:00]] [SUCCESS] Screenshot refreshed
[[22:33:00]] [INFO] Refreshing screenshot...
[[22:33:00]] [INFO] q8oldD8uZt=pass
[[22:32:56]] [SUCCESS] Screenshot refreshed successfully
[[22:32:56]] [SUCCESS] Screenshot refreshed successfully
[[22:32:56]] [INFO] q8oldD8uZt=running
[[22:32:56]] [INFO] Executing action 35/351: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[22:32:56]] [SUCCESS] Screenshot refreshed
[[22:32:56]] [INFO] Refreshing screenshot...
[[22:32:56]] [INFO] Jf2wJyOphY=pass
[[22:32:48]] [SUCCESS] Screenshot refreshed successfully
[[22:32:48]] [SUCCESS] Screenshot refreshed successfully
[[22:32:48]] [INFO] Jf2wJyOphY=running
[[22:32:48]] [INFO] Executing action 34/351: Tap on element with accessibility_id: Add to bag
[[22:32:48]] [SUCCESS] Screenshot refreshed
[[22:32:48]] [INFO] Refreshing screenshot...
[[22:32:48]] [INFO] b06OvcntcY=pass
[[22:32:35]] [SUCCESS] Screenshot refreshed successfully
[[22:32:35]] [SUCCESS] Screenshot refreshed successfully
[[22:32:34]] [INFO] b06OvcntcY=running
[[22:32:34]] [INFO] Executing action 33/351: Check if element with text="Papanui" exists
[[22:32:34]] [SUCCESS] Screenshot refreshed
[[22:32:34]] [INFO] Refreshing screenshot...
[[22:32:34]] [INFO] ORI6ZFMBK1=pass
[[22:32:29]] [SUCCESS] Screenshot refreshed successfully
[[22:32:29]] [SUCCESS] Screenshot refreshed successfully
[[22:32:29]] [INFO] ORI6ZFMBK1=running
[[22:32:29]] [INFO] Executing action 32/351: Tap on Text: "Save"
[[22:32:29]] [SUCCESS] Screenshot refreshed
[[22:32:29]] [INFO] Refreshing screenshot...
[[22:32:29]] [INFO] hr0IVckpYI=pass
[[22:32:24]] [SUCCESS] Screenshot refreshed successfully
[[22:32:24]] [SUCCESS] Screenshot refreshed successfully
[[22:32:24]] [INFO] hr0IVckpYI=running
[[22:32:24]] [INFO] Executing action 31/351: Wait till accessibility_id=btnSaveOrContinue
[[22:32:24]] [SUCCESS] Screenshot refreshed
[[22:32:24]] [INFO] Refreshing screenshot...
[[22:32:24]] [INFO] H0ODFz7sWJ=pass
[[22:32:19]] [SUCCESS] Screenshot refreshed successfully
[[22:32:19]] [SUCCESS] Screenshot refreshed successfully
[[22:32:19]] [INFO] H0ODFz7sWJ=running
[[22:32:19]] [INFO] Executing action 30/351: Tap on Text: "8053"
[[22:32:18]] [SUCCESS] Screenshot refreshed
[[22:32:18]] [INFO] Refreshing screenshot...
[[22:32:18]] [INFO] uZHvvAzVfx=pass
[[22:32:13]] [SUCCESS] Screenshot refreshed successfully
[[22:32:13]] [SUCCESS] Screenshot refreshed successfully
[[22:32:13]] [INFO] uZHvvAzVfx=running
[[22:32:13]] [INFO] Executing action 29/351: textClear action
[[22:32:13]] [SUCCESS] Screenshot refreshed
[[22:32:13]] [INFO] Refreshing screenshot...
[[22:32:13]] [INFO] WmNWcsWVHv=pass
[[22:32:08]] [SUCCESS] Screenshot refreshed successfully
[[22:32:08]] [SUCCESS] Screenshot refreshed successfully
[[22:32:07]] [INFO] WmNWcsWVHv=running
[[22:32:07]] [INFO] Executing action 28/351: Tap on element with accessibility_id: Search suburb or postcode
[[22:32:07]] [SUCCESS] Screenshot refreshed
[[22:32:07]] [INFO] Refreshing screenshot...
[[22:32:07]] [INFO] lnjoz8hHUU=pass
[[22:32:02]] [SUCCESS] Screenshot refreshed successfully
[[22:32:02]] [SUCCESS] Screenshot refreshed successfully
[[22:32:02]] [INFO] lnjoz8hHUU=running
[[22:32:02]] [INFO] Executing action 27/351: Tap on Text: "Edit"
[[22:32:01]] [SUCCESS] Screenshot refreshed
[[22:32:01]] [INFO] Refreshing screenshot...
[[22:32:01]] [INFO] letbbewlnA=pass
[[22:31:58]] [SUCCESS] Screenshot refreshed successfully
[[22:31:58]] [SUCCESS] Screenshot refreshed successfully
[[22:31:57]] [INFO] letbbewlnA=running
[[22:31:57]] [INFO] Executing action 26/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[22:31:57]] [SUCCESS] Screenshot refreshed
[[22:31:57]] [INFO] Refreshing screenshot...
[[22:31:57]] [INFO] trBISwJ8eZ=pass
[[22:31:53]] [SUCCESS] Screenshot refreshed successfully
[[22:31:53]] [SUCCESS] Screenshot refreshed successfully
[[22:31:52]] [INFO] trBISwJ8eZ=running
[[22:31:52]] [INFO] Executing action 25/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[22:31:52]] [SUCCESS] Screenshot refreshed
[[22:31:52]] [INFO] Refreshing screenshot...
[[22:31:52]] [INFO] 73NABkfWyY=pass
[[22:31:37]] [SUCCESS] Screenshot refreshed successfully
[[22:31:37]] [SUCCESS] Screenshot refreshed successfully
[[22:31:36]] [INFO] 73NABkfWyY=running
[[22:31:36]] [INFO] Executing action 24/351: Check if element with text="Henderson" exists
[[22:31:36]] [SUCCESS] Screenshot refreshed
[[22:31:36]] [INFO] Refreshing screenshot...
[[22:31:36]] [INFO] pKjXoj4mNg=pass
[[22:31:31]] [SUCCESS] Screenshot refreshed successfully
[[22:31:31]] [SUCCESS] Screenshot refreshed successfully
[[22:31:31]] [INFO] pKjXoj4mNg=running
[[22:31:31]] [INFO] Executing action 23/351: Tap on Text: "Save"
[[22:31:30]] [SUCCESS] Screenshot refreshed
[[22:31:30]] [INFO] Refreshing screenshot...
[[22:31:30]] [INFO] M3dXqigqRv=pass
[[22:31:26]] [SUCCESS] Screenshot refreshed successfully
[[22:31:26]] [SUCCESS] Screenshot refreshed successfully
[[22:31:26]] [INFO] M3dXqigqRv=running
[[22:31:26]] [INFO] Executing action 22/351: Wait till accessibility_id=btnSaveOrContinue
[[22:31:25]] [SUCCESS] Screenshot refreshed
[[22:31:25]] [INFO] Refreshing screenshot...
[[22:31:25]] [INFO] GYRHQr7TWx=pass
[[22:31:21]] [SUCCESS] Screenshot refreshed successfully
[[22:31:21]] [SUCCESS] Screenshot refreshed successfully
[[22:31:21]] [INFO] GYRHQr7TWx=running
[[22:31:21]] [INFO] Executing action 21/351: Tap on Text: "0616"
[[22:31:20]] [SUCCESS] Screenshot refreshed
[[22:31:20]] [INFO] Refreshing screenshot...
[[22:31:20]] [INFO] kbdEPCPYod=pass
[[22:31:15]] [SUCCESS] Screenshot refreshed successfully
[[22:31:15]] [SUCCESS] Screenshot refreshed successfully
[[22:31:15]] [INFO] kbdEPCPYod=running
[[22:31:15]] [INFO] Executing action 20/351: textClear action
[[22:31:15]] [SUCCESS] Screenshot refreshed
[[22:31:15]] [INFO] Refreshing screenshot...
[[22:31:15]] [INFO] 8WCusTZ8q9=pass
[[22:31:09]] [SUCCESS] Screenshot refreshed successfully
[[22:31:09]] [SUCCESS] Screenshot refreshed successfully
[[22:31:09]] [INFO] 8WCusTZ8q9=running
[[22:31:09]] [INFO] Executing action 19/351: Tap on element with accessibility_id: Search suburb or postcode
[[22:31:09]] [SUCCESS] Screenshot refreshed
[[22:31:09]] [INFO] Refreshing screenshot...
[[22:31:09]] [INFO] VkUKQbf1Qt=pass
[[22:31:04]] [SUCCESS] Screenshot refreshed successfully
[[22:31:04]] [SUCCESS] Screenshot refreshed successfully
[[22:31:04]] [INFO] VkUKQbf1Qt=running
[[22:31:04]] [INFO] Executing action 18/351: Tap on Text: "Edit"
[[22:31:03]] [SUCCESS] Screenshot refreshed
[[22:31:03]] [INFO] Refreshing screenshot...
[[22:31:03]] [INFO] C6JHhLdWTv=pass
[[22:31:00]] [SUCCESS] Screenshot refreshed successfully
[[22:31:00]] [SUCCESS] Screenshot refreshed successfully
[[22:30:59]] [INFO] C6JHhLdWTv=running
[[22:30:59]] [INFO] Executing action 17/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[22:30:59]] [SUCCESS] Screenshot refreshed
[[22:30:59]] [INFO] Refreshing screenshot...
[[22:30:59]] [INFO] IupxLP2Jsr=pass
[[22:30:54]] [SUCCESS] Screenshot refreshed successfully
[[22:30:54]] [SUCCESS] Screenshot refreshed successfully
[[22:30:54]] [INFO] IupxLP2Jsr=running
[[22:30:54]] [INFO] Executing action 16/351: iOS Function: text - Text: "Uno card"
[[22:30:54]] [SUCCESS] Screenshot refreshed
[[22:30:54]] [INFO] Refreshing screenshot...
[[22:30:54]] [INFO] 70iOOakiG7=pass
[[22:30:49]] [SUCCESS] Screenshot refreshed successfully
[[22:30:49]] [SUCCESS] Screenshot refreshed successfully
[[22:30:49]] [INFO] 70iOOakiG7=running
[[22:30:49]] [INFO] Executing action 15/351: Tap on Text: "Find"
[[22:30:48]] [SUCCESS] Screenshot refreshed
[[22:30:48]] [INFO] Refreshing screenshot...
[[22:30:48]] [INFO] b06OvcntcY=pass
[[22:30:29]] [SUCCESS] Screenshot refreshed successfully
[[22:30:29]] [SUCCESS] Screenshot refreshed successfully
[[22:30:28]] [INFO] b06OvcntcY=running
[[22:30:28]] [INFO] Executing action 14/351: Check if element with text="Papanui" exists
[[22:30:28]] [SUCCESS] Screenshot refreshed
[[22:30:28]] [INFO] Refreshing screenshot...
[[22:30:28]] [INFO] Xqj9EIVEfg=pass
[[22:30:15]] [SUCCESS] Screenshot refreshed successfully
[[22:30:15]] [SUCCESS] Screenshot refreshed successfully
[[22:30:15]] [INFO] Xqj9EIVEfg=running
[[22:30:15]] [INFO] Executing action 13/351: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[22:30:15]] [SUCCESS] Screenshot refreshed
[[22:30:15]] [INFO] Refreshing screenshot...
[[22:30:15]] [INFO] E2jpN7BioW=pass
[[22:30:10]] [SUCCESS] Screenshot refreshed successfully
[[22:30:10]] [SUCCESS] Screenshot refreshed successfully
[[22:30:09]] [INFO] E2jpN7BioW=running
[[22:30:09]] [INFO] Executing action 12/351: Tap on Text: "Save"
[[22:30:09]] [SUCCESS] Screenshot refreshed
[[22:30:09]] [INFO] Refreshing screenshot...
[[22:30:09]] [INFO] Sl6eiqZkRm=pass
[[22:30:05]] [SUCCESS] Screenshot refreshed successfully
[[22:30:05]] [SUCCESS] Screenshot refreshed successfully
[[22:30:04]] [INFO] Sl6eiqZkRm=running
[[22:30:04]] [INFO] Executing action 11/351: Wait till accessibility_id=btnSaveOrContinue
[[22:30:04]] [SUCCESS] Screenshot refreshed
[[22:30:04]] [INFO] Refreshing screenshot...
[[22:30:04]] [INFO] mw9GQ4mzRE=pass
[[22:29:59]] [SUCCESS] Screenshot refreshed successfully
[[22:29:59]] [SUCCESS] Screenshot refreshed successfully
[[22:29:59]] [INFO] mw9GQ4mzRE=running
[[22:29:59]] [INFO] Executing action 10/351: Tap on Text: "8053"
[[22:29:59]] [SUCCESS] Screenshot refreshed
[[22:29:59]] [INFO] Refreshing screenshot...
[[22:29:59]] [INFO] kbdEPCPYod=pass
[[22:29:54]] [SUCCESS] Screenshot refreshed successfully
[[22:29:54]] [SUCCESS] Screenshot refreshed successfully
[[22:29:54]] [INFO] kbdEPCPYod=running
[[22:29:54]] [INFO] Executing action 9/351: textClear action
[[22:29:53]] [SUCCESS] Screenshot refreshed
[[22:29:53]] [INFO] Refreshing screenshot...
[[22:29:53]] [INFO] 8WCusTZ8q9=pass
[[22:29:48]] [SUCCESS] Screenshot refreshed successfully
[[22:29:48]] [SUCCESS] Screenshot refreshed successfully
[[22:29:47]] [INFO] 8WCusTZ8q9=running
[[22:29:47]] [INFO] Executing action 8/351: Tap on element with accessibility_id: Search suburb or postcode
[[22:29:47]] [SUCCESS] Screenshot refreshed
[[22:29:47]] [INFO] Refreshing screenshot...
[[22:29:47]] [INFO] QMXBlswP6H=pass
[[22:29:44]] [SUCCESS] Screenshot refreshed successfully
[[22:29:44]] [SUCCESS] Screenshot refreshed successfully
[[22:29:43]] [INFO] QMXBlswP6H=running
[[22:29:43]] [INFO] Executing action 7/351: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[22:29:43]] [SUCCESS] Screenshot refreshed
[[22:29:43]] [INFO] Refreshing screenshot...
[[22:29:43]] [INFO] m0956RsrdM=pass
[[22:29:41]] [SUCCESS] Screenshot refreshed successfully
[[22:29:41]] [SUCCESS] Screenshot refreshed successfully
[[22:29:39]] [INFO] m0956RsrdM=running
[[22:29:39]] [INFO] Executing action 6/351: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[22:29:39]] [SUCCESS] Screenshot refreshed
[[22:29:39]] [INFO] Refreshing screenshot...
[[22:29:39]] [SUCCESS] Screenshot refreshed
[[22:29:39]] [INFO] Refreshing screenshot...
[[22:29:34]] [SUCCESS] Screenshot refreshed successfully
[[22:29:34]] [SUCCESS] Screenshot refreshed successfully
[[22:29:34]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[22:29:33]] [SUCCESS] Screenshot refreshed
[[22:29:33]] [INFO] Refreshing screenshot...
[[22:29:29]] [SUCCESS] Screenshot refreshed successfully
[[22:29:29]] [SUCCESS] Screenshot refreshed successfully
[[22:29:29]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[22:29:29]] [SUCCESS] Screenshot refreshed
[[22:29:29]] [INFO] Refreshing screenshot...
[[22:29:24]] [SUCCESS] Screenshot refreshed successfully
[[22:29:24]] [SUCCESS] Screenshot refreshed successfully
[[22:29:24]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[22:29:23]] [SUCCESS] Screenshot refreshed
[[22:29:23]] [INFO] Refreshing screenshot...
[[22:29:19]] [SUCCESS] Screenshot refreshed successfully
[[22:29:19]] [SUCCESS] Screenshot refreshed successfully
[[22:29:19]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[22:29:18]] [SUCCESS] Screenshot refreshed
[[22:29:18]] [INFO] Refreshing screenshot...
[[22:29:13]] [SUCCESS] Screenshot refreshed successfully
[[22:29:13]] [SUCCESS] Screenshot refreshed successfully
[[22:29:13]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:29:13]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[22:29:13]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[22:29:13]] [INFO] kBjnmRXZVq=running
[[22:29:13]] [INFO] Executing action 5/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[22:29:12]] [SUCCESS] Screenshot refreshed
[[22:29:12]] [INFO] Refreshing screenshot...
[[22:29:12]] [INFO] Azb1flbIJJ=pass
[[22:29:09]] [SUCCESS] Screenshot refreshed successfully
[[22:29:09]] [SUCCESS] Screenshot refreshed successfully
[[22:29:09]] [INFO] Azb1flbIJJ=running
[[22:29:09]] [INFO] Executing action 4/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[22:29:08]] [SUCCESS] Screenshot refreshed
[[22:29:08]] [INFO] Refreshing screenshot...
[[22:29:08]] [INFO] 2xC5fLfLe8=pass
[[22:29:06]] [SUCCESS] Screenshot refreshed successfully
[[22:29:06]] [SUCCESS] Screenshot refreshed successfully
[[22:29:05]] [INFO] 2xC5fLfLe8=running
[[22:29:05]] [INFO] Executing action 3/351: iOS Function: alert_accept
[[22:29:05]] [SUCCESS] Screenshot refreshed
[[22:29:05]] [INFO] Refreshing screenshot...
[[22:29:05]] [INFO] Y8vz7AJD1i=pass
[[22:28:55]] [SUCCESS] Screenshot refreshed successfully
[[22:28:55]] [SUCCESS] Screenshot refreshed successfully
[[22:28:55]] [INFO] Y8vz7AJD1i=running
[[22:28:55]] [INFO] Executing action 2/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:28:54]] [SUCCESS] Screenshot refreshed
[[22:28:54]] [INFO] Refreshing screenshot...
[[22:28:54]] [INFO] H9fy9qcFbZ=pass
[[22:28:49]] [INFO] H9fy9qcFbZ=running
[[22:28:49]] [INFO] Executing action 1/351: Restart app: env[appid]
[[22:28:49]] [INFO] ExecutionManager: Starting execution of 351 actions...
[[22:28:49]] [SUCCESS] Cleared 1 screenshots from database
[[22:28:49]] [INFO] Clearing screenshots from database before execution...
[[22:28:49]] [SUCCESS] All screenshots deleted successfully
[[22:28:49]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[22:28:49]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250628_222849/screenshots
[[22:28:49]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250628_222849
[[22:28:49]] [SUCCESS] Report directory initialized successfully
[[22:28:49]] [INFO] Initializing report directory and screenshots folder for test suite...
[[22:28:45]] [INFO] Collapsed all test cases
[[22:28:42]] [SUCCESS] All screenshots deleted successfully
[[22:28:42]] [INFO] All actions cleared
[[22:28:42]] [INFO] Cleaning up screenshots...
[[22:28:29]] [SUCCESS] Screenshot refreshed successfully
[[22:28:29]] [SUCCESS] Screenshot refreshed
[[22:28:29]] [INFO] Refreshing screenshot...
[[22:28:28]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[22:28:28]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[22:28:25]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[22:28:19]] [SUCCESS] Found 1 device(s)
[[22:28:18]] [INFO] Refreshing device list...
