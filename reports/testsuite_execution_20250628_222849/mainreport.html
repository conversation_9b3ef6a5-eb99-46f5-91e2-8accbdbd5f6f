<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/28/2025, 11:13:53 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">6</span> passed,
                <span class="failed-count">3</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 28/06/2025, 23:13:53
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="53 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Postcode Flow_NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            53 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3346ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">8086ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1151ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1956ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1749ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2118ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4329ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                textClear action <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">3248ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">3171ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2859ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3117ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate" <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">10915ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">17641ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3868ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2405ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1783ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">3545ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3724ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                textClear action <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">3109ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "0616" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">3114ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2850ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-0-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3134ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-0-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">13479ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2256ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2003ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="b06OvcntcY.png" data-action-id="b06OvcntcY" onclick="showStepDetails('step-0-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: b06OvcntcY">b06OvcntcY</span>
                            </div>
                            <span class="test-step-duration">3770ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3691ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-0-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                textClear action <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">3247ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-0-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">3603ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2879ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-0-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">3119ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-0-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">11165ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5517ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1565ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2467ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Delivery"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1689ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-0-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">3188ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1790ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-0-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">3458ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: delete <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4348ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="passed"
                            data-screenshot="M3dXqigqRv.png" data-action-id="M3dXqigqRv" onclick="showStepDetails('step-0-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "0616" <span class="action-id-badge" title="Action ID: M3dXqigqRv">M3dXqigqRv</span>
                            </div>
                            <span class="test-step-duration">5228ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="passed"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-0-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">3372ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Done <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4254ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="passed"
                            data-screenshot="pKjXoj4mNg.png" data-action-id="pKjXoj4mNg" onclick="showStepDetails('step-0-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: pKjXoj4mNg">pKjXoj4mNg</span>
                            </div>
                            <span class="test-step-duration">3392ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2217ms</span>
                        </li>
                        <li class="test-step" data-step-id="47" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-46')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1501ms</span>
                        </li>
                        <li class="test-step" data-step-id="48" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-47')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1909ms</span>
                        </li>
                        <li class="test-step" data-step-id="49" data-status="unknown"
                            data-screenshot="OKCHAK6HCJ.png" data-action-id="OKCHAK6HCJ" onclick="showStepDetails('step-0-48')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: OKCHAK6HCJ">OKCHAK6HCJ</span>
                            </div>
                            <span class="test-step-duration">30448ms</span>
                        </li>
                        <li class="test-step" data-step-id="50" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-49')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2451ms</span>
                        </li>
                        <li class="test-step" data-step-id="51" data-status="unknown"
                            data-screenshot="3hOTINBVMf.png" data-action-id="3hOTINBVMf" onclick="showStepDetails('step-0-50')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 3hOTINBVMf">3hOTINBVMf</span>
                            </div>
                            <span class="test-step-duration">2606ms</span>
                        </li>
                        <li class="test-step" data-step-id="52" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-51')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2052ms</span>
                        </li>
                        <li class="test-step" data-step-id="53" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-52')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="37 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Browse & PDP NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            37 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3292ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2712ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1459ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">2715ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Latest" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">2776ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2784ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1779ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2262ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1828ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-1-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[product-share-img] <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2315ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1701ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-1-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "product-share-logo.png" exists on screen <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">11501ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-1-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3054ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2121ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-1-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 50%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3640ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-1-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "more" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">3188ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2142ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1965ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-1-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "Kid toy" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2412ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-1-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "search-result-test-se.png" exists on screen <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">21438ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-1-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2314ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-1-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">2231ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1977ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1308ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1296ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-1-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2282ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="b06OvcntcY.png" data-action-id="b06OvcntcY" onclick="showStepDetails('step-1-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: b06OvcntcY">b06OvcntcY</span>
                            </div>
                            <span class="test-step-duration">3214ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-1-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">3996ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-1-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">2365ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1813ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2282ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2292ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1641ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2082ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2062ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-1-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: env[appid] <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">1062ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-1-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="27 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 Delivery & CNC- NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            27 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3210ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5475ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1146ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1920ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">4236ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2393ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1623ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2082ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1895ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2075ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-2-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "cnc-tab-se.png" exists on screen <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">11177ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-2-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3086ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-2-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2845ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2166ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-2-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2223ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2091ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-2-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">5123ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2062ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2120ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-2-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">3863ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-2-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2405ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1631ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2095ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2095ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-2-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Delivery Buy Step NZ (33 steps) <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-2-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #4 NZ- MyAccount
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-3-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3224ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-3-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">5018ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4812ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-3-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">1156ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-3-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4089ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1598ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1590ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2080ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-3-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">5021ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2166ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">5420ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2075ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Print order details <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3878ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2411ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-3-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">2951ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-3-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Return" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3141ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2861ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2092ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1587ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-3-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "details" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2850ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-3-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2196ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-3-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "address" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2828ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-3-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2223ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-3-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "payment" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2778ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-3-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">2138ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-3-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2142ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="b06OvcntcY.png" data-action-id="b06OvcntcY" onclick="showStepDetails('step-3-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: b06OvcntcY">b06OvcntcY</span>
                            </div>
                            <span class="test-step-duration">2762ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-3-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "locator" <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">2770ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-3-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">3466ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">20180ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-3-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap and Type at (29, 262): "0616" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">5236ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-3-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">3447ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2349ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-3-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">2251ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-3-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Customer" <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">2764ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="M3dXqigqRv.png" data-action-id="M3dXqigqRv" onclick="showStepDetails('step-3-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: M3dXqigqRv">M3dXqigqRv</span>
                            </div>
                            <span class="test-step-duration">2189ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-3-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">2777ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-3-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="28 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #5 All Sign ins NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-4-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3213ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-4-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4839ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-4-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1181ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1859ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-4-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2146ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-4-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2604ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2085ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2085ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1950ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-4-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">1220ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1937ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-4-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2020ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1612ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2055ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-4-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">2702ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2087ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-4-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3216ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2164ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2716ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-4-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">1156ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-4-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2149ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-4-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2630ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2166ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-4-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">5019ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-4-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #6 Kmart-Prod Sign in NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-5-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3215ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4888ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-5-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1148ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2660ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2445ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-5-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">3017ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2456ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-5-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2775ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1285ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2079ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4351ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2068ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">1768ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3789ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-5-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">1139ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1905ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-5-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Apple" <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3197ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-5-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 10 ms <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">10021ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-5-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Passcode" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">1980ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="5"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1673ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="9"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1588ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="1"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1913ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="2"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1596ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="3"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1587ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="4"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1596ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">5708ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2083ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4958ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2078ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">1776ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3781ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-5-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">1158ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1946ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-5-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Google" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">3136ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2624ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2591ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2075ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4362ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2077ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-5-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">5017ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-5-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="46 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #7 WishList NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            46 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-6-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3198ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-6-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5481ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-6-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1168ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1919ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-6-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-6-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">3913ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-6-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2428ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1739ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1733ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2937ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2007ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2428ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-6-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">2501ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2226ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1984ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2107ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-6-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">2061ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2222ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1695ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3591ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3187ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1345ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1953ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-6-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-6-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Move" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2732ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1578ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-6-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-6-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2559ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1936ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1976ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2423ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4445ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1647ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2782ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-6-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2175ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2467ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3208ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-6-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">2741ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3204ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-6-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2930ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1952ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="unknown"
                            data-screenshot="b06OvcntcY.png" data-action-id="b06OvcntcY" onclick="showStepDetails('step-6-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: b06OvcntcY">b06OvcntcY</span>
                            </div>
                            <span class="test-step-duration">2605ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2046ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="unknown"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-6-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 10%) <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">2129ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-6-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #8 App Settings NZ
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-7-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3207ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2094ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-7-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1177ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-7-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1227ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1242ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-7-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Wi-Fi" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2936ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">941ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-7-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">3220ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">259ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">804ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">227ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">666ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">213ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">631ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">193ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">123ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">768ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-7-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">5022ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-7-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3270ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2058ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-7-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2620ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-7-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2898ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="mobilesafa.png" data-action-id="mobilesafa" onclick="showStepDetails('step-7-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.apple.mobilesafari <span class="action-id-badge" title="Action ID: mobilesafa">mobilesafa</span>
                            </div>
                            <span class="test-step-duration">3228ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1809ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-7-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "kmart nz" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2281ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">724ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-7-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-7-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-7-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-7-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-7-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #9 NZ- Performance
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-8-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3307ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-8-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">3343ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-8-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43515028" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">2492ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2273ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-8-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">15017ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">13740ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-8-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Done" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">34656ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-8-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3242ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2032ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-8-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">4030ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-8-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "kids toys" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2381ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="Pagination.png" data-action-id="Pagination" onclick="showStepDetails('step-8-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Click_Paginations (10 steps) <span class="action-id-badge" title="Action ID: Pagination">Pagination</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-8-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">3436ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1989ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-8-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2773ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-8-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Age" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2794ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-8-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Months" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2848ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-8-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3771ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-8-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">3607ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1950ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3749ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="b06OvcntcY.png" data-action-id="b06OvcntcY" onclick="showStepDetails('step-8-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: b06OvcntcY">b06OvcntcY</span>
                            </div>
                            <span class="test-step-duration">1164ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3169ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-8-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">3061ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2440ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-8-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">2834ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1304ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-8-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">3908ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-8-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "enn[cooker-id]" <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">3119ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1736ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2196ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2079ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2558ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2574ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-8-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">1864ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-8-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">1847ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1894ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="M3dXqigqRv.png" data-action-id="M3dXqigqRv" onclick="showStepDetails('step-8-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: M3dXqigqRv">M3dXqigqRv</span>
                            </div>
                            <span class="test-step-duration">1896ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-8-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">2755ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till accessibility_id=txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2907ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-8-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 28/06/2025, 23:13:53","testCases":[{"name":"Postcode Flow_NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions","status":"failed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3346ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"8086ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1151ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1956ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"passed","duration":"1749ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"passed","duration":"2118ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"passed","duration":"4329ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"passed","duration":"3248ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on Text: \"8053\"","status":"passed","duration":"3171ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"passed","duration":"2859ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"passed","duration":"3117ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"","status":"passed","duration":"10915ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"passed","duration":"17641ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3868ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"passed","duration":"2405ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1783ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"passed","duration":"3545ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"passed","duration":"3724ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"passed","duration":"3109ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"0616\"","status":"passed","duration":"3114ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"passed","duration":"2850ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"passed","duration":"3134ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"passed","duration":"13479ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2256ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2003ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"passed","duration":"3770ms","action_id":"b06OvcntcY","screenshot_filename":"b06OvcntcY.png","report_screenshot":"b06OvcntcY.png","resolved_screenshot":"screenshots/b06OvcntcY.png","clean_action_id":"b06OvcntcY","prefixed_action_id":"al_b06OvcntcY","action_id_screenshot":"screenshots/b06OvcntcY.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"passed","duration":"3691ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"passed","duration":"3247ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on Text: \"8053\"","status":"passed","duration":"3603ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"passed","duration":"2879ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"passed","duration":"3119ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"passed","duration":"11165ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"passed","duration":"5517ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"passed","duration":"1565ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2467ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]","status":"passed","duration":"1689ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Collect\"","status":"passed","duration":"3188ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]","status":"passed","duration":"1790ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Nearby\"","status":"passed","duration":"3458ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on element with accessibility_id: delete","status":"passed","duration":"4348ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"0616\"","status":"passed","duration":"5228ms","action_id":"M3dXqigqRv","screenshot_filename":"M3dXqigqRv.png","report_screenshot":"M3dXqigqRv.png","resolved_screenshot":"screenshots/M3dXqigqRv.png","clean_action_id":"M3dXqigqRv","prefixed_action_id":"al_M3dXqigqRv","action_id_screenshot":"screenshots/M3dXqigqRv.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"passed","duration":"3372ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"Tap on element with accessibility_id: Done","status":"passed","duration":"4254ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"3392ms","action_id":"pKjXoj4mNg","screenshot_filename":"pKjXoj4mNg.png","report_screenshot":"pKjXoj4mNg.png","resolved_screenshot":"screenshots/pKjXoj4mNg.png","clean_action_id":"pKjXoj4mNg","prefixed_action_id":"al_pKjXoj4mNg","action_id_screenshot":"screenshots/pKjXoj4mNg.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2217ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"failed","duration":"1501ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"unknown","duration":"1909ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"unknown","duration":"30448ms","action_id":"OKCHAK6HCJ","screenshot_filename":"OKCHAK6HCJ.png","report_screenshot":"OKCHAK6HCJ.png","resolved_screenshot":"screenshots/OKCHAK6HCJ.png","clean_action_id":"OKCHAK6HCJ","prefixed_action_id":"al_OKCHAK6HCJ","action_id_screenshot":"screenshots/OKCHAK6HCJ.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"2451ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2606ms","action_id":"3hOTINBVMf","screenshot_filename":"3hOTINBVMf.png","report_screenshot":"3hOTINBVMf.png","resolved_screenshot":"screenshots/3hOTINBVMf.png","clean_action_id":"3hOTINBVMf","prefixed_action_id":"al_3hOTINBVMf","action_id_screenshot":"screenshots/3hOTINBVMf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2052ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Browse & PDP NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            37 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3292ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"2712ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[@name=\"txtShopMenuTitle\"]","status":"passed","duration":"1459ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"passed","duration":"2715ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Tap on Text: \"Latest\"","status":"passed","duration":"2776ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2784ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*","status":"passed","duration":"1779ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)","status":"passed","duration":"2262ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1828ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[product-share-img]","status":"passed","duration":"2315ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists","status":"passed","duration":"1701ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"product-share-logo.png\" exists on screen","status":"passed","duration":"11501ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"3054ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Add to bag\"]","status":"passed","duration":"2121ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 50%)","status":"passed","duration":"3640ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"more\"","status":"passed","duration":"3188ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"2142ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"passed","duration":"1965ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"Kid toy\"","status":"passed","duration":"2412ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Check if image \"search-result-test-se.png\" exists on screen","status":"passed","duration":"21438ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2314ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2231ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"passed","duration":"1977ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists","status":"passed","duration":"1308ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists","status":"passed","duration":"1296ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2282ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3214ms","action_id":"b06OvcntcY","screenshot_filename":"b06OvcntcY.png","report_screenshot":"b06OvcntcY.png","resolved_screenshot":"screenshots/b06OvcntcY.png","clean_action_id":"b06OvcntcY","prefixed_action_id":"al_b06OvcntcY","action_id_screenshot":"screenshots/b06OvcntcY.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3996ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"passed","duration":"2365ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1813ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2282ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2292ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"1641ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2082ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2062ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: env[appid]","status":"passed","duration":"1062ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Delivery & CNC- NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            27 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3210ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"5475ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1146ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1920ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4236ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"passed","duration":"2393ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1623ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2082ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"passed","duration":"1895ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2075ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"cnc-tab-se.png\" exists on screen","status":"passed","duration":"11177ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on Text: \"Collect\"","status":"passed","duration":"3086ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2845ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2166ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"2223ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2091ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"5123ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2062ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2120ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3863ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"passed","duration":"2405ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1631ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2095ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2095ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Delivery Buy Step NZ (33 steps)","status":"passed","duration":"0ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3224ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5018ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4812ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1156ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"4089ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"passed","duration":"1598ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"passed","duration":"1590ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"passed","duration":"2080ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5021ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"main\"]//XCUIElementTypeLink)[4]","status":"passed","duration":"2166ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible","status":"passed","duration":"5420ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]","status":"passed","duration":"2075ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Print order details","status":"passed","duration":"3878ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]","status":"passed","duration":"2411ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2951ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on Text: \"Return\"","status":"passed","duration":"3141ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2861ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2092ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"passed","duration":"1587ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"details\"","status":"passed","duration":"2850ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2196ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on Text: \"address\"","status":"passed","duration":"2828ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2223ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"payment\"","status":"passed","duration":"2778ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2138ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2142ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2762ms","action_id":"b06OvcntcY","screenshot_filename":"b06OvcntcY.png","report_screenshot":"b06OvcntcY.png","resolved_screenshot":"screenshots/b06OvcntcY.png","clean_action_id":"b06OvcntcY","prefixed_action_id":"al_b06OvcntcY","action_id_screenshot":"screenshots/b06OvcntcY.png"},{"name":"Tap on Text: \"locator\"","status":"passed","duration":"2770ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on Text: \"Nearby\"","status":"passed","duration":"3466ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"If exists: xpath=\"//XCUIElementTypeButton[@name=\"Allow While Using App\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Allow While Using App\"]","status":"passed","duration":"20180ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap and Type at (29, 262): \"0616\"","status":"passed","duration":"5236ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"passed","duration":"3447ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"2349ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2251ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Tap on Text: \"Customer\"","status":"passed","duration":"2764ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2189ms","action_id":"M3dXqigqRv","screenshot_filename":"M3dXqigqRv.png","report_screenshot":"M3dXqigqRv.png","resolved_screenshot":"screenshots/M3dXqigqRv.png","clean_action_id":"M3dXqigqRv","prefixed_action_id":"al_M3dXqigqRv","action_id_screenshot":"screenshots/M3dXqigqRv.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2777ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"All Sign ins NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            28 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3213ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4839ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1181ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1859ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2146ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2604ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2085ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2085ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]","status":"passed","duration":"1950ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1220ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1937ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2020ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]","status":"passed","duration":"1612ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2055ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2702ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2087ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3216ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2164ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]","status":"passed","duration":"2716ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1156ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2149ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2630ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2166ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5019ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Kmart-Prod Sign in NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3215ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4888ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1148ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"2660ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"2445ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"passed","duration":"3017ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"passed","duration":"2456ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"passed","duration":"2775ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"1285ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2079ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4351ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2068ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"passed","duration":"1768ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3789ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1139ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1905ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Apple\"","status":"passed","duration":"3197ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Wait for 10 ms","status":"passed","duration":"10021ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on Text: \"Passcode\"","status":"passed","duration":"1980ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]","status":"passed","duration":"1673ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]","status":"passed","duration":"1588ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]","status":"passed","duration":"1913ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]","status":"passed","duration":"1596ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]","status":"passed","duration":"1587ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]","status":"passed","duration":"1596ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"5708ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2083ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4958ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2078ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"passed","duration":"1776ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3781ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1158ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1946ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Google\"","status":"passed","duration":"3136ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]","status":"passed","duration":"2624ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"2591ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2075ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4362ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2077ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5017ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"WishList NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            46 actions","status":"failed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3198ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"5481ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1168ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1919ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3913ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"passed","duration":"2428ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1739ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1733ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2937ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2007ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2428ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2501ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]","status":"passed","duration":"2226ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1984ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"failed","duration":"2107ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2061ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2222ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1695ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"unknown","duration":"3591ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"3187ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1345ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1953ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"Move\"","status":"unknown","duration":"2732ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1578ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2559ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]","status":"unknown","duration":"1936ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1976ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"2423ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Move to wishlist\"]\" is visible","status":"unknown","duration":"4445ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"unknown","duration":"1647ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"unknown","duration":"2782ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"unknown","duration":"2175ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"2467ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"3208ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2741ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"3204ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2930ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1952ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2605ms","action_id":"b06OvcntcY","screenshot_filename":"b06OvcntcY.png","report_screenshot":"b06OvcntcY.png","resolved_screenshot":"screenshots/b06OvcntcY.png","clean_action_id":"b06OvcntcY","prefixed_action_id":"al_b06OvcntcY","action_id_screenshot":"screenshots/b06OvcntcY.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2046ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 10%)","status":"unknown","duration":"2129ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"App Settings NZ\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"failed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3207ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]","status":"passed","duration":"2094ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1177ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Terminate app: com.apple.Preferences","status":"passed","duration":"1227ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Launch app: com.apple.Preferences","status":"passed","duration":"1242ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on Text: \"Wi-Fi\"","status":"passed","duration":"2936ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"passed","duration":"941ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3220ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"259ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]","status":"passed","duration":"804ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"227ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]","status":"passed","duration":"666ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"213ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"passed","duration":"631ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"193ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Launch app: com.apple.Preferences","status":"passed","duration":"123ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"passed","duration":"768ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5022ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3270ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]","status":"passed","duration":"2058ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2620ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2898ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Restart app: com.apple.mobilesafari","status":"passed","duration":"3228ms","action_id":"mobilesafa","screenshot_filename":"mobilesafa.png","report_screenshot":"mobilesafa.png","resolved_screenshot":"screenshots/mobilesafa.png","action_id_screenshot":"screenshots/mobilesafa.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]","status":"passed","duration":"1809ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"kmart nz\"","status":"passed","duration":"2281ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.co.nz\"]","status":"failed","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]","status":"unknown","duration":"724ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"0ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"unknown","duration":"0ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"0ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"0ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3307ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3343ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"iOS Function: text - Text: \"P_43515028\"","status":"passed","duration":"2492ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2273ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"15017ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]","status":"passed","duration":"13740ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Done\"","status":"passed","duration":"34656ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3242ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2032ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4030ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"iOS Function: text - Text: \"kids toys\"","status":"passed","duration":"2381ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Execute Test Case: Click_Paginations (10 steps)","status":"passed","duration":"0ms","action_id":"Pagination","screenshot_filename":"Pagination.png","report_screenshot":"Pagination.png","resolved_screenshot":"screenshots/Pagination.png","action_id_screenshot":"screenshots/Pagination.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3436ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"1989ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"passed","duration":"2773ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on Text: \"Age\"","status":"passed","duration":"2794ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"Months\"","status":"passed","duration":"2848ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"passed","duration":"3771ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"passed","duration":"3607ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"1950ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3749ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1164ms","action_id":"b06OvcntcY","screenshot_filename":"b06OvcntcY.png","report_screenshot":"b06OvcntcY.png","resolved_screenshot":"screenshots/b06OvcntcY.png","clean_action_id":"b06OvcntcY","prefixed_action_id":"al_b06OvcntcY","action_id_screenshot":"screenshots/b06OvcntcY.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"3169ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"passed","duration":"3061ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"passed","duration":"2440ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"passed","duration":"2834ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"1304ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3908ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"iOS Function: text - Text: \"enn[cooker-id]\"","status":"passed","duration":"3119ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1736ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2196ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2079ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2558ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2574ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"passed","duration":"1864ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"passed","duration":"1847ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"1894ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"1896ms","action_id":"M3dXqigqRv","screenshot_filename":"M3dXqigqRv.png","report_screenshot":"M3dXqigqRv.png","resolved_screenshot":"screenshots/M3dXqigqRv.png","clean_action_id":"M3dXqigqRv","prefixed_action_id":"al_M3dXqigqRv","action_id_screenshot":"screenshots/M3dXqigqRv.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2755ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"Wait till accessibility_id=txtHomeAccountCtaSignIn","status":"passed","duration":"2907ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]}],"passed":6,"failed":3,"skipped":0,"status":"failed","availableScreenshots":["08NzsvhQXK.png","0962MtId5t.png","0Q0fm6OTij.png","0bnBNoqPt8.png","0pwZCYAtOv.png","13YG4jrM9E.png","1Lirmyxkft.png","20qUCJgpE9.png","2FnAZDskt1.png","2cTZvK1psn.png","2kwu2VBmuZ.png","2mhi7GOrlS.png","2p13JoJbbA.png","2xC5fLfLe8.png","3CTsyFe28F.png","3KNqlNy6Bj.png","3caMBvQX7k.png","3gJsiap2Ds.png","3hOTINBVMf.png","5Gj5mgIxVu.png","5ZzW1VVSzy.png","5e4LeoW1YU.png","5nsUXQ5L7u.png","6G6P3UE7Uy.png","6HhScBaqQp.png","6LQ5cq0f6N.png","6mHVWI3j5e.png","6qZnk86hGg.png","6xgrAWyfZ4.png","70iOOakiG7.png","73NABkfWyY.png","7WYExJTqjp.png","7g2LmvjtEZ.png","7g6MFJSGIO.png","7xs3GiydGF.png","83tV9A4NOn.png","88BYVcWtJZ.png","8OsQmoVYqW.png","8WCusTZ8q9.png","8umPSX0vrr.png","92tKl3T5N8.png","94ikwhIEE2.png","9B5MQGTmpP.png","9Jhn4eWZwR.png","9MqlsILCgk.png","9Pwdq32eUk.png","9QADAZGNH3.png","A1Wz7p1iVG.png","Azb1flbIJJ.png","BCM1sS8SGA.png","Bdhe5AoUlM.png","BracBsfa3Y.png","BzTvnSrykE.png","C6JHhLdWTv.png","CBBib3pFkq.png","CJ88OgjKXp.png","CLMmkV1OIM.png","CWkqGp5ndO.png","CtWhaVwbJC.png","CzVeOTdAX9.png","DY8MfL0wXI.png","DhWa2PCBXE.png","E2jpN7BioW.png","EELcfo48Sh.png","EJkHvEQccu.png","Ef6OumM2eS.png","Ey8MUB57vM.png","F0gZF1jEnT.png","F1olhgKhUt.png","F4NGh9HrLw.png","F9UfvzyNii.png","FARWZvOj0x.png","FAvQgIuHc1.png","FHRlQXe58T.png","FlEukNkjlS.png","FnrbyHq7bU.png","G4A3KBlXHq.png","GHH3xhNGgr.png","GRwHMVK4sA.png","GTXmST3hEA.png","GYRHQr7TWx.png","GgQaBLWYkb.png","H0ODFz7sWJ.png","H9fy9qcFbZ.png","HotUJOd6oB.png","I4gwigwXSj.png","I5bRbYY1hD.png","IL6kON0uQ9.png","IOzaCR1Euv.png","ISfUFUnvFh.png","ISpNHH3V9g.png","ITHvSyXXmu.png","ImienLpJEN.png","IsGWxLFpIn.png","IupxLP2Jsr.png","IvqPpScAJa.png","J7BPGVnRJI.png","JRheDTvpJf.png","JXFxYCr98V.png","Jf2wJyOphY.png","K7yV3GGsgr.png","KOLK6CUomC.png","KfMHchi8cx.png","KlfYmNjrq8.png","KyyS139agr.png","L6wTorOX8B.png","L8LEfGm9WC.png","LcYLwUffqj.png","LfyQctrEJn.png","LlRfimKPrn.png","M1IXnYddFx.png","M3dXqigqRv.png","NL2gtj6qIu.png","NQGIFb5O7u.png","NcU6aex76k.png","NkybTKfs2U.png","NurQsFoMkE.png","OKCHAK6HCJ.png","OQ1fr8NUlV.png","OR0SKKnFxy.png","ORI6ZFMBK1.png","OUT2ASweb6.png","Ob26qqcA0p.png","OmKfD9iBjD.png","OyUowAaBzD.png","P2OkZzbCB3.png","PLrRarI0Y9.png","PbfHAtFQPP.png","Q5A0cNaJ24.png","QMXBlswP6H.png","QPKR6jUF9O.png","Qbg9bipTGs.png","QpBLC6BStn.png","QvuueoTR8W.png","RCYxT9YD8u.png","RDQCFIxjA0.png","RbD937Xbte.png","RbNtEW6N9T.png","RuPGkdCdah.png","SDtskxyVpg.png","SFj4Aa7RHQ.png","SPE01N6pyp.png","SPgFRgq13M.png","Sl6eiqZkRm.png","TTpwkHEyuE.png","TV4kJIIV9v.png","To7bij5MnF.png","UpUSVInizv.png","V42eHtTRYW.png","V59u3l1wkM.png","VkUKQbf1Qt.png","Vxt7QOYeDD.png","Vy3WZ0LTJF.png","Vyrkv4wK1v.png","WEB5St2Mb7.png","WbxRVpWtjw.png","WlISsMf9QA.png","WmNWcsWVHv.png","WoymrHdtrO.png","WwIZzJEW9W.png","XEbZHdi0GT.png","XLpUP3Wr93.png","XRiJwoJD9w.png","Xqj9EIVEfg.png","Xr6F8gdd8q.png","XuLgjNG74w.png","Y8vz7AJD1i.png","YH6erO83XY.png","YbamBpASJi.png","YuuQe2KupX.png","Z6g3sGuHTp.png","ZCsqeOXrY1.png","ZWpYNcpbFA.png","ZhH80yndRU.png","a4pJa7EAyI.png","alaozIePOy.png","aqBkqyVhrZ.png","arH1CZCPXh.png","b06OvcntcY.png","bGo3feCwBQ.png","bQrT7FZsxl.png","bZCkx4U9Gk.png","bkU728TrRF.png","cJDpd7aK3d.png","cKNu2QoRC1.png","cokvFXhj4c.png","dF3hpprg71.png","dYEtjrv6lz.png","dkSs61jGvX.png","eGQ7VrKUSo.png","eHLWiRoqqS.png","eHvkAVake5.png","eJnHS9n9VL.png","eLxHVWKeDQ.png","eSr9EFlJek.png","eVytJrry9x.png","ewuLtuqVuo.png","fDgFGQYpCw.png","fPX582qHkp.png","fTdGMJ3NH3.png","ftA0OJvd0W.png","g0PE7Mofye.png","gPYNwJ0HKo.png","gekNSY5O2E.png","h9trcMrvxt.png","hCCEvRtj1A.png","hnH3ayslCh.png","hr0IVckpYI.png","hwdyCKFAUJ.png","iDtcdR3nSL.png","iExyA0ZirJ.png","iSckENpXrN.png","iwqbVl90WJ.png","j4y8kIvfN6.png","jQYHQIvQ8l.png","jUCAk6GJc4.png","jY0oPjKbuS.png","k3mu9Mt7Ec.png","kAQ1yIIw3h.png","kDpsm2D3xt.png","kQJbqm7uCi.png","kbdEPCPYod.png","ksCBjJiwHZ.png","ky6rfmPv0u.png","kz9lnCdwoH.png","lCSewtjn1z.png","lSG7un0qKK.png","lWJtKSqlPS.png","latest.png","letbbewlnA.png","lnjoz8hHUU.png","ly2oT3zqmf.png","m0956RsrdM.png","mIKA85kXaW.png","mfOWujfRpL.png","mw9GQ4mzRE.png","nAB6Q8LAdv.png","nVWzLauG8N.png","napKDohf3Z.png","oETU9DzLi4.png","oSQ8sPdVOJ.png","oWLIFhrzr1.png","oqTdx3vL0d.png","p8rfQL9ara.png","pFlYwTS53v.png","pKjXoj4mNg.png","pjFNt3w5Fr.png","pk2DLZFBmx.png","placeholder.png","pr9o8Zsm5p.png","q4hPXCBtx4.png","q6kSH9e0MI.png","q70JSbqKNk.png","q8oldD8uZt.png","qA1ap4n1m4.png","qjj0i3rcUh.png","qkZ5KShdEU.png","quZwUwj3a8.png","quzlwPw42x.png","rJ86z4njuR.png","rJVGLpLWM3.png","rSxM47lUdy.png","rkL0oz4kiL.png","rqLJpAP0mA.png","s0WyiD1w0B.png","s8h8VDUIOC.png","sc2KH9bG6H.png","seQcUKjkSU.png","sl3Wk1gK8X.png","soKM0KayFJ.png","trBISwJ8eZ.png","ts3qyFxyMf.png","u6bRYZZFAv.png","uArzgeZYf7.png","uM5FOSrU5U.png","uZHvvAzVfx.png","uuatVQwQFW.png","vAKpEDIzs7.png","vYLhraWpQm.png","veukWo4573.png","vfwUVEyq6X.png","vwFwkK6ydQ.png","w0CWlknXmX.png","w1RV76df9x.png","wguGCt7OoB.png","x4Mid4HQ0Z.png","x4yLCZHaCR.png","xAPeBnVHrT.png","xAa049Qgls.png","xLGm9FefWE.png","xUbWFa8Ok2.png","xVuuejtCFA.png","xmelRkcdVx.png","xqHGFj3tDd.png","yEga5MkcRe.png","yNAxs8bgMy.png","yUJyVO5Wev.png","ydRnBBO1vR.png","yi5EsHEFvc.png","ylslyLAYKb.png","zNRPvs2cC4.png","zWrzEgdH3Q.png","zsVeGHiIgX.png","zzd5ufNDih.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>