import os
import json
import base64
import time
import logging
import glob
import sys
import traceback
import shutil
import threading
import signal
import uuid
from pathlib import Path

# Define a dummy socketio object to avoid errors
class DummySocketIO:
    def emit(self, event, data):
        pass

socketio = DummySocketIO()

# Add app directory to path if not already in it
current_dir = Path(__file__).resolve().parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session
from utils.appium_device_controller import AppiumDeviceController
from utils.recorder import Recorder
from utils.test_case_manager import TestCaseManager
from utils.player import Player
from actions.action_factory import ActionFactory
from logging.config import dictConfig
from test_suites_manager import TestSuitesManager
import PIL.Image
import PIL.ImageDraw
import io
import xml.etree.ElementTree as ET
import requests
import re
from datetime import datetime
from functools import wraps
from werkzeug.utils import secure_filename
from utils.reportGenerator import generateReport
from utils.custom_report_generator import generate_custom_report
from routes.random_data_routes import random_data_bp
from routes.devices import devices_bp
from routes.tools import tools_bp
from utils.screenshot_manager import screenshot_manager
from utils.directory_paths_db import directory_paths_db
from utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value

# Add parent directory to path to import config
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
import config

# Initialize Flask app
app = Flask(__name__)
app.config.from_object('config.FlaskConfig')
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'a_very_secret_key_that_should_be_set_in_env') # Ensure secret key is set for session

# --- Helper to get current environment ID from session ---
def get_current_environment_id_from_session():
    return session.get('current_environment_id')
# END OF HELPER FUNCTION

# Register blueprints
app.register_blueprint(random_data_bp)
app.register_blueprint(devices_bp)
app.register_blueprint(tools_bp)

# Initialize managers
test_suites_manager = TestSuitesManager()

# *** DEVICE CONTROLLER MAP ***
# Instead of using global variables, use a dictionary to store device controllers by device ID
device_controllers = {}
players = {}
action_factories = {}

# Session-based device tracking - each session maintains its own current device
def get_client_session_id():
    """Get the client session ID from request headers or body"""
    # Try to get from header first
    client_session_id = request.headers.get('X-Client-Session-ID')

    # If not in header, try to get from request body
    if not client_session_id and request.is_json:
        client_session_id = request.json.get('client_session_id')

    # If still not found, fall back to Flask session
    if not client_session_id:
        if 'client_session_id' not in session:
            session['client_session_id'] = f"server_{str(uuid.uuid4())[:8]}"
            logger.info(f"Generated new server-side session ID: {session['client_session_id']}")
        client_session_id = session['client_session_id']

    return client_session_id

def get_session_device_id():
    """Get the current device ID for this session"""
    client_session_id = get_client_session_id()
    return session.get(f'current_device_id_{client_session_id}')

def set_session_device_id(device_id):
    """Set the current device ID for this session"""
    client_session_id = get_client_session_id()
    session[f'current_device_id_{client_session_id}'] = device_id

def clear_session_device_id():
    """Clear the current device ID for this session"""
    client_session_id = get_client_session_id()
    session.pop(f'current_device_id_{client_session_id}', None)

def get_session_id():
    """Get or create a unique session ID for this session"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())[:8]  # Use first 8 characters for brevity
        logger.info(f"Generated new session ID: {session['session_id']}")
    return session['session_id']

@app.route('/api/debug/session', methods=['GET'])
def debug_session():
    """Debug endpoint to check session state and device controllers"""
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    # Get all device controllers and their session keys
    controller_info = {}
    for key, controller in device_controllers.items():
        controller_info[key] = {
            'connected': hasattr(controller, 'is_connected') and controller.is_connected(),
            'device_id': getattr(controller, 'device_id', 'unknown')
        }

    return jsonify({
        'session_id': session_id,
        'client_session_id': client_session_id,
        'current_device_id': device_id,
        'session_device_key': f"{device_id}_{client_session_id}" if device_id else None,
        'total_controllers': len(device_controllers),
        'controllers': controller_info
    })

@app.route('/api/debug/capture_test', methods=['POST'])
def debug_capture_test():
    """Debug endpoint to test capture image area logic without actually capturing"""
    try:
        # Use session-specific device controller lookup
        session_id = get_session_id()
        client_session_id = get_client_session_id()

        # Try to get device_id from request body first, then fall back to session
        data = request.json or {}
        device_id = data.get('device_id') or request.headers.get('X-Device-ID') or get_session_device_id()

        debug_info = {
            'session_id': session_id,
            'client_session_id': client_session_id,
            'device_id': device_id,
            'request_device_id': data.get('device_id'),
            'header_device_id': request.headers.get('X-Device-ID'),
            'session_device_id': get_session_device_id()
        }

        if not device_id:
            debug_info['error'] = 'No device ID found'
            return jsonify(debug_info), 400

        # Create session-specific key using the same format as other endpoints
        session_device_key = f"{device_id}_{client_session_id}"
        debug_info['session_device_key'] = session_device_key
        debug_info['available_controllers'] = list(device_controllers.keys())

        # Check if controller exists
        device_controller = None
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            debug_info['controller_found'] = 'session_specific'
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            debug_info['controller_found'] = 'legacy'
        else:
            # Try to find any controller for this device_id (regardless of session)
            for key, controller in device_controllers.items():
                if key.startswith(device_id + "_"):
                    device_controller = controller
                    debug_info['controller_found'] = f'any_available: {key}'
                    break

        if device_controller:
            debug_info['controller_status'] = 'found'
            debug_info['controller_connected'] = hasattr(device_controller, 'is_connected') and device_controller.is_connected()
        else:
            debug_info['controller_status'] = 'not_found'

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

try:
    from config import DIRECTORIES
    TEST_CASES_DIR = DIRECTORIES['TEST_CASES']
    REFERENCE_IMAGES_DIR = DIRECTORIES['REFERENCE_IMAGES']
    SCREENSHOTS_DIR = DIRECTORIES['SCREENSHOTS']
    logger = logging.getLogger(__name__)
    logger.info(f"Using directories from config.py:")
    logger.info(f"  - TEST_CASES_DIR: {TEST_CASES_DIR}")
    logger.info(f"  - REFERENCE_IMAGES_DIR: {REFERENCE_IMAGES_DIR}")
    logger.info(f"  - SCREENSHOTS_DIR: {SCREENSHOTS_DIR}")
except ImportError:
    # Default fallback if config can't be imported
    logger.error("Failed to import config.py - this is required for correct operation")
    TEST_CASES_DIR = Path(__file__).resolve().parent.parent / 'test_cases'
    REFERENCE_IMAGES_DIR = Path(__file__).resolve().parent.parent / 'reference_images'
    SCREENSHOTS_DIR = Path(__file__).resolve().parent / 'static' / 'screenshots'
    logger.warning(f"Using fallback directories:")
    logger.warning(f"  - TEST_CASES_DIR: {TEST_CASES_DIR}")
    logger.warning(f"  - REFERENCE_IMAGES_DIR: {REFERENCE_IMAGES_DIR}")
    logger.warning(f"  - SCREENSHOTS_DIR: {SCREENSHOTS_DIR}")

# Configure logging
dictConfig({
    'version': 1,
    'formatters': {'default': {
        'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
    }},
    'handlers': {'wsgi': {
        'class': 'logging.StreamHandler',
        'stream': 'ext://flask.logging.wsgi_errors_stream',
        'formatter': 'default'
    }},
    'root': {
        'level': 'INFO', # Keep root level INFO or higher
        'handlers': ['wsgi']
    },
    # Add specific loggers for our modules
    'loggers': {
        'appium_device_controller': {
            'level': 'DEBUG', # Set controller level to DEBUG
            'handlers': ['wsgi'],
            'propagate': False # Prevent double logging in root
        },
        'utils.parameter_utils': {
            'level': 'DEBUG', # Set parameter utils level to DEBUG
            'handlers': ['wsgi'],
            'propagate': False # Prevent double logging in root
        },
        'actions': {
            'level': 'DEBUG', # Set actions level to DEBUG
            'handlers': ['wsgi'],
            'propagate': False # Prevent double logging in root
        }
    }
})

# Set additional Flask app config
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'default-secret-key') # Load from env or use default
app.config['SCREENSHOTS_FOLDER'] = os.path.join(app.root_path, 'static', 'screenshots')

# Initialize the test case manager
test_case_manager = TestCaseManager(TEST_CASES_DIR)

# Register shutdown handler to clean up processes when the app is closing
def shutdown_handler():
    logger.info("Shutting down and cleaning up all processes...")

    # Shutdown all device controllers
    for device_id, controller in device_controllers.items():
        try:
            logger.info(f"Shutting down device controller for device {device_id}")
            controller.shutdown()
        except Exception as e:
            logger.error(f"Error during device controller shutdown for device {device_id}: {e}")

    # Clear the device controllers dictionary
    device_controllers.clear()
    players.clear()
    action_factories.clear()

    # Call additional cleanup functions
    cleanup_screenshots()
    logger.info("App shutdown complete")

# Add signal handler to ensure proper cleanup on exit
signal.signal(signal.SIGTERM, shutdown_handler)

# Global variables
current_device = None
current_device_id = None  # Add missing global variable for current device ID
recording_actions = []
action_factory = None
is_test_suite_execution = False  # Global flag to track if we're executing a test suite

# Initialize global variables for report directories
current_report_dir = None
current_report_timestamp = None
current_screenshots_dir = None
current_action_logs = []  # Store action logs for the current test run

# Global variables for step execution timeout
step_execution_timer = None
step_timeout_occurred = False

# Class to handle step execution timeout
class StepExecutionTimeout:
    def __init__(self, timeout, step_name, test_case_name):
        self.timeout = timeout
        self.step_name = step_name
        self.test_case_name = test_case_name
        self.timer = None
        self.timeout_occurred = False

    def start(self):
        """Start the timeout timer"""
        global step_timeout_occurred
        step_timeout_occurred = False

        def timeout_handler():
            """Handler called when timeout occurs"""
            global step_timeout_occurred
            step_timeout_occurred = True
            logger.error(f"========== STEP TIMEOUT DETECTED ==========")
            logger.error(f"Step execution timed out after {self.timeout} seconds")
            logger.error(f"Test Case: {self.test_case_name}")
            logger.error(f"Step: {self.step_name}")
            logger.error(f"========== STOPPING TEST EXECUTION ==========")

            # Force flush the logs to ensure they appear in real-time
            sys.stdout.flush()
            sys.stderr.flush()

            # Raise an exception in the main thread to interrupt execution
            # This is a bit of a hack, but it's the most reliable way to interrupt execution
            # when the main thread is stuck in a blocking operation
            os.kill(os.getpid(), signal.SIGUSR1)

        self.timer = threading.Timer(self.timeout, timeout_handler)
        self.timer.daemon = True  # Make the timer a daemon thread so it doesn't prevent program exit
        self.timer.start()

    def cancel(self):
        """Cancel the timeout timer"""
        if self.timer:
            self.timer.cancel()
            self.timer = None

# Create objects to hold current test and step indices
class IndexHolder:
    def __init__(self, initial_value=0):
        self.value = initial_value

    def __str__(self):
        return f"IndexHolder(value={self.value})"

    def __repr__(self):
        return self.__str__()

# Initialize with default values
current_test_idx = IndexHolder(0)
current_step_idx = IndexHolder(0)
current_image_counter = IndexHolder(1)  # Counter for sequential image numbering
current_suite_id = None  # Will be set when a test suite is executed



logger.info(f"=== INITIALIZED GLOBAL INDEX HOLDERS ===")
logger.info(f"current_test_idx: {current_test_idx}")
logger.info(f"current_step_idx: {current_step_idx}")
logger.info(f"current_image_counter: {current_image_counter}")
logger.info(f"current_suite_id: {current_suite_id}")
# Remove the dated screenshots global variable

# Ensure all required directories exist with proper permissions
screenshots_dir = SCREENSHOTS_DIR
os.makedirs(screenshots_dir, exist_ok=True)

# Also ensure the app/static/screenshots directory exists
app_screenshots_dir = os.path.join(os.path.dirname(__file__), 'static', 'screenshots')
os.makedirs(app_screenshots_dir, exist_ok=True)
logger.info(f"Created/verified app screenshots directory: {app_screenshots_dir}")

# Ensure reports directory exists
reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports')
os.makedirs(reports_dir, exist_ok=True)
logger.info(f"Created/verified reports directory: {reports_dir}")

try:
    # Set directory permissions to allow read/write
    os.chmod(screenshots_dir, 0o775)
    os.chmod(app_screenshots_dir, 0o775)
    os.chmod(reports_dir, 0o775)
    logger.info(f"Set permissions for directories")
except Exception as e:
    logger.error(f"Error setting directory permissions: {e}")

# Clean up old screenshots
def cleanup_screenshots():
    # Clean up screenshots in the root screenshots directory
    if os.path.exists(screenshots_dir):
        logger.info(f"Cleaning up old screenshots in {screenshots_dir}")
        # Keep only latest.png, remove all other png files
        for screenshot in glob.glob(os.path.join(screenshots_dir, "*.png")):
            if os.path.basename(screenshot) != "latest.png":
                try:
                    os.remove(screenshot)
                    logger.debug(f"Removed old screenshot: {screenshot}")
                except Exception as e:
                    logger.error(f"Failed to remove screenshot {screenshot}: {e}")
    else:
        os.makedirs(screenshots_dir, exist_ok=True)
        logger.info(f"Created screenshots directory: {screenshots_dir}")

# Clean up screenshots in the app/static/screenshots directory
def cleanup_app_screenshots():
    if os.path.exists(app_screenshots_dir):
        logger.info(f"Cleaning up all screenshots in {app_screenshots_dir}")
        # Keep only latest.png, remove all other png files
        for screenshot in glob.glob(os.path.join(app_screenshots_dir, "*.png")):
            if os.path.basename(screenshot) != "latest.png":
                try:
                    os.remove(screenshot)
                    logger.debug(f"Removed app screenshot: {screenshot}")
                except Exception as e:
                    logger.error(f"Failed to remove app screenshot {screenshot}: {e}")

        # Log the number of files remaining
        remaining_files = glob.glob(os.path.join(app_screenshots_dir, "*.png"))
        logger.info(f"Remaining files in app screenshots directory: {len(remaining_files)}")
    else:
        os.makedirs(app_screenshots_dir, exist_ok=True)
        logger.info(f"Created app screenshots directory: {app_screenshots_dir}")

# Run cleanup at startup
cleanup_screenshots()
cleanup_app_screenshots()

# Set up signal handler for SIGUSR1 (used by the timeout mechanism)
def sigusr1_handler(signum, frame):
    """Handler for SIGUSR1 signal - used to interrupt execution when a step times out"""
    logger.error("Received SIGUSR1 signal - step execution timeout detected")
    # Raise an exception to interrupt the current execution
    raise Exception("Step execution timeout detected - execution interrupted")

# Register the signal handler
signal.signal(signal.SIGUSR1, sigusr1_handler)

# Initialize database tables at startup
try:
    from utils.database import clear_execution_tracking, update_execution_tracking_schema, update_screenshots_schema, init_db

    # Initialize database tables (create if not exist, but don't clear existing data)
    init_db()
    logger.info("Database tables initialized/verified at startup")

    # Update execution_tracking table schema
    update_schema_result = update_execution_tracking_schema()
    logger.info(f"Updated execution_tracking table schema at startup: {update_schema_result}")

    # Update screenshots table schema
    update_screenshots_result = update_screenshots_schema()
    logger.info(f"Updated screenshots table schema at startup: {update_screenshots_result}")

    # Clear only execution_tracking table (this is for tracking current test runs)
    # But only for the default instance to avoid interfering with other running instances
    import os
    instance_port = os.environ.get('INSTANCE_PORT')
    if not instance_port or instance_port == '8080':
        clear_result = clear_execution_tracking()
        logger.info(f"Cleared execution_tracking table at startup: {clear_result}")
    else:
        logger.info(f"Skipping execution_tracking table clearing for instance on port {instance_port} to avoid interfering with other instances")

    # NOTE: We do NOT clear test_suites and test_cases tables at startup
    # This preserves existing test cases and test suites
    logger.info("Preserved existing test cases and test suites")
except Exception as e:
    logger.error(f"Error during database initialization at startup: {str(e)}")

# Endpoint to initialize report directory when "Execute All" is clicked
@app.route('/api/report/initialize', methods=['POST'])
def initialize_report_directory():
    """Initialize the report directory and screenshots folder for the current test execution"""
    global current_report_dir, current_screenshots_dir, current_report_timestamp

    try:
        # Generate timestamp for the report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Initialize the screenshot manager
        report_dir, screenshots_dir = screenshot_manager.initialize(timestamp=timestamp)

        if report_dir and screenshots_dir:
            # Set the global variables
            current_report_dir = report_dir
            current_screenshots_dir = screenshots_dir
            current_report_timestamp = timestamp

            logger.info(f"Initialized report directory: {report_dir}")
            logger.info(f"Initialized screenshots directory: {screenshots_dir}")

            return jsonify({
                'status': 'success',
                'message': 'Report directory initialized successfully',
                'report_dir': report_dir,
                'screenshots_dir': screenshots_dir,
                'timestamp': timestamp
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to initialize report directory'
            }), 500
    except Exception as e:
        logger.error(f"Error initializing report directory: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error initializing report directory: {str(e)}"
        }), 500

# Endpoint to delete screenshots when "Execute All" is clicked
@app.route('/api/screenshots/delete_all', methods=['POST'])
def delete_all_screenshots():
    """Delete all screenshots in the app/static/screenshots directory"""
    try:
        logger.info("Deleting all screenshots in app/static/screenshots directory")
        cleanup_app_screenshots()
        return jsonify({
            'status': 'success',
            'message': 'All screenshots deleted successfully'
        })
    except Exception as e:
        logger.error(f"Error deleting screenshots: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error deleting screenshots: {str(e)}"
        }), 500



@app.route('/api/logs/save', methods=['POST'])
def save_action_logs():
    """Save action logs to a text file in the current report directory"""
    try:
        global current_report_dir, current_action_logs

        # Get the logs from the request
        data = request.json
        logs = data.get('logs', [])

        if not logs:
            return jsonify({
                'status': 'error',
                'message': 'No logs provided'
            }), 400

        # Store logs in the global variable for later use
        current_action_logs = logs
        logger.info(f"Stored {len(logs)} action logs for later use")

        # If we have a current report directory, save the logs now
        if current_report_dir and os.path.exists(current_report_dir):
            from utils.reportGenerator import save_action_logs_to_file
            log_file_path = save_action_logs_to_file(logs, current_report_dir)

            if log_file_path:
                logger.info(f"Saved action logs to file: {log_file_path}")
                return jsonify({
                    'status': 'success',
                    'message': 'Action logs saved successfully',
                    'log_file_path': log_file_path
                })

        # If we don't have a report directory yet, just store the logs for later
        return jsonify({
            'status': 'success',
            'message': 'Action logs stored for later use'
        })
    except Exception as e:
        logger.error(f"Error saving action logs: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error saving action logs: {str(e)}"
        }), 500

# App screenshots directory already created above

# Function to initialize report directory
def initialize_report_directory():
    """
    Initialize a new report directory for the current test execution.
    Creates a simplified directory structure directly in the reports folder.

    Returns:
        tuple: (report_dir, screenshots_dir, timestamp) - Paths to the report and screenshots directories, and the timestamp
    """
    global current_report_dir, current_report_timestamp, current_screenshots_dir, current_suite_id

    # Generate a unique suite ID for this execution if not already set
    if not hasattr(globals(), 'current_suite_id') or not current_suite_id:
        import uuid
        current_suite_id = str(uuid.uuid4())
        logger.info(f"Generated new suite ID for report: {current_suite_id}")

    try:
        # Generate UUID for the report directory
        import uuid
        suite_id = str(uuid.uuid4())

        # Get the reports directory path from the database
        try:
            from utils.directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if not reports_dir:
                # No fallback - reports directory must be configured in Settings tab
                raise Exception("Reports directory not configured in Settings tab")

            # Ensure absolute path
            if not os.path.isabs(reports_dir):
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                reports_dir = os.path.join(base_dir, reports_dir)

            logger.info(f"Using reports directory from Settings tab: {reports_dir}")
        except Exception as e:
            logger.error(f"Error getting reports directory from Settings: {str(e)}")
            raise Exception("Reports directory not configured in Settings tab. Please configure it before running tests.")

        # Ensure reports directory exists
        os.makedirs(reports_dir, exist_ok=True)
        logger.info(f"Using reports directory from settings: {reports_dir}")

        # Create the main report directory with suite ID
        report_dir = os.path.join(reports_dir, f'suite_execution_{suite_id}')
        os.makedirs(report_dir, exist_ok=True)
        logger.info(f"Created report directory: {report_dir}")

        # Create the screenshots subdirectory
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)
        logger.info(f"Created screenshots directory: {screenshots_dir}")

        # Set the global variables
        current_report_timestamp = suite_id  # Use suite_id instead of timestamp
        current_report_dir = report_dir
        current_screenshots_dir = screenshots_dir

        logger.info(f"Initialized report directory: {report_dir}")
        return report_dir, screenshots_dir, timestamp
    except Exception as e:
        logger.error(f"Error initializing report directory: {str(e)}")
        return None, None, None

# Helper functions for configuration
def load_config():
    """Load configuration from config.json file"""
    config_path = Path(__file__).resolve().parent.parent / 'app' / 'config.json'
    if config_path.exists():
        with open(config_path, 'r') as f:
            return json.load(f)
    return {"testcases_dir": "test_cases", "reports_dir": "reports"}

def validate_folder_configuration():
    """Validate that all required folder paths are properly configured and exist"""
    try:
        # Get all directory paths from database
        paths = directory_paths_db.get_all_paths()

        # Core required folders for device connection
        core_required_folders = ['TEST_CASES', 'REPORTS', 'REFERENCE_IMAGES', 'TEST_SUITES']
        # Optional folders that can be created if missing
        optional_folders = ['FILES_TO_PUSH']

        errors = []

        # Check core required folders
        for folder_key in core_required_folders:
            folder_path = paths.get(folder_key)

            if not folder_path:
                errors.append(f"{folder_key} folder path is not configured")
                continue

            # Convert to absolute path if relative
            if not os.path.isabs(folder_path):
                # Get base directory from config
                try:
                    from config import BASE_DIR
                    folder_path = os.path.join(BASE_DIR, folder_path)
                except ImportError:
                    # Fallback to app parent directory
                    base_dir = Path(__file__).resolve().parent.parent
                    folder_path = os.path.join(base_dir, folder_path)

            # Check if folder exists
            if not os.path.exists(folder_path):
                errors.append(f"{folder_key} folder does not exist: {folder_path}")
                continue

            # Check if it's actually a directory
            if not os.path.isdir(folder_path):
                errors.append(f"{folder_key} path is not a directory: {folder_path}")
                continue

        # Check optional folders and create them if missing
        for folder_key in optional_folders:
            folder_path = paths.get(folder_key)

            if not folder_path:
                logger.warning(f"Optional folder {folder_key} is not configured, skipping")
                continue

            # Convert to absolute path if relative
            if not os.path.isabs(folder_path):
                try:
                    from config import BASE_DIR
                    folder_path = os.path.join(BASE_DIR, folder_path)
                except ImportError:
                    base_dir = Path(__file__).resolve().parent.parent
                    folder_path = os.path.join(base_dir, folder_path)

            # Create the folder if it doesn't exist
            if not os.path.exists(folder_path):
                try:
                    os.makedirs(folder_path, exist_ok=True)
                    logger.info(f"Created optional folder: {folder_path}")
                except Exception as create_error:
                    logger.warning(f"Could not create optional folder {folder_key}: {create_error}")
                    # Don't add to errors since it's optional
                continue

            # Check if it's actually a directory
            if not os.path.isdir(folder_path):
                logger.warning(f"Optional folder {folder_key} path is not a directory: {folder_path}")
                # Don't add to errors since it's optional
                continue

        # Special validation for reports folder - check if it has required files
        reports_path = paths.get('REPORTS')
        if reports_path and os.path.exists(reports_path):
            # Convert to absolute path if relative
            if not os.path.isabs(reports_path):
                try:
                    from config import BASE_DIR
                    reports_path = os.path.join(BASE_DIR, reports_path)
                except ImportError:
                    base_dir = Path(__file__).resolve().parent.parent
                    reports_path = os.path.join(base_dir, reports_path)

            # Check for required report files
            required_report_files = ['generate_report.js', 'report_template.html']
            project_reports_dir = Path(__file__).resolve().parent.parent / 'reports'

            for required_file in required_report_files:
                report_file_path = os.path.join(reports_path, required_file)
                if not os.path.exists(report_file_path):
                    # Try to copy from project reports folder
                    source_file = project_reports_dir / required_file
                    if source_file.exists():
                        try:
                            import shutil
                            shutil.copy2(str(source_file), report_file_path)
                            logger.info(f"Copied {required_file} to reports folder")
                        except Exception as copy_error:
                            errors.append(f"Missing required report file {required_file} in reports folder and failed to copy: {copy_error}")
                    else:
                        errors.append(f"Missing required report file {required_file} in reports folder")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    except Exception as e:
        logger.error(f"Error validating folder configuration: {e}")
        return {
            'valid': False,
            'errors': [f"Error during validation: {str(e)}"]
        }

def save_config(config):
    """Save configuration to config.json file"""
    config_path = Path(__file__).resolve().parent.parent / 'app' / 'config.json'
    config_path.parent.mkdir(exist_ok=True)
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    return True

# Helper function to encode image to base64
def encode_image_to_base64(image_path):
    if not os.path.exists(image_path):
        return None

    with open(image_path, 'rb') as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

    return f"data:image/png;base64,{encoded_string}"

# Main routes
@app.route('/')
def index():
    import time
    timestamp = int(time.time()) + 3000  # Force cache bust
    return render_template('index.html', timestamp=timestamp)

@app.route('/test_suites')
def test_suites():
    return render_template('test_suites.html')

@app.route('/multi_device_test.html')
def multi_device_test():
    """Serve the multi-device test page"""
    test_file_path = os.path.join(os.path.dirname(os.path.dirname(app.root_path)), 'multi_device_test.html')
    if os.path.exists(test_file_path):
        return send_file(test_file_path)
    else:
        return "Multi-device test file not found", 404

@app.route('/screenshot')
def get_screenshot():
    # Get the device ID and session ID from the query parameters
    device_id = request.args.get('deviceId')
    session_id = request.args.get('sessionId')
    client_session_id_param = request.args.get('clientSessionId')

    # Log the request for debugging
    logger.info(f"Screenshot request for device: {device_id}, session: {session_id}, clientSessionId: {client_session_id_param}")

    # If no device ID is provided, use the session device ID
    if not device_id:
        device_id = get_session_device_id()
        logger.info(f"Using session device ID: {device_id}")

    # Check if we have a controller for this device
    if not device_id:
        logger.warning("No device ID available")
        return send_file(os.path.join(os.path.dirname(__file__), 'static', 'img', 'no_device.png'), mimetype='image/png')

    # Use client session ID from query parameter if provided, otherwise get from session
    client_session_id = client_session_id_param or get_client_session_id()
    session_device_key = f"{device_id}_{client_session_id}"
    logger.info(f"Screenshot request - Client Session ID: {client_session_id}, Device ID: {device_id}, Session Device Key: {session_device_key}")

    # First try session-specific key, then fall back to device_id for backward compatibility
    controller = None
    if session_device_key in device_controllers:
        controller = device_controllers[session_device_key]
        logger.info(f"Found session-specific controller for: {session_device_key}")
    elif device_id in device_controllers:
        controller = device_controllers[device_id]
        logger.info(f"Found legacy controller for device: {device_id}")

    # Log all available controllers for debugging
    logger.info(f"Available device controllers: {list(device_controllers.keys())}")

    if not controller:
        logger.warning(f"No controller found for device ID: {device_id} or session key: {session_device_key}. Available controllers: {list(device_controllers.keys())}")
        return send_file(os.path.join(os.path.dirname(__file__), 'static', 'img', 'no_device.png'), mimetype='image/png')

    try:

        # Create a device-specific screenshot path
        screenshot_filename = f"device_{device_id.replace('-', '_').replace(':', '_')}_latest.png"
        screenshot_path = os.path.join(app.root_path, 'static', 'screenshots', screenshot_filename)

        # Ensure directory exists
        os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)

        # Take a fresh screenshot directly to the device-specific path
        logger.info(f"Taking screenshot to: {screenshot_path}")
        screenshot_result = controller.take_screenshot(filename=screenshot_path)

        # Extract the path from the result dictionary
        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            logger.info(f"Screenshot result path: {screenshot_path}")
            if screenshot_path and os.path.exists(screenshot_path):
                logger.info(f"Screenshot file exists, serving: {screenshot_path}")
                # Return the screenshot file directly with cache control headers
                response = send_file(screenshot_path, mimetype='image/png')
                response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response
            else:
                logger.warning(f"Screenshot path does not exist: {screenshot_path}")
        else:
            logger.warning(f"Screenshot failed or returned error: {screenshot_result}")

        # Fall back to no_device.png if screenshot failed or doesn't exist
        logger.info("Falling back to no_device.png")
        return send_file(os.path.join(os.path.dirname(__file__), 'static', 'img', 'no_device.png'), mimetype='image/png')
    except Exception as e:
        logger.error(f"Error taking screenshot for device {device_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return send_file(os.path.join(os.path.dirname(__file__), 'static', 'img', 'no_device.png'), mimetype='image/png')

@app.route('/screenshots/<path:filename>')
def serve_screenshot(filename):
    """Serve screenshots from the root project screenshots directory"""
    try:
        # Get screenshots directory from config (absolute path)
        file_path = os.path.join(SCREENSHOTS_DIR, filename)

        # Log the requested path for debugging
        logger.info(f"Serving screenshot from absolute path: {file_path}")

        # Make sure the file exists
        if os.path.exists(file_path):
            # Set cache control headers to prevent caching
            response = send_file(file_path, mimetype='image/png')
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response
        else:
            logger.error(f"Screenshot file not found: {file_path}")
            return jsonify({'error': 'Screenshot not found'}), 404
    except Exception as e:
        logger.error(f"Error serving screenshot: {str(e)}")
        return jsonify({'error': f'Error serving screenshot: {str(e)}'}), 500

@app.route('/api/reference_images', methods=['GET'])
def get_reference_images():
    """Get a list of all reference images"""
    try:
        # Get reference images directory from config
        reference_images_dir = REFERENCE_IMAGES_DIR

        # Check if the directory exists
        if not os.path.exists(reference_images_dir):
            logger.warning(f"Reference images directory not found: {reference_images_dir}")
            return jsonify({
                'status': 'error',
                'message': 'Reference images directory not found'
            }), 404

        # Get all image files in the directory and subdirectories
        image_files = []
        for root, _, files in os.walk(reference_images_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    # Get the full path
                    full_path = os.path.join(root, file)
                    # Get the relative path from the reference images directory
                    rel_path = os.path.relpath(full_path, reference_images_dir)

                    # Create a preview URL
                    preview_url = f"/api/reference_image_preview?path={rel_path}"

                    # Add to the list
                    image_files.append({
                        'path': rel_path,
                        'preview': preview_url
                    })

        # Sort the images by path
        image_files.sort(key=lambda x: x['path'])

        return jsonify({
            'status': 'success',
            'images': image_files
        })
    except Exception as e:
        logger.error(f"Error getting reference images: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error getting reference images: {str(e)}"
        }), 500

@app.route('/api/reference_image_preview', methods=['GET'])
def get_reference_image_preview():
    """Get a preview of a reference image"""
    try:
        # Get the image path from the query parameters
        image_path = request.args.get('path')
        if not image_path:
            return jsonify({
                'status': 'error',
                'message': 'No image path provided'
            }), 400

        # Get the full path to the image
        full_path = os.path.join(REFERENCE_IMAGES_DIR, image_path)

        # Check if the file exists
        if not os.path.exists(full_path):
            logger.warning(f"Reference image not found: {full_path}")
            return jsonify({
                'status': 'error',
                'message': 'Reference image not found'
            }), 404

        # Read the image and convert to base64
        with open(full_path, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

        # Return the base64-encoded image
        return jsonify({
            'status': 'success',
            'preview': f"data:image/png;base64,{encoded_string}"
        })
    except Exception as e:
        logger.error(f"Error getting reference image preview: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error getting reference image preview: {str(e)}"
        }), 500

# API endpoints
@app.route('/api/devices', methods=['GET'])
def get_devices():
    # Use a shared controller for device discovery with configured ports
    # Note: Don't pass wda_port here - let the controller read device-specific ports from wda_ports.txt
    try:
        import config
        appium_port = getattr(config, 'APPIUM_PORT', 4723)
        # Don't use static WDA_PORT - let controller determine device-specific port
        discovery_controller = AppiumDeviceController(appium_port=appium_port)
    except ImportError:
        discovery_controller = AppiumDeviceController()

    try:
        devices = discovery_controller.get_devices()
        return jsonify({"devices": devices})
    except Exception as e:
        logger.error(f"Error getting devices: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/device/connect', methods=['POST'])
def connect_device():
    """Connect to a specific device"""
    try:
        # First, validate that all required folder paths are configured
        validation_result = validate_folder_configuration()
        if not validation_result['valid']:
            return jsonify({
                "status": "error",
                "error": "Folder configuration validation failed",
                "details": validation_result['errors'],
                "warning": "Please configure all required folder paths in Settings tab before connecting to device"
            }), 400

        data = request.get_json()
        device_id = data.get('device_id')
        platform = data.get('platform') # Extract platform from request
        session_id = data.get('session_id') or request.args.get('sessionId')

        # Log the connection request for debugging
        logger.info(f"Device connection request - ID: {device_id}, Platform: {platform}, Session: {session_id}, Flask Session: {get_session_id()}")

        if not device_id:
            return jsonify({"status": "error", "error": "Missing device_id parameter"})

        # Create a session-specific device key to allow multiple sessions to connect to the same device
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"
        logger.info(f"Session-specific device key: {session_device_key} (Client Session: {client_session_id})")

        # Check if this session already has a controller for this device
        if session_device_key in device_controllers:
            logger.info(f"Session already connected to device {device_id}, reusing existing connection")
            controller = device_controllers[session_device_key]
        else:
            # Create a new controller for this device with configured ports
            # Note: Don't pass wda_port here - let the controller read device-specific ports from wda_ports.txt
            try:
                import config
                appium_port = getattr(config, 'APPIUM_PORT', 4723)
                # Don't use static WDA_PORT - let controller determine device-specific port
                controller = AppiumDeviceController(appium_port=appium_port)
            except ImportError:
                controller = AppiumDeviceController()

            # Connect to the device, passing the platform hint
            logger.info(f"Connecting to device: {device_id}, Platform hint: {platform}")
            success = controller.connect_to_device(device_id, platform=platform)
            
            if not success:
                logger.error(f"Failed to connect to device: {device_id}")
                return jsonify({"status": "error", "error": "Failed to connect to device"})
                
            # Store the controller in our dictionary using session-specific key
            device_controllers[session_device_key] = controller
            logger.info(f"Stored controller with session-specific key: {session_device_key}")

            # Make sure current_test_idx is initialized
            if 'current_test_idx' not in globals():
                current_test_idx = type('IndexHolder', (), {'value': 0})()
            else:
                current_test_idx = globals()['current_test_idx']

            # Create a player for this device using session-specific key
            players[session_device_key] = Player(
                device_controller=controller,
                test_cases_dir=TEST_CASES_DIR,
                test_idx=current_test_idx.value
            )
            logger.info(f"Player initialized for session device key: {session_device_key} with test_idx={current_test_idx.value}")

            # Create an action factory for this device using session-specific key
            action_factories[session_device_key] = ActionFactory(controller)
            logger.info(f"Action Factory initialized for session device key: {session_device_key}")

            # Ensure AirTest is initialized if available
            if hasattr(controller, '_init_airtest'):
                logger.info(f"Ensuring AirTest is initialized for device: {device_id}")
                airtest_success = controller._init_airtest(device_id)
                logger.info(f"AirTest initialization result: {airtest_success}")

        # Set as current device for this session
        set_session_device_id(device_id)

        # Set global device_controller for backward compatibility with health check
        # Note: This is still global but will be overridden by session-specific logic in API endpoints
        global device_controller, player, action_factory, current_device_id
        device_controller = controller
        player = players[session_device_key]
        action_factory = action_factories[session_device_key]
        current_device_id = device_id  # Set the global current_device_id for health check compatibility

        # Take screenshot when device is connected
        screenshot_result = controller.take_screenshot()
        screenshot_data = None
        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # Check if AirTest is available
        airtest_available = hasattr(controller, 'airtest_device') and controller.airtest_device is not None

        # Get device information
        device_info = {
            "device_id": device_id,
            "is_emulator": "emulator" in device_id or "localhost" in device_id
        }

        # Get additional device information if available
        if hasattr(controller, 'driver') and controller.driver:
            try:
                capabilities = controller.driver.capabilities
                if capabilities:
                    device_info.update({
                        "platform_name": capabilities.get("platformName"),
                        "platform_version": capabilities.get("platformVersion"),
                        "device_name": capabilities.get("deviceName"),
                        "model": capabilities.get("deviceModel"),
                        "manufacturer": capabilities.get("deviceManufacturer")
                    })

                # Get device dimensions and add them to device_info
                try:
                    dimensions = controller.driver.get_window_size()
                    if dimensions:
                        device_info.update({
                            "width": dimensions.get("width"),
                            "height": dimensions.get("height")
                        })
                        app.logger.info(f"Including device dimensions in connect response: {dimensions}")
                except Exception as dim_err:
                    app.logger.warning(f"Could not get device dimensions: {str(dim_err)}")

            except Exception as e:
                logger.warning(f"Could not get device capabilities: {str(e)}")

        return jsonify({
            "status": "connected",
            "device_id": device_id,
            "screenshot": screenshot_data,
            "airtest_available": airtest_available,
            "device_info": device_info
        })

    except Exception as e:
        logger.error(f"Error connecting to device: {str(e)}")
        return jsonify({"status": "error", "error": str(e)})

@app.route('/api/device/disconnect', methods=['POST'])
def disconnect_device():
    # Get device ID from request or session
    data = request.get_json() or {}
    device_id = data.get('deviceId', get_session_device_id())

    if not device_id:
        return jsonify({"status": "disconnected", "message": "No device ID provided"})

    # Create session-specific device key using client session ID
    client_session_id = get_client_session_id()
    session_device_key = f"{device_id}_{client_session_id}"

    # Check if we have a session-specific controller
    controller = None
    controller_key = None

    if session_device_key in device_controllers:
        controller = device_controllers[session_device_key]
        controller_key = session_device_key
        logger.info(f"Found session-specific controller to disconnect: {session_device_key}")
    elif device_id in device_controllers:
        controller = device_controllers[device_id]
        controller_key = device_id
        logger.info(f"Found legacy controller to disconnect: {device_id}")

    if not controller:
        return jsonify({"status": "disconnected", "message": "No device connected with this ID"})

    try:
        # Disconnect the controller
        controller.disconnect()

        # Remove from dictionaries using the correct key
        del device_controllers[controller_key]
        if controller_key in players:
            del players[controller_key]
        if controller_key in action_factories:
            del action_factories[controller_key]

        # Reset current device if this was the current one for this session
        if get_session_device_id() == device_id:
            clear_session_device_id()

            # Clear global variables for backward compatibility
            global device_controller, player, action_factory, current_device_id
            device_controller = None
            player = None
            action_factory = None
            current_device_id = None  # Clear the global current_device_id

        return jsonify({"status": "disconnected"})
    except Exception as e:
        logger.error(f"Error disconnecting device {device_id}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/database/clear_screenshots', methods=['POST'])
def clear_screenshots():
    """Clear all screenshots from the database"""
    try:
        from utils.database import get_db_path
        import sqlite3

        # Get count before clearing
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM screenshots")
        before_count = cursor.fetchone()[0]

        # Clear all screenshots
        cursor.execute("DELETE FROM screenshots")
        conn.commit()

        # Get count after clearing
        cursor.execute("SELECT COUNT(*) FROM screenshots")
        after_count = cursor.fetchone()[0]

        conn.close()

        logger.info(f"Cleared {before_count} screenshots from database")

        return jsonify({
            "status": "success",
            "message": f"Cleared {before_count} screenshots from database",
            "before_count": before_count,
            "after_count": after_count
        })

    except Exception as e:
        logger.error(f"Error clearing screenshots from database: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Error clearing screenshots: {str(e)}"
        }), 500

@app.route('/api/check_inspector', methods=['GET'])
def check_inspector():
    """Check if the Appium Inspector plugin is available and return session info"""
    global device_controller
    # Get appium port from config or device controller
    try:
        import config
        appium_port = getattr(config, 'APPIUM_PORT', 4723)
    except ImportError:
        appium_port = device_controller.appium_port if device_controller else 4723

    base_url = f'http://localhost:{appium_port}'
    inspector_url = f'{base_url}/inspector'
    status_url = f'{base_url}/wd/hub/status'

    session_info = {}
    available = False
    message = ""
    server_status = "unknown"

    try:
        # 1. Check basic Appium server status first
        try:
            logger.info(f"Checking Appium status at: {status_url}")
            status_response = requests.get(status_url, timeout=2)
            if status_response.status_code == 200:
                logger.info("Appium server is running (returned status 200).")
                server_status = "running"

                # 2. Check inspector endpoint if server is running
                try:
                    logger.info(f"Checking Appium Inspector at: {inspector_url}")
                    inspector_response = requests.get(inspector_url, timeout=2)
                    if inspector_response.status_code == 200:
                        logger.info("Appium Inspector endpoint is available.")
                        available = True
                        message = "Inspector available."
                    else:
                        logger.warning(f"Inspector endpoint returned status code {inspector_response.status_code}")
                        available = False
                        message = f"Inspector endpoint returned status {inspector_response.status_code}. Plugin might be missing or disabled."
                except requests.exceptions.ConnectionError:
                    logger.warning(f"Could not connect to Appium Inspector endpoint: {inspector_url}. Server running but inspector not served.")
                    available = False
                    message = "Could not connect to Inspector endpoint. Plugin might be missing or disabled."
                except Exception as e_inspect:
                    logger.error(f"Error checking Appium Inspector endpoint: {str(e_inspect)}")
                    available = False
                    message = f"Error checking inspector endpoint: {str(e_inspect)}"
            else:
                logger.warning(f"Appium server status check failed. Status code: {status_response.status_code}")
                server_status = "not_responding"
                available = False
                message = f"Appium server at {base_url} returned status {status_response.status_code}. Cannot check inspector."
        except requests.exceptions.ConnectionError:
            logger.error(f"Could not connect to Appium server at {status_url}. Connection refused.")
            server_status = "connection_refused"
            available = False
            message = f"Could not connect to Appium server at {base_url}. Is it running?"
        except Exception as e_status:
            logger.error(f"Error checking Appium server status: {str(e_status)}")
            server_status = "error"
            available = False
            message = f"Error checking Appium status: {str(e_status)}"

        # 3. Get session information if a device is connected (regardless of inspector availability)
        logger.debug("Attempting to get session info...")
        if device_controller:
            logger.debug(f"Device controller exists. Driver present: {device_controller.driver is not None}")
            if device_controller.driver:
                try:
                    session_id = device_controller.session_id # Check 2: Does the controller have a session_id?
                    logger.debug(f"Attempting to read session_id property: {session_id}")
                    if session_id:
                        logger.debug(f"Session ID retrieved: {session_id}")
                        capabilities = device_controller.driver.capabilities # Check 3: Can we get capabilities?
                        logger.debug(f"Capabilities retrieved: {capabilities is not None}")
                        # ... WDA session ID logic ...
                        wda_session_id = None
                        platform_name = capabilities.get('platformName', '').lower() if capabilities else ''
                        logger.debug(f"Platform name from capabilities: {platform_name}")

                        if platform_name == 'ios':
                            try:
                                detailed_session_info = device_controller.get_session_info()
                                logger.debug(f"Detailed session info retrieved for iOS: {detailed_session_info is not None}")
                                if detailed_session_info and 'wda_status' in detailed_session_info:
                                    wda_session_id = detailed_session_info['wda_status'].get('sessionId')
                                    logger.info(f"Found WebDriverAgent session ID from session info: {wda_session_id}")
                            except Exception as wda_err:
                                logger.warning(f"Error getting WebDriverAgent session ID: {wda_err}")

                        session_info = {
                            "session_id": session_id,
                            "capabilities": capabilities
                        }
                        if wda_session_id:
                            session_info["wda_session_id"] = wda_session_id
                            logger.info(f"Using WebDriverAgent session ID for iOS: {wda_session_id}")
                        logger.info(f"Found active Appium session ID: {session_id}")
                    else:
                        logger.warning("device_controller.session_id property returned None or empty.") # This might be logged
                except Exception as session_err:
                    logger.error(f"Error getting session info from driver: {str(session_err)}")
                    logger.error(traceback.format_exc())
            else:
                logger.warning("Device controller has no active driver instance.")
        else:
            logger.warning("Device controller instance not found.") # Or this might be logged

        # 4. Return the result
        return jsonify({
            "status": "success" if server_status == "running" else "error",
            "available": available,
            "url": inspector_url if available else None,
            "message": message,
            "session_info": session_info,
            "server_status": server_status # Added for clarity
        })

    except Exception as e:
        logger.error(f"Unexpected error in check_inspector endpoint: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "status": "error",
            "available": False,
            "message": f"Internal server error: {str(e)}",
            "session_info": {},
            "server_status": "endpoint_error"
        }), 500

@app.route('/api/device/dimensions', methods=['GET'])
def get_device_dimensions():
    """Get the dimensions of the connected device's screen"""
    global device_controller, current_device

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        # Try to get dimensions from the device controller
        dimensions = device_controller.get_device_dimensions()

        if dimensions:
            width, height = dimensions
            logger.info(f"Retrieved device dimensions: {width}x{height}")
            return jsonify({
                "status": "success",
                "width": width,
                "height": height
            })
        else:
            # If we have an Airtest device, try to get dimensions from it
            if hasattr(device_controller, 'airtest_device') and device_controller.airtest_device:
                try:
                    screen_size = device_controller.airtest_device.get_current_resolution()
                    if screen_size:
                        width, height = screen_size
                        logger.info(f"Retrieved device dimensions from Airtest: {width}x{height}")
                        return jsonify({
                            "status": "success",
                            "width": width,
                            "height": height
                        })
                except Exception as airtest_err:
                    logger.error(f"Error getting dimensions from Airtest: {str(airtest_err)}")

            # If all else fails, return a default size
            logger.warning("Could not get device dimensions, using default values")
            return jsonify({
                "status": "warning",
                "width": 1080,  # Default width
                "height": 1920, # Default height
                "message": "Using default dimensions"
            })
    except Exception as e:
        logger.error(f"Error getting device dimensions: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/screenshot', methods=['GET', 'POST'])
def take_screenshot():
    """Take a screenshot of the current device screen"""
    # Get the device ID from the query parameters or request body
    if request.method == 'GET':
        device_id = request.args.get('deviceId')
        session_id = request.args.get('sessionId')
    else:  # POST
        data = request.get_json() or {}
        device_id = data.get('deviceId')
        session_id = data.get('sessionId')
    
    # Log the screenshot request for debugging
    logger.debug(f"API screenshot request - Device: {device_id}, Session: {session_id}")
    
    # If no device ID is provided, use the session device ID
    if not device_id:
        device_id = get_session_device_id()
    
    # Check if we have a controller for this device
    if not device_id:
        logger.warning("No device ID provided for screenshot")
        return jsonify({
            'status': 'error',
            'message': 'No device ID provided'
        }), 400

    # Create session-specific device key using client session ID
    client_session_id = get_client_session_id()
    session_device_key = f"{device_id}_{client_session_id}"

    # Get the controller for this device (try session-specific first, then legacy)
    controller = None
    if session_device_key in device_controllers:
        controller = device_controllers[session_device_key]
        logger.debug(f"Using session-specific controller for screenshot: {session_device_key}")
    elif device_id in device_controllers:
        controller = device_controllers[device_id]
        logger.debug(f"Using legacy controller for screenshot: {device_id}")

    if not controller:
        logger.warning(f"No device controller found for ID: {device_id} or session key: {session_device_key}")
        return jsonify({
            'status': 'error',
            'message': f'No device connected with ID: {device_id}'
        }), 400
    if not controller or not controller.driver:
        return jsonify({
            'status': 'error',
            'message': 'Device controller not properly initialized'
        }), 400

    # Create a device-specific filename
    timestamp = int(time.time() * 1000)
    device_filename = f"device_{device_id.replace('-', '_').replace(':', '_')}_{timestamp}.png"
    
    # Allow custom filename from request, but keep device ID prefix
    if request.method == 'POST' and request.json and 'filename' in request.json:
        custom_filename = request.json['filename']
        # Ensure the device ID is part of the filename for isolation
        if not custom_filename.startswith(f"device_{device_id}"):
            device_part = f"device_{device_id.replace('-', '_').replace(':', '_')}_"
            custom_filename = f"{device_part}{custom_filename}"
        device_filename = custom_filename

    # Get the output path - either static/screenshots or a custom path
    if request.method == 'POST' and request.json and 'path' in request.json:
        output_path = request.json['path']
    else:
        # Default path in static folder with device ID in filename
        output_path = os.path.join(app.root_path, 'static', 'screenshots', device_filename)

    logger.info(f"Taking screenshot to: {output_path}")

    # Ensure the directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Maximum retry attempts for screenshot
    max_retries = 3
    retry_count = 0
    screenshot_success = False
    last_error = None

    while retry_count < max_retries and not screenshot_success:
        try:
            # Take screenshot
            result = controller.take_screenshot(filename=output_path)

            if result and result.get('status') == 'success':
                screenshot_success = True
                logger.info(f"Screenshot saved successfully to {output_path}")

                # Also save as device-specific latest.png for reference
                device_latest_path = os.path.join(app.root_path, 'static', 'screenshots', 
                                               f"device_{device_id.replace('-', '_').replace(':', '_')}_latest.png")
                try:
                    shutil.copy2(output_path, device_latest_path)
                    logger.info(f"Updated device-specific latest screenshot for {device_id}")
                except Exception as copy_error:
                    logger.warning(f"Error updating device-specific latest screenshot: {str(copy_error)}")

                # Verify the file exists and has content
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    # Return success with the image and path
                    screenshot_url = f"/static/screenshots/{os.path.basename(output_path)}"
                    logger.info(f"Generated screenshot URL: {screenshot_url}")
                    return jsonify({
                        'status': 'success',
                        'message': 'Screenshot captured',
                        'path': screenshot_url,
                        'filename': os.path.basename(output_path),
                        'screenshot_url': screenshot_url
                    })
                else:
                    last_error = "Screenshot file missing or empty"
                    logger.error(f"Screenshot failed: {last_error}")
            else:
                last_error = result.get('message', 'Unknown error taking screenshot')
                logger.error(f"Screenshot failed: {last_error}")

        except Exception as e:
            last_error = str(e)
            logger.error(f"Exception during screenshot: {last_error}")

        # If we get here, screenshot failed
        retry_count += 1
        if retry_count < max_retries:
            logger.info(f"Retrying screenshot ({retry_count}/{max_retries})...")

            # Add a small delay before retry
            time.sleep(1)

            # Try to recover the device connection if possible
            try:
                controller.recover_connection()
                logger.info("Attempted to recover device connection before screenshot retry")
            except Exception as recovery_error:
                logger.error(f"Failed to recover connection: {str(recovery_error)}")

    # If all retries failed, handle fallback options
    logger.error(f"All screenshot attempts failed after {max_retries} retries")

    # Try to use device-specific latest.png as fallback if it exists
    device_latest_path = os.path.join(app.root_path, 'static', 'screenshots', 
                                   f"device_{device_id.replace('-', '_').replace(':', '_')}_latest.png")
    if os.path.exists(device_latest_path) and os.path.getsize(device_latest_path) > 0:
        try:
            # Copy device-specific latest.png to the requested output path
            shutil.copy2(device_latest_path, output_path)
            logger.info(f"Used device-specific latest screenshot as fallback for device {device_id}")

            # Return partial success
            screenshot_url = f"/static/screenshots/{os.path.basename(output_path)}"
            logger.info(f"Generated fallback screenshot URL: {screenshot_url}")
            return jsonify({
                'status': 'partial_success',
                'message': f'Used fallback screenshot. Original error: {last_error}',
                'path': screenshot_url,
                'filename': os.path.basename(output_path),
                'is_fallback': True,
                'screenshot_url': screenshot_url
            })
        except Exception as copy_error:
            logger.error(f"Failed to use latest.png fallback: {str(copy_error)}")

    # Return error if we get here
    return jsonify({
        'status': 'error',
        'message': f'Failed to capture screenshot: {last_error}',
        'error': last_error
    }), 500

@app.route('/api/capture_image', methods=['POST'])
def capture_image():
    """Capture a region of the screen and save it as a reference image"""
    global device_controller

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        data = request.json
        x = data.get('x', 0)
        y = data.get('y', 0)
        width = data.get('width', 100)
        height = data.get('height', 100)
        image_name = data.get('name')
        save_debug = data.get('save_debug', False)  # Default to False - don't save debug images

        # Log the received coordinates for debugging
        logger.info(f"Capture image request: x={x}, y={y}, width={width}, height={height}, name={image_name}, save_debug={save_debug}")

        # Validate input parameters
        if width <= 0 or height <= 0:
            logger.warning(f"Invalid dimensions received: width={width}, height={height}. Adjusting to positive values.")
            # If width is negative, adjust x and make width positive
            if width < 0:
                x = x + width  # Move x to the left edge
                width = abs(width)
            # If height is negative, adjust y and make height positive
            if height < 0:
                y = y + height  # Move y to the top edge
                height = abs(height)
            # Ensure minimum dimensions
            width = max(1, width)
            height = max(1, height)
            logger.info(f"Adjusted coordinates: x={x}, y={y}, width={width}, height={height}")

        if not image_name:
            return jsonify({"status": "error", "error": "Missing image name"}), 400

        # Ensure image name has .png extension
        if not image_name.lower().endswith('.png'):
            image_name += '.png'

        # Secure the filename to prevent path traversal
        image_name = secure_filename(image_name)

        # Take a screenshot
        screenshot_result = device_controller.take_screenshot(save_debug=save_debug)

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if not screenshot_path or not os.path.exists(screenshot_path):
                return jsonify({"status": "error", "error": "Failed to take screenshot"}), 500

            # Open the screenshot
            try:
                with PIL.Image.open(screenshot_path) as img:
                    # Get the image dimensions
                    img_width, img_height = img.size
                    logger.info(f"Screenshot dimensions: {img_width}x{img_height}")

                    # Log the requested crop coordinates
                    logger.info(f"Requested crop coordinates: x={x}, y={y}, width={width}, height={height}")

                    # Store original coordinates for logging
                    original_x, original_y = x, y
                    original_width, original_height = width, height

                    # First, validate and fix the starting coordinates
                    if x < 0 or y < 0 or x >= img_width or y >= img_height:
                        logger.warning(f"Starting coordinates ({x}, {y}) outside image bounds ({img_width}x{img_height})")
                        x = max(0, min(x, img_width - 1))
                        y = max(0, min(y, img_height - 1))
                        logger.info(f"Adjusted starting point to ({x}, {y})")

                    # Then, validate and fix the dimensions
                    if width <= 0 or height <= 0:
                        logger.error(f"Invalid dimensions: width={width}, height={height}")
                        # If width or height is negative, it might be due to a right-to-left or bottom-to-top selection
                        # In this case, we need to adjust both the starting point and the dimensions
                        if width < 0:
                            logger.warning(f"Negative width detected: {width}. Adjusting...")
                            x = x + width  # Move x to the left edge
                            width = abs(width)
                        if height < 0:
                            logger.warning(f"Negative height detected: {height}. Adjusting...")
                            y = y + height  # Move y to the top edge
                            height = abs(height)

                        # Ensure minimum dimensions
                        width = max(1, width)
                        height = max(1, height)
                        logger.info(f"Adjusted dimensions to: width={width}, height={height}")

                    # Finally, ensure the selection doesn't exceed image bounds
                    if x + width > img_width:
                        width = img_width - x
                        logger.warning(f"Width exceeds image bounds. Adjusted to: {width}")
                    if y + height > img_height:
                        height = img_height - y
                        logger.warning(f"Height exceeds image bounds. Adjusted to: {height}")

                    # Log the final adjustments
                    if (x != original_x or y != original_y or width != original_width or height != original_height):
                        logger.warning(f"Coordinates adjusted from ({original_x}, {original_y}, {original_width}x{original_height}) to ({x}, {y}, {width}x{height})")

                    # Save debug image with rectangle drawn on it if requested
                    if save_debug:
                        # Create a copy of the original image for debugging
                        debug_img = img.copy()
                        draw = PIL.ImageDraw.Draw(debug_img)

                        # Draw the selection rectangle in red
                        draw.rectangle((x, y, x + width, y + height), outline='red', width=3)

                        # Add text with coordinates
                        font = None  # PIL will use a default font
                        text_position = (10, 10)
                        draw.text(text_position, f"Selection: ({x}, {y}, {width}x{height})", fill='red', font=font)
                        draw.text((10, 30), f"Image size: {img_width}x{img_height}", fill='red', font=font)

                        # Save the debug image
                        debug_path = os.path.join(os.path.dirname(REFERENCE_IMAGES_DIR), 'debug_screenshot.png')
                        debug_img.save(debug_path)
                        logger.info(f"Saved debug image with selection rectangle to {debug_path}")

                        # Also save a cropped debug image to verify what's being captured
                        debug_crop_path = os.path.join(os.path.dirname(REFERENCE_IMAGES_DIR), 'debug_crop.png')
                        debug_crop = img.crop((x, y, x + width, y + height))
                        debug_crop.save(debug_crop_path)
                        logger.info(f"Saved debug crop image to {debug_crop_path}")

                    # Crop the image to the specified region
                    try:
                        cropped_img = img.crop((x, y, x + width, y + height))
                        logger.info(f"Cropped image size: {cropped_img.size}")
                    except Exception as crop_err:
                        logger.error(f"Error cropping image: {crop_err}")
                        # Return a 1x1 transparent image as fallback
                        cropped_img = PIL.Image.new('RGBA', (1, 1), (255, 255, 255, 0))
                        logger.warning("Created fallback 1x1 transparent image")

                    # Save the cropped image to the reference images directory
                    output_path = os.path.join(REFERENCE_IMAGES_DIR, image_name)
                    cropped_img.save(output_path)
                    logger.info(f"Saved cropped image to: {output_path}")

                    # Save debug images only if requested
                    if save_debug:
                        # Save debug match image
                        debug_img = img.copy()
                        draw = PIL.ImageDraw.Draw(debug_img)
                        draw.rectangle([x, y, x + width, y + height], outline="red", width=3)
                        debug_path = os.path.join(REFERENCE_IMAGES_DIR, "debug_match.png")
                        debug_img.save(debug_path)
                        logger.info(f"Saved debug match image to: {debug_path}")

                        # Save a copy of the original screenshot for debugging
                        debug_screenshot_path = os.path.join(REFERENCE_IMAGES_DIR, "debug_screenshot.png")
                        img.save(debug_screenshot_path)
                        logger.info(f"Saved debug screenshot to: {debug_screenshot_path}")
                    else:
                        logger.info("Debug images not saved (save_debug=False)")

                    # Convert the cropped image to base64 for preview
                    buffered = io.BytesIO()
                    cropped_img.save(buffered, format="PNG")
                    img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')

                    return jsonify({
                        "status": "success",
                        "message": f"Image captured and saved as {image_name}",
                        "image": f"data:image/png;base64,{img_str}",
                        "path": output_path,
                        "coordinates": {
                            "x": x,
                            "y": y,
                            "width": width,
                            "height": height
                        }
                    })
            except Exception as img_err:
                logger.error(f"Error processing image: {str(img_err)}")
                return jsonify({"status": "error", "error": f"Error processing image: {str(img_err)}"}), 500
        else:
            return jsonify({
                "status": "error",
                "error": screenshot_result.get('message', 'Failed to take screenshot')
            }), 500

    except Exception as e:
        logger.error(f"Error capturing image: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Removed duplicate get_reference_images endpoint

@app.route('/api/action/tap', methods=['POST'])
def tap_action():
    global device_controller, action_factory

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json
    x = data.get('x')
    y = data.get('y')

    if x is None or y is None:
        return jsonify({"status": "error", "error": "Missing coordinates"}), 400

    try:
        # Execute tap action using the action factory
        if action_factory:
            result = action_factory.execute_action('tap', {'x': x, 'y': y})
        else:
            # Fallback to direct controller call if action factory not available
            device_controller.tap(x, y)
            result = {"status": "success", "message": f"Tapped at ({x}, {y})"}

        # Take screenshot after action execution
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # If recording, add to actions
        recorder = getattr(device_controller, 'recorder', None)
        if recorder and recorder.is_recording:
            action = {
                "type": "tap",
                "x": x,
                "y": y,
                "timestamp": time.time()
            }
            recorder.add_action(action)
            return jsonify({
                "status": "recorded",
                "action": action,
                "screenshot": screenshot_data
            })

        # Return result with screenshot
        response = {
            "status": result.get("status", "success"),
            "message": result.get("message", f"Tapped at ({x}, {y})"),
            "screenshot": screenshot_data
        }

        # If there was an error, add the error field
        if result.get("status") == "error":
            response["error"] = result.get("message")

        return jsonify(response)
    except Exception as e:
        logger.error(f"Error executing tap: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/action/swipe', methods=['POST'])
def swipe_action():
    global device_controller, action_factory

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json

    # Get vector parameters
    vector_start = data.get('vector_start')  # Percentage [x, y] in 0-1 range
    vector_end = data.get('vector_end')      # Percentage [x, y] in 0-1 range
    duration = data.get('duration', 300)     # Default 300ms
    count = data.get('count', 1)             # Default 1 swipe
    interval = data.get('interval', 0.5)     # Default 0.5s between swipes

    # Handle swipe direction if provided
    direction = data.get('direction')
    if direction and not vector_start and not vector_end:
        # Set relative coordinates based on predefined directions
        if direction == 'up':
            # Swipe from bottom center to top center
            vector_start = [0.5, 0.7]
            vector_end = [0.5, 0.3]
        elif direction == 'down':
            # Swipe from top center to bottom center
            vector_start = [0.5, 0.3]
            vector_end = [0.5, 0.7]
        elif direction == 'left':
            # Swipe from right center to left center
            vector_start = [0.7, 0.5]
            vector_end = [0.3, 0.5]
        elif direction == 'right':
            # Swipe from left center to right center
            vector_start = [0.3, 0.5]
            vector_end = [0.7, 0.5]

    # For backward compatibility, check if we have old coordinates
    if (not vector_start or not vector_end) and all(k in data for k in ['start_x', 'start_y', 'end_x', 'end_y']):
        # Convert absolute coordinates to relative format
        try:
            screen_size = device_controller.get_device_dimensions()
            if screen_size:
                width, height = screen_size
                start_x = float(data.get('start_x', 0))
                start_y = float(data.get('start_y', 0))
                end_x = float(data.get('end_x', 0))
                end_y = float(data.get('end_y', 0))

                # Convert percentage values (0-100) to relative format (0-1)
                vector_start = [start_x / 100 if start_x <= 100 else start_x / width,
                               start_y / 100 if start_y <= 100 else start_y / height]
                vector_end = [end_x / 100 if end_x <= 100 else end_x / width,
                             end_y / 100 if end_y <= 100 else end_y / height]
            else:
                return jsonify({"status": "error", "error": "Cannot get screen dimensions for coordinate conversion"}), 400
        except Exception as e:
            logger.error(f"Error converting coordinates: {e}")
            return jsonify({"status": "error", "error": f"Error converting coordinates: {str(e)}"}), 400

    if not vector_start or not vector_end:
        return jsonify({"status": "error", "error": "Missing vector coordinates"}), 400

    try:
        # Execute swipe action using the action factory
        if action_factory:
            result = action_factory.execute_action('swipe', {
                'vector_start': vector_start,
                'vector_end': vector_end,
                'duration': duration,
                'count': count,
                'interval': interval
            })
        else:
            # Fallback to direct controller call if action factory not available
            # Convert percentage to absolute coordinates
            screen_size = device_controller.get_device_dimensions() or (1080, 1920)
            start_x = int(vector_start[0] * screen_size[0])
            start_y = int(vector_start[1] * screen_size[1])
            end_x = int(vector_end[0] * screen_size[0])
            end_y = int(vector_end[1] * screen_size[1])

            # Execute the swipe multiple times if needed
            for i in range(count):
                device_controller.swipe(start_x, start_y, end_x, end_y, duration)
                if i < count - 1:  # Don't sleep after the last swipe
                    time.sleep(interval)

            result = {
                "status": "success",
                "message": f"Performed {count} swipe(s) from {vector_start} to {vector_end}"
            }

        # Take screenshot after action execution
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # If recording, add to actions
        recorder = getattr(device_controller, 'recorder', None)
        if recorder:
            # Add action to recorder
            recorder.add_action('swipe', {
                'vector_start': vector_start,
                'vector_end': vector_end,
                'duration': duration,
                'count': count,
                'interval': interval,
                'direction': direction  # Store direction for readability
            })

        return jsonify({
            "status": result.get("status", "success"),
            "message": result.get("message", "Swipe executed"),
            "screenshot": screenshot_data
        })
    except Exception as e:
        logger.error(f"Error executing swipe: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/action/text', methods=['POST'])
def text_action():
    global device_controller, action_factory, current_device, device_controllers, action_factories

    # Try to get device_id from request data first, then fall back to session
    data = request.get_json() or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            action_factory = action_factories.get(session_device_key)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using session-specific controller for text input: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            action_factory = action_factories.get(device_id)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using legacy controller for text input: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({"status": "error", "error": "No device connected"}), 400
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json
    text_to_input = data.get('text') # Renamed for clarity

    if text_to_input is None: # Check for None specifically, empty string might be valid
        return jsonify({"status": "error", "error": "Missing text parameter"}), 400

    try:
        # 1. Apply existing parameter substitution (${paramname})
        from utils.parameter_utils import substitute_parameters
        original_text_for_substitution = str(text_to_input) # Ensure it's a string for substitute_parameters
        text_after_dollar_substitution = substitute_parameters(original_text_for_substitution)

        # Log the substitution if it occurred
        if text_after_dollar_substitution != original_text_for_substitution:
            logger.info(f"Parameter substitution for ${_VARS}: '{original_text_for_substitution}' -> '{text_after_dollar_substitution}'")
        
        # 2. Apply environment variable substitution (env[varname])
        current_env_id = get_current_environment_id_from_session() # Helper function defined earlier
        final_text = text_after_dollar_substitution # Start with the output of the first substitution

        if current_env_id is not None:
            text_before_env_resolution = final_text
            final_text = resolve_text_with_env_variables(text_before_env_resolution, current_env_id)
            if final_text != text_before_env_resolution:
                logger.info(f"Environment variable resolution for env_vars: '{text_before_env_resolution}' -> '{final_text}' using env ID {current_env_id}")
        else:
            logger.info("No active environment selected, skipping environment variable resolution.")

        # Execute text input action using the action factory
        if action_factory:
            result = action_factory.execute_action('text', {'text': final_text})
        else:
            # Fallback to direct controller call if action factory not available
            device_controller.input_text(final_text)
            result = {"status": "success", "message": f"Input text: {final_text}"}

        # Take screenshot after action execution
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # If recording, add to actions
        recorder = getattr(device_controller, 'recorder', None)
        if recorder and recorder.is_recording:
            action = {
                "type": "text",
                "text": final_text, # Use final_text here as well
                "timestamp": time.time()
            }
            recorder.add_action(action)
            return jsonify({
                "status": "recorded",
                "action": action,
                "screenshot": screenshot_data
            })

        # Return result with screenshot
        response = {
            "status": result.get("status", "success"),
            "message": result.get("message", f"Input text: {final_text}"), # And here
            "screenshot": screenshot_data
        }

        # If there was an error, add the error field
        if result.get("status") == "error":
            response["error"] = result.get("message")

        return jsonify(response)
    except Exception as e:
        logger.error(f"Error inputting text: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/action/key', methods=['POST'])
def key_action():
    global device_controller, action_factory

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json
    key = data.get('key')

    if not key:
        return jsonify({"status": "error", "error": "Missing key code"}), 400

    try:
        # Execute key press action using the action factory
        if action_factory:
            logger.info(f"Using action factory to press key: {key}")
            result = action_factory.execute_action('key', {'key': key})
        else:
            # Fallback to direct controller call if action factory not available
            logger.info(f"Action factory not available, trying direct press_keycode: {key}")
            if hasattr(device_controller, 'press_keycode'):
                result = device_controller.press_keycode(key)
            else:
                logger.error("Neither action_factory nor press_keycode method is available")
                return jsonify({
                    "status": "error",
                    "error": "Key press not supported by the current driver"
                }), 400

            if isinstance(result, dict):
                # Result is already in the right format
                pass
            else:
                # Create a result dict if we got a boolean or other return type
                result = {
                    "status": "success" if result else "error",
                    "message": f"Pressed key: {key}" if result else f"Failed to press key: {key}"
                }

        # Take screenshot after action execution
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # If recording, add to actions
        recorder = getattr(device_controller, 'recorder', None)
        if recorder and recorder.is_recording:
            action = {
                "type": "key",
                "key": key,
                "timestamp": time.time()
            }
            recorder.add_action(action)
            return jsonify({
                "status": "recorded",
                "action": action,
                "screenshot": screenshot_data
            })

        # Return result with screenshot
        response = {
            "status": result.get("status", "success"),
            "message": result.get("message", f"Pressed key: {key}"),
            "screenshot": screenshot_data
        }

        # If there was an error, add the error field
        if result.get("status") == "error":
            response["error"] = result.get("message")

        return jsonify(response)
    except Exception as e:
        logger.error(f"Error pressing key: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/recording/start', methods=['POST'])
def start_recording():
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    if not device_id:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    # Create session-specific key
    session_device_key = f"{session_id}_{client_session_id}_{device_id}"

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]

    if not device_controller:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        # Initialize the recorder if not already initialized
        if not hasattr(device_controller, 'recorder'):
            device_controller.recorder = Recorder(device_controller)

        # Start recording
        device_controller.recorder.start_recording()

        return jsonify({
            "status": "recording_started",
            "message": "Recording started successfully"
        })
    except Exception as e:
        logger.error(f"Error starting recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/recording/stop', methods=['POST'])
def stop_recording():
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    if not device_id:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    # Create session-specific key
    session_device_key = f"{session_id}_{client_session_id}_{device_id}"

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]

    if not device_controller:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        if not hasattr(device_controller, 'recorder') or not device_controller.recorder.is_recording:
            return jsonify({"status": "error", "error": "No recording in progress"}), 400

        # Stop recording and get the recorded actions
        recording_actions = device_controller.recorder.stop_recording()

        return jsonify({
            "status": "recording_stopped",
            "action_count": len(recording_actions)
        })
    except Exception as e:
        logger.error(f"Error stopping recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/recording/save', methods=['POST'])
def save_recording():
    """Save recording session to file"""
    global recording_actions, current_device, test_case_manager

    data = request.json
    name = data.get('name', '')
    actions = data.get('currentActions', recording_actions)
    is_save_as = data.get('isSaveAs', False)
    filename = data.get('filename', None)
    labels = data.get('labels', [])

    # Validate actions if provided in request
    if not actions:
        logger.error("No actions to save")
        return jsonify({"status": "error", "error": "No actions to save"}), 400

    # Validate name
    if not name:
        logger.error("No test case name provided")
        return jsonify({"status": "error", "error": "Please provide a test case name"}), 400

    try:
        logger.info(f"Saving test case: {name} with {len(actions)} actions (Save As: {is_save_as}, Filename: {filename if filename else 'new'})")

        # Log actions for debugging
        for i, action in enumerate(actions):
            if 'fallback_locators' in action:
                logger.info(f"Action {i+1} has {len(action['fallback_locators'])} fallback locators")
                for j, fallback in enumerate(action['fallback_locators']):
                    logger.info(f"  Fallback {j+1}: {fallback.get('locator_type')}={fallback.get('locator_value')}")

        # Create test case data
        test_case = {
            "name": name,
            "created": time.strftime("%Y-%m-%d %H:%M:%S"),
            "device_id": current_device,
            "actions": actions,
            "labels": labels
        }

        # Save using TestCaseManager - pass filename for direct saves
        if not is_save_as and filename:
            saved_filename = test_case_manager.save_test_case(test_case, filename=filename, is_save_as=is_save_as)
        else:
            saved_filename = test_case_manager.save_test_case(test_case, is_save_as=is_save_as)

        if not saved_filename:
            return jsonify({"status": "error", "error": "Failed to save test case"}), 500

        logger.info(f"Test case saved as {saved_filename}")
        return jsonify({
            "status": "saved",
            "filename": saved_filename,
            "message": f"Test case saved as {saved_filename}"
        })
    except Exception as e:
        logger.error(f"Error saving recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/recording/list', methods=['GET'])
def list_recordings():
    global test_case_manager

    try:
        # Log the request
        logger.info("Received request to list test cases")

        # Verify the test cases directory exists
        if not os.path.exists(TEST_CASES_DIR):
            logger.warning(f"Test cases directory does not exist: {TEST_CASES_DIR}")
            os.makedirs(TEST_CASES_DIR, exist_ok=True)
            logger.info(f"Created test cases directory: {TEST_CASES_DIR}")
            return jsonify({
                "status": "success",
                "test_cases": []
            })

        # Get test cases using TestCaseManager
        logger.info(f"Getting test cases from directory: {TEST_CASES_DIR}")
        test_cases_list = test_case_manager.get_test_cases()
        logger.info(f"Found {len(test_cases_list)} test cases metadata entries")

        # Load the full details for each test case
        detailed_test_cases = []
        for test_case_meta in test_cases_list:
            try:
                # Log the test case being loaded
                logger.info(f"Loading test case: {test_case_meta['filename']}")

                # Load the full test case data
                test_case = test_case_manager.load_test_case(test_case_meta['filename'])
                if test_case:
                    # Add filename to the test case data for reference
                    test_case['filename'] = test_case_meta['filename']
                    detailed_test_cases.append(test_case)
                    logger.info(f"Successfully loaded test case: {test_case_meta['filename']}")
                else:
                    logger.warning(f"Failed to load test case: {test_case_meta['filename']}")
            except Exception as load_error:
                logger.error(f"Error loading test case {test_case_meta['filename']}: {str(load_error)}")
                # Continue with other test cases even if one fails
                continue

        logger.info(f"Successfully loaded {len(detailed_test_cases)} test cases")

        # Return the test cases
        return jsonify({
            "status": "success",
            "test_cases": detailed_test_cases
        })
    except Exception as e:
        logger.error(f"Error listing test cases: {str(e)}")
        traceback.print_exc()  # Print full traceback for debugging
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/get_element_at_position', methods=['POST'])
def get_element_at_position_legacy():
    try:
        # Use session-specific device controller lookup
        session_id = get_session_id()
        client_session_id = get_client_session_id()
        device_id = get_session_device_id()

        if not device_id:
            return jsonify({
                'success': False,
                'error': 'No device connected. Please connect to a device first.'
            }), 400

        # Create session-specific key
        session_device_key = f"{session_id}_{client_session_id}_{device_id}"

        # Get device controller using session-specific lookup
        device_controller = None
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]

        if not device_controller or not device_controller.driver:
            return jsonify({
                'success': False,
                'error': 'No active Appium session. Please connect to a device first.'
            }), 400

        data = request.get_json()
        x = data.get('x')
        y = data.get('y')

        if x is None or y is None:
            return jsonify({
                'success': False,
                'error': 'Missing coordinates'
            }), 400

        logger.info(f"Finding element at coordinates: ({x}, {y})")

        try:
            # Get all elements with bounds
            elements = device_controller.driver.find_elements('xpath', '//*')

            # Find element that contains the coordinates
            element = None
            closest_element = None
            min_distance = float('inf')

            for el in elements:
                try:
                    bounds_str = el.get_attribute('bounds')
                    if bounds_str:
                        # Parse bounds string "[left,top][right,bottom]"
                        bounds = bounds_str.replace('][', ',').strip('[]').split(',')
                        if len(bounds) == 4:
                            left, top, right, bottom = map(int, bounds)

                            # Check if point is inside element bounds
                            if left <= x <= right and top <= y <= bottom:
                                # Calculate element area
                                area = (right - left) * (bottom - top)

                                # If we haven't found an element yet, or this one is smaller
                                if element is None or area < min_distance:
                                    element = el
                                    min_distance = area
                                    logger.info(f"Found matching element: {bounds_str}")

                except Exception as e:
                    logger.warning(f"Error processing element bounds: {str(e)}")
                    continue

            if not element:
                return jsonify({
                    'success': False,
                    'error': 'No element found at position'
                }), 404

            # Get element attributes
            attributes = {}
            try:
                # Get basic attributes
                for attr in ['resourceId', 'className', 'text', 'contentDescription',
                           'clickable', 'enabled', 'focused', 'selected', 'bounds']:
                    try:
                        value = element.get_attribute(attr)
                        if value:
                            attributes[attr] = value
                    except:
                        pass

                # Generate locators
                locators = {
                    'xpath': [],
                    'id': None,
                    'class': None,
                    'text': None,
                    'content-desc': None,
                    'uiautomator': []
                }

                # Resource ID locator
                if attributes.get('resourceId'):
                    locators['id'] = attributes['resourceId']
                    locators['xpath'].append(f"//*[@resource-id='{attributes['resourceId']}']")
                    locators['uiautomator'].append(f'new UiSelector().resourceId("{attributes["resourceId"]}")')

                # Class locator
                if attributes.get('className'):
                    locators['class'] = attributes['className']
                    locators['xpath'].append(f"//*[@class='{attributes['className']}']")
                    locators['uiautomator'].append(f'new UiSelector().className("{attributes["className"]}")')

                # Text locator
                if attributes.get('text'):
                    locators['text'] = attributes['text']
                    locators['xpath'].append(f"//*[@text='{attributes['text']}']")
                    locators['uiautomator'].append(f'new UiSelector().text("{attributes["text"]}")')

                # Content-desc locator
                if attributes.get('contentDescription'):
                    locators['content-desc'] = attributes['contentDescription']
                    locators['xpath'].append(f"//*[@content-desc='{attributes['contentDescription']}']")
                    locators['uiautomator'].append(f'new UiSelector().description("{attributes["contentDescription"]}")')

                # Add bounds locator
                if attributes.get('bounds'):
                    locators['xpath'].append(f"//*[@bounds='{attributes['bounds']}']")

                # Add combined locators for better uniqueness
                if attributes.get('className') and attributes.get('text'):
                    locators['xpath'].append(
                        f"//*[@class='{attributes['className']}' and @text='{attributes['text']}']"
                    )
                    locators['uiautomator'].append(
                        f'new UiSelector().className("{attributes["className"]}").text("{attributes["text"]}")'
                    )

                # Add AirTest locators
                position_locator = f"pos=({x}, {y})"
                airtest_locators = [position_locator]

                # Add text-based AirTest locator if available
                if attributes.get('text'):
                    airtest_locators.append(f"text=\"{attributes['text']}\"")

                locators['airtest'] = airtest_locators

                logger.info(f"Element attributes: {attributes}")
                logger.info(f"Generated locators: {locators}")

                return jsonify({
                    'success': True,
                    'element': {
                        'attributes': attributes,
                        'locators': locators,
                        'coordinates': {'x': x, 'y': y}
                    }
                })

            except Exception as e:
                logger.error(f"Error getting element attributes: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'Error getting element attributes: {str(e)}'
                }), 500

        except Exception as e:
            logger.error(f"Error finding element: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    except Exception as e:
        logger.error(f"Error in get_element_at_position: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/element/at_position', methods=['POST'])
def get_element_at_position():
    """Get the element at a specific position on the screen."""
    global device_controller

    if not device_controller or not device_controller.device_id:
        return jsonify({'success': False, 'error': 'Device not connected'}), 400

    try:
        data = request.json
        if not data or 'x' not in data or 'y' not in data:
            return jsonify({'success': False, 'error': 'Missing x, y coordinates'}), 400

        x = int(data['x'])
        y = int(data['y'])

        logger.info(f"Finding element at position ({x}, {y})")
        element_info = device_controller.get_element_at_position(x, y)

        if not element_info:
            return jsonify({
                'success': False,
                'error': 'No element found at the specified position'
            }), 404

        # Ensure we have complete locator information
        if 'locators' not in element_info:
            element_info['locators'] = {}

        # Generate additional locators if possible
        attributes = element_info.get('attributes', {})

        # Add position locator (always available)
        element_info['locators']['airtest'] = [f"pos=({x}, {y})"]

        # Ensure we have coordinates data
        element_info['coordinates'] = {
            'x': x,
            'y': y
        }

        # Add additional locator strategies based on platform and available attributes
        if device_controller.platform_name and device_controller.platform_name.lower() == 'ios':
            # iOS additional locators
            if 'type' in attributes:
                element_type = attributes['type']

                # XPath locator
                if 'xpath' not in element_info['locators'] or not element_info['locators']['xpath']:
                    element_info['locators']['xpath'] = []
                    xpath = f"//{element_type}"

                    # Add attribute conditions to XPath
                    if 'name' in attributes and attributes['name']:
                        xpath += f"[@name='{attributes['name']}']"
                    elif 'label' in attributes and attributes['label']:
                        xpath += f"[@label='{attributes['label']}']"

                    element_info['locators']['xpath'].append(xpath)

                # For elements that appear to be buttons or links but are identified as images
                if element_type == 'XCUIElementTypeImage' and 'name' in attributes:
                    # Check for common button/link keywords in the name
                    name_lower = attributes['name'].lower()
                    if any(keyword in name_lower for keyword in ['edit', 'cancel', 'save', 'delete', 'submit', 'sign in', 'login', 'back']):
                        # Add a more specific XPath for a likely interactive element
                        element_info['locators']['xpath'].append(f"//XCUIElementTypeButton[@name='{attributes['name']}']")

                # iOS class chain locator
                if 'class chain' not in element_info['locators'] or not element_info['locators']['class chain']:
                    element_info['locators']['class chain'] = []

                    if 'name' in attributes and attributes['name']:
                        element_info['locators']['class chain'].append(f"**//{element_type}[`name == \"{attributes['name']}\"`]")
                    elif 'label' in attributes and attributes['label']:
                        element_info['locators']['class chain'].append(f"**//{element_type}[`label == \"{attributes['label']}\"`]")

                # iOS predicate string locator
                if 'predicate string' not in element_info['locators'] or not element_info['locators']['predicate string']:
                    element_info['locators']['predicate string'] = []

                    if 'name' in attributes and attributes['name']:
                        element_info['locators']['predicate string'].append(f"name == \"{attributes['name']}\"")
                    elif 'label' in attributes and attributes['label']:
                        element_info['locators']['predicate string'].append(f"label == \"{attributes['label']}\"")
                    else:
                        element_info['locators']['predicate string'].append(f"type == \"{element_type}\"")

            # Accessibility ID locator
            if ('accessibility id' not in element_info['locators'] or not element_info['locators']['accessibility id']) and \
               ('name' in attributes or 'label' in attributes):
                element_info['locators']['accessibility id'] = [
                    attributes.get('name', attributes.get('label', ''))
                ]

        elif device_controller.platform_name and device_controller.platform_name.lower() == 'android':
            # Android additional locators

            # Resource ID locator
            if 'resource-id' in attributes and attributes['resource-id']:
                if 'id' not in element_info['locators'] or not element_info['locators']['id']:
                    element_info['locators']['id'] = [attributes['resource-id']]

            # Content description locator (accessibility ID)
            if 'content-desc' in attributes and attributes['content-desc']:
                if 'accessibility id' not in element_info['locators'] or not element_info['locators']['accessibility id']:
                    element_info['locators']['accessibility id'] = [attributes['content-desc']]

            # Text locator
            if 'text' in attributes and attributes['text']:
                if 'text' not in element_info['locators'] or not element_info['locators']['text']:
                    element_info['locators']['text'] = [attributes['text']]

            # UiAutomator locator
            if 'uiautomator' not in element_info['locators'] or not element_info['locators']['uiautomator']:
                element_info['locators']['uiautomator'] = []

                if 'resource-id' in attributes and attributes['resource-id']:
                    element_info['locators']['uiautomator'].append(f"new UiSelector().resourceId(\"{attributes['resource-id']}\")")

                if 'text' in attributes and attributes['text']:
                    element_info['locators']['uiautomator'].append(f"new UiSelector().text(\"{attributes['text']}\")")

                if 'content-desc' in attributes and attributes['content-desc']:
                    element_info['locators']['uiautomator'].append(f"new UiSelector().description(\"{attributes['content-desc']}\")")

                if 'class' in attributes and attributes['class']:
                    element_info['locators']['uiautomator'].append(f"new UiSelector().className(\"{attributes['class']}\")")

        return jsonify({
            'success': True,
            'element': element_info,
            'coordinates': {'x': x, 'y': y}
        })
    except Exception as e:
        logger.error(f"Error finding element at position: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f"Error finding element: {str(e)}"
        }), 500

@app.route('/api/action/stop', methods=['POST'])
def stop_execution():
    global player # Make sure player is accessible

    app.logger.info("Received stop execution request")
    if player:
        try:
            player.stop() # Tell the player to stop
            app.logger.info("Player stop method called.")
            return jsonify({"success": True, "message": "Execution stop requested."})
        except Exception as e:
            app.logger.error(f"Error calling player.stop(): {e}")
            return jsonify({"success": False, "error": f"Error stopping execution: {str(e)}"}), 500
    else:
        app.logger.warning("Stop request received but player is not initialized.")
        return jsonify({"success": False, "error": "Player not initialized."}), 400

@app.route('/api/action/stop_test_case', methods=['POST'])
def stop_test_case():
    """
    Stop execution of a specific test case and mark it as skipped
    """
    global player

    app.logger.info("Received stop test case request")

    try:
        data = request.get_json() or {}
        test_idx = data.get('test_idx')
        test_case_name = data.get('test_case_name', 'Unknown Test Case')

        if test_idx is None:
            return jsonify({"success": False, "error": "test_idx is required"}), 400

        app.logger.info(f"Stopping test case: {test_case_name} (index: {test_idx})")

        if player and hasattr(player, 'stop_test_case'):
            try:
                # Call the player's stop_test_case method
                result = player.stop_test_case(test_idx, test_case_name)

                if result:
                    app.logger.info(f"Test case {test_case_name} stopped successfully")
                    return jsonify({
                        "success": True,
                        "message": f"Test case '{test_case_name}' stopped and marked as skipped"
                    })
                else:
                    app.logger.warning(f"Failed to stop test case {test_case_name}")
                    return jsonify({
                        "success": False,
                        "error": f"Failed to stop test case '{test_case_name}'"
                    }), 500

            except Exception as e:
                app.logger.error(f"Error stopping test case {test_case_name}: {e}")
                return jsonify({
                    "success": False,
                    "error": f"Error stopping test case: {str(e)}"
                }), 500
        else:
            app.logger.warning("Stop test case request received but player is not initialized or doesn't support stop_test_case")
            return jsonify({
                "success": False,
                "error": "Player not initialized or doesn't support test case stopping"
            }), 400

    except Exception as e:
        app.logger.error(f"Error processing stop test case request: {e}")
        return jsonify({"success": False, "error": f"Error processing request: {str(e)}"}), 500

@app.route('/api/execute_test_case', methods=['POST'])
def execute_test_case():
    """
    Execute a test case by filename with retry logic based on global settings
    """
    global device_controller, current_device, player, test_case_manager, current_test_idx, current_step_idx, device_controllers, action_factories, players

    # Try to get device_id from request data first, then fall back to session
    data = request.get_json() or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            player = players.get(session_device_key)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using session-specific controller for test execution: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            player = players.get(device_id)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using legacy controller for test execution: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({
                'status': 'error',
                'error': 'No device connected'
            }), 400
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({
            'status': 'error',
            'error': 'No device connected'
        }), 400

    try:
        # Get the filename from the request
        data = request.json
        if not data or 'filename' not in data:
            return jsonify({
                'status': 'error',
                'error': 'No filename provided'
            }), 400

        filename = data['filename']
        logger.info(f"=== EXECUTING TEST CASE: {filename} ===")

        # Get the Test Run Retry value from global settings
        import config
        max_retries = 0  # Default to no retries
        if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
            if 'Test Run Retry' in config.GLOBAL_VALUES:
                try:
                    max_retries = int(config.GLOBAL_VALUES['Test Run Retry'])
                    logger.info(f"Test Run Retry value from global settings: {max_retries}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid Test Run Retry value in global settings: {config.GLOBAL_VALUES['Test Run Retry']}")

        # Initialize retry counter
        retry_count = 0
        success = False
        last_error = None

        # Generate a unique suite ID for this execution
        import uuid
        suite_id = str(uuid.uuid4())
        logger.info(f"Generated suite ID for test execution: {suite_id}")

        # Reset any existing execution context to start fresh
        if hasattr(app, 'current_execution_id'):
            old_execution_id = app.current_execution_id
            app.current_execution_id = None
            logger.info(f"Reset previous execution_id: {old_execution_id}")

        # Set up execution context for test runs tracking
        execution_id = str(uuid.uuid4())[:10]  # Short unique ID
        app.current_execution_id = execution_id
        app.current_suite_id = suite_id
        app.current_test_idx = 0
        app.current_test_case_name = filename

        # Set the global flag to indicate individual test case execution (not test suite)
        global is_test_suite_execution
        is_test_suite_execution = False
        logger.info(f"Set execution context - ID: {execution_id}, Suite: {suite_id}, Test: {filename}, is_test_suite_execution: False")

        # Import database tracking functions
        from utils.database import track_test_execution, get_test_execution_status, clear_execution_tracking

        # Clear execution_tracking table before starting test execution (only for current test tracking)
        clear_result = clear_execution_tracking()
        logger.info(f"Cleared execution_tracking table before test case execution: {clear_result}")

        # Execute the test case with retries
        while retry_count <= max_retries and not success:
            # Update execution tracking in database
            track_test_execution(
                suite_id=suite_id,
                test_idx=0,  # Single test case execution always uses test_idx=0
                filename=filename,
                status="running",
                retry_count=retry_count,
                max_retries=max_retries,
                in_progress=True
            )

            if retry_count > 0:
                logger.info(f"=== RETRYING TEST CASE: {filename} (Attempt {retry_count} of {max_retries}) ===")

                # Notify the UI that we're retrying
                # Removed socketio emit for test retry
                # Give the UI time to update
                time.sleep(2)

                # Log that we're retrying but don't reset the database
                # This preserves screenshot history between retries
                logger.info("Preparing for retry without resetting database to preserve screenshots...")

            try:
                # Check database state before clearing
                from utils.database import check_database_state, clear_database, reset_database
                logger.info("Checking database state before clearing...")
                before_state = check_database_state()

                # Reset the database but preserve screenshots
                logger.info("Resetting database before test case execution (preserving screenshots)...")
                reset_success = reset_database()

                if reset_success:
                    logger.info("Successfully reset database before test case execution (screenshots preserved)")
                else:
                    # If reset fails, try the standard clear method
                    logger.warning("Database reset failed, trying standard clearing...")
                    clear_success = clear_database()

                    if not clear_success:
                        logger.error("BOTH reset_database AND clear_database failed - this may cause issues with test results")

                # Check database state after clearing
                logger.info("Checking database state after clearing...")
                after_state = check_database_state()
                logger.info(f"Database before: {before_state['suites_count']} suites, {before_state['cases_count']} cases, {before_state['steps_count']} steps, {before_state['screenshots_count']} screenshots")
                logger.info(f"Database after: {after_state['suites_count']} suites, {after_state['cases_count']} cases, {after_state['steps_count']} steps, {after_state['screenshots_count']} screenshots (screenshots are preserved)")

                # Final verification - we now expect screenshots to be preserved
                if after_state['screenshots_count'] > 0:
                    logger.info(f"Database contains {after_state['screenshots_count']} screenshots as expected (screenshots are preserved)")

                # Only perform emergency clearing if other tables still have data
                if after_state['suites_count'] > 0 or after_state['cases_count'] > 0 or after_state['steps_count'] > 0:
                    logger.error(f"CRITICAL: Database still contains data after clearing attempts: {after_state['suites_count']} suites, {after_state['cases_count']} cases, {after_state['steps_count']} steps")

                    # Last resort - try direct SQL with VACUUM
                    try:
                        import sqlite3
                        from utils.database import get_db_path

                        logger.info("Attempting emergency database clearing with VACUUM...")
                        conn = sqlite3.connect(get_db_path())

                        # Use PRAGMA to disable foreign keys and journal
                        conn.execute('PRAGMA foreign_keys = OFF')
                        conn.execute('PRAGMA journal_mode = OFF')

                        # Delete all data except screenshots
                        # conn.execute('DELETE FROM screenshots')  # Preserve screenshots - don't delete this table
                        conn.execute('DELETE FROM test_steps')
                        conn.execute('DELETE FROM test_cases')
                        conn.execute('DELETE FROM test_suites')

                        # Vacuum the database to reclaim space and reset
                        conn.execute('VACUUM')

                        # Restore settings
                        conn.execute('PRAGMA foreign_keys = ON')
                        conn.execute('PRAGMA journal_mode = DELETE')

                        conn.close()

                        logger.info("Emergency database clearing completed")

                        # Final check
                        final_state = check_database_state()
                        logger.info(f"Database after emergency clearing: {final_state['suites_count']} suites, {final_state['screenshots_count']} screenshots")
                    except Exception as db_error:
                        logger.error(f"Emergency database clearing also failed: {str(db_error)}")
                        # Continue execution anyway

                # Reset test_idx and step_idx for this execution
                # Start test_idx from 0 (first test case)
                # Start step_idx from 1 (first step) to match UI display
                current_test_idx.value = 0
                current_step_idx.value = 1
                logger.info(f"Reset test indices: test_idx={current_test_idx.value}, step_idx={current_step_idx.value}")

                # Reset execution tracking for this test case
                try:
                    from utils.database import reset_test_case_execution_tracking
                    reset_test_case_execution_tracking(suite_id, current_test_idx.value)
                    logger.info(f"Reset execution tracking for test case {current_test_idx.value} in suite {suite_id}")
                except Exception as reset_error:
                    logger.error(f"Error resetting execution tracking: {str(reset_error)}")

                # Load the test case
                test_case = test_case_manager.load_test_case(filename)
                if not test_case:
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=f"Test case not found: {filename}",
                        in_progress=False
                    )
                    return jsonify({
                        'status': 'error',
                        'error': f"Test case not found: {filename}",
                        'suite_id': suite_id
                    }), 404

                # Get the actions from the test case
                actions = test_case.get('actions', [])
                if not actions:
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=f"No actions found in test case: {filename}",
                        in_progress=False
                    )
                    return jsonify({
                        'status': 'error',
                        'error': f"No actions found in test case: {filename}",
                        'suite_id': suite_id
                    }), 400

                # Initialize player if needed with test_idx=0 for single test case execution
                if not player:
                    player = Player(device_controller=device_controller, test_cases_dir=TEST_CASES_DIR, test_idx=current_test_idx.value)
                    logger.info(f"Created player with test_idx={current_test_idx.value} for test case {filename}")
                else:
                    # Ensure the player has the correct test_idx
                    player.current_test_idx = current_test_idx.value
                    logger.info(f"Updated player's current_test_idx to {current_test_idx.value} for test case {filename}")
                    # We now use the database for tracking failures instead of last_error
                    logger.info("Using database for tracking failures in test case execution")

                # Set the current suite_id for screenshots
                global current_suite_id
                current_suite_id = suite_id

                # Execute the actions with test case boundaries
                # For individual test case execution, set start_index=0 and end_index=len(actions)
                # Explicitly pass the test_idx to ensure it's used consistently
                result, message = player.play(actions, suite_id=suite_id, test_case_start_index=0, test_case_end_index=len(actions), test_idx=current_test_idx.value)

                # Log the result for debugging
                logger.info(f"Test case execution result: success={result}, message={message}")

                # If execution was successful, set success flag to exit retry loop
                if result:
                    success = True
                    logger.info(f"Test case executed successfully on attempt {retry_count + 1}")

                    # Update execution tracking in database
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="passed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        in_progress=False
                    )
                else:
                    # If execution failed, store the error message and continue to next retry
                    last_error = message
                    logger.error(f"Test case execution failed on attempt {retry_count + 1}: {message}")

                    # Update execution tracking in database
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed" if retry_count >= max_retries else "retrying",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=message,
                        in_progress=retry_count < max_retries
                    )

                # Check database state after test execution
                logger.info("Checking database state after test execution...")
                from utils.database import check_database_state
                after_execution_state = check_database_state()
                logger.info(f"Database after test execution: {after_execution_state['suites_count']} suites, {after_execution_state['cases_count']} cases, {after_execution_state['steps_count']} steps, {after_execution_state['screenshots_count']} screenshots")

                # Do NOT clear screenshots after test execution - preserve them for retry
                # This was previously causing screenshots to be lost between retries
                # try:
                #     logger.info("Clearing screenshots after test execution...")
                #     clear_screenshots_response = clear_screenshots_route()
                #     logger.info(f"Screenshots cleared after test execution: {clear_screenshots_response.json}")
                # except Exception as clear_error:
                #     logger.error(f"Error clearing screenshots after test execution: {str(clear_error)}")

            except Exception as e:
                # If an exception occurred during execution, store the error and continue to next retry
                last_error = str(e)
                logger.error(f"Exception during test case execution on attempt {retry_count + 1}: {str(e)}")
                traceback.print_exc()

                # Update execution tracking in database
                track_test_execution(
                    suite_id=suite_id,
                    test_idx=0,
                    filename=filename,
                    status="error",
                    retry_count=retry_count,
                    max_retries=max_retries,
                    error=str(e),
                    in_progress=False
                )

            # Increment retry counter
            retry_count += 1

        # After all retries, generate a report for the test case
        report_url = None
        zip_url = None
        report_id = None

        try:
            # Get the execution tracking data for this suite
            from utils.database import get_execution_tracking_for_suite
            global current_report_dir
            execution_data = get_execution_tracking_for_suite(suite_id)
            logger.info(f"Retrieved {len(execution_data)} execution tracking entries from database for report")

            # Build data.json directly from the test case JSON file
            from utils.build_data_json import build_data_json_from_test_case

            # Get the full path to the test case file
            test_case_path = filename
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', filename)
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', f"{filename}.json")

            logger.info(f"Using test case path for data.json: {test_case_path}")

            suite_data = build_data_json_from_test_case(
                test_case_file=test_case_path,
                suite_id=suite_id,
                execution_data=execution_data,
                success=success,
                error=last_error if not success else None
            )

            if not suite_data:
                logger.error(f"Failed to build data.json from test case file: {filename}")
                # Fallback to using the database
                logger.info("Falling back to using the database for report generation")

                # Get the steps from the database for the report
                from utils.database import get_test_steps_for_suite
                test_steps = get_test_steps_for_suite(suite_id)
                logger.info(f"Retrieved {len(test_steps)} steps from database for report")

                # Try to enhance the steps with original test case data for INFO actions
                try:
                    # Load the original test case to get INFO action text
                    if os.path.exists(test_case_path):
                        with open(test_case_path, 'r') as f:
                            test_case = json.load(f)

                        # Create a mapping of action_id to original action data
                        action_map = {}
                        for action in test_case.get('actions', []):
                            action_id = action.get('action_id')
                            if action_id:
                                action_map[action_id] = action

                        # Enhance database steps with original action data
                        for step in test_steps:
                            step_action_id = step.get('action_id')
                            if step_action_id and step_action_id in action_map:
                                original_action = action_map[step_action_id]

                                # For INFO actions, add the original text content
                                if original_action.get('type') == 'info' and 'text' in original_action:
                                    step['text'] = original_action['text']
                                    step['original_text'] = original_action['text']
                                    step['name'] = original_action['text']  # Update step name
                                    logger.info(f"Enhanced INFO action step with text: {original_action['text']}")

                        logger.info("Successfully enhanced database steps with original test case data")
                except Exception as enhance_error:
                    logger.warning(f"Could not enhance steps with original test case data: {enhance_error}")

                # Create a screenshots map for the report based on action_id
                screenshots_map = {}
                for entry in execution_data:
                    if entry['action_id']:
                        # Use test_idx and step_idx as keys
                        key = f"{entry['test_idx']}_{entry['step_idx']}"
                        # Use action_id for the screenshot filename
                        screenshots_map[key] = f"{entry['action_id']}.png"
                        logger.info(f"Added screenshot mapping: {key} -> {entry['action_id']}.png")

                # Create a test suite data structure for the report
                suite_data = {
                    'name': f"Test Case: {os.path.basename(filename)}",
                    'id': suite_id,
                    'testCases': [
                        {
                            'name': os.path.basename(filename),
                            'id': filename,
                            'status': 'passed' if success else 'failed',
                            'duration': '0ms',  # We don't have accurate duration
                            'steps': test_steps  # Use the steps from the database
                        }
                    ],
                    'status': 'passed' if success else 'failed',
                    'passed': 1 if success else 0,
                    'failed': 0 if success else 1,
                    'skipped': 0,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'error': last_error if not success else None,
                    'screenshots_map': screenshots_map  # Add the screenshots map
                }

            # Make sure we have a report directory
            if not current_report_dir or not os.path.exists(current_report_dir):
                # Create report directory structure if it doesn't exist
                # Get reports folder from Settings tab configuration
                try:
                    from app.utils.directory_paths_db import directory_paths_db
                    reports_folder = directory_paths_db.get_path('REPORTS')
                    if not reports_folder:
                        # If not found in database, use config fallback
                        reports_folder = app.config.get('REPORTS_FOLDER')
                        if not reports_folder:
                            # Ultimate fallback - should not happen with proper validation
                            raise Exception("No reports folder configured in Settings tab")

                    # Ensure absolute path
                    if not os.path.isabs(reports_folder):
                        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                        reports_folder = os.path.join(base_dir, reports_folder)

                    logger.info(f"Using reports folder from Settings tab: {reports_folder}")
                except Exception as e:
                    logger.error(f"Error getting reports folder from Settings: {str(e)}")
                    return jsonify({
                        'status': 'error',
                        'error': 'Reports folder not configured in Settings tab. Please configure it before running tests.'
                    }), 400

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                testsuite_dir = f"testsuite_execution_{timestamp}"
                report_dir = os.path.join(reports_folder, testsuite_dir)
                screenshots_dir = os.path.join(report_dir, "screenshots")

                # Create both directories
                os.makedirs(report_dir, exist_ok=True)
                os.makedirs(screenshots_dir, exist_ok=True)

                # Update global variables
                current_report_dir = report_dir
                current_screenshots_dir = screenshots_dir
                current_report_timestamp = timestamp

                logger.info(f"Created report directory for test case: {report_dir}")

            # Save test suite data to database
            try:
                from utils.database import save_test_suite
                # Add report directory to suite data
                suite_data['report_dir'] = current_report_dir
                # Save to database
                save_test_suite(suite_data)
                logger.info(f"Saved test suite data to database for single test case execution")
            except Exception as db_error:
                logger.error(f"Error saving test suite data to database: {str(db_error)}")
                traceback.print_exc()

            # Generate the report only if this is a test suite execution
            if is_test_suite_execution:
                try:
                    from utils.reportGenerator import generateReport
                    # Pass the test case file path to ensure correct action_ids are used
                    report_path, zip_path = generateReport(suite_data, test_case_path)
                    logger.info(f"Generated test case report: {report_path}")

                    # Store the report path for later retrieval
                    app.config['LATEST_REPORT_PATH'] = report_path

                    # Get the report directory (parent of the report file)
                    report_dir = os.path.dirname(report_path)
                    # Use the directory name as the report identifier
                    report_id = os.path.basename(report_dir)
                    # Set the report URL and ZIP URL
                    report_url = f"/reports/{report_id}/mainreport.html"
                    zip_url = f"/api/reports/download_zip/{os.path.basename(zip_path)}"

                    logger.info(f"Report URLs generated - report_url: {report_url}, zip_url: {zip_url}")
                except Exception as report_error:
                    logger.error(f"Error generating report: {str(report_error)}")
                    traceback.print_exc()
            else:
                logger.info("Skipping report generation - individual test case execution (not from test suite)")
                report_path = None
                zip_path = None
        except Exception as report_gen_error:
            logger.error(f"Error preparing report data: {str(report_gen_error)}")
            traceback.print_exc()

        # Complete the test execution and update final status
        try:
            from utils.database import update_test_execution_status
            final_status = 'completed' if success else 'failed'
            update_test_execution_status(execution_id, final_status)
            logger.info(f"Test execution {execution_id} marked as {final_status}")

            # Clear the session execution ID
            app.current_execution_id = None
            logger.info(f"Cleared session execution_id after test completion")
        except Exception as completion_error:
            logger.error(f"Error completing test execution: {str(completion_error)}")

        # After all retries, return appropriate response with report URLs if available
        response_data = {
            'status': 'success' if success else 'error',
            'message': f"Test case executed successfully: {filename}" if success else "Test case execution failed",
            'retry_count': retry_count - 1,  # Subtract 1 because we increment after success
            'suite_id': suite_id,
            'execution_id': execution_id
        }

        # Add error message if execution failed
        if not success:
            response_data['error'] = last_error or "Unknown error occurred during test execution"

        # Add report URLs if available
        if report_url:
            response_data['report_url'] = report_url
        if zip_url:
            response_data['zip_url'] = zip_url
        if report_id:
            response_data['report_id'] = report_id

        # Return with appropriate status code
        if success:
            return jsonify(response_data)
        else:
            return jsonify(response_data), 500

    except Exception as e:
        logger.error(f"Error in execute_test_case route: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/action/execute', methods=['POST'])
def execute_single_action():
    """
    Execute a single action
    """
    global device_controller, current_device, action_factory, player, current_test_idx, current_step_idx, current_screenshots_dir, TEST_CASES_DIR, socketio, current_device_id, device_controllers, action_factories, players

    # Try to get device_id from request data first, then fall back to session
    data = request.json or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            action_factory = action_factories.get(session_device_key)
            player = players.get(session_device_key)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using session-specific controller for action execution: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            action_factory = action_factories.get(device_id)
            player = players.get(device_id)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using legacy controller for action execution: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({
                'success': False,
                'error': 'No device connected'
            })
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({
            'success': False,
            'error': 'No device connected'
        })

    try:
        # Get the action from the request
        data = request.json
        if not data or 'action' not in data:
            return jsonify({
                'success': False,
                'error': 'No action provided'
            })

        action = data['action']
        force_screenshot = data.get('force_screenshot', False)
        logger.info(f"Executing action: {action}")

        # 1. Apply existing parameter substitution (${paramname}) for specific fields
        try:
            from utils.parameter_utils import substitute_parameters
            action_type = action.get('type', '').lower()

            fields_to_substitute = []
            if action_type == 'text' and 'text' in action:
                fields_to_substitute.append('text')
            elif action_type == 'inputtext' and 'text' in action:
                fields_to_substitute.append('text')
            elif action_type == 'tapandtype' and 'text' in action:
                fields_to_substitute.append('text')
            elif action_type == 'tapontext' and 'text_to_find' in action:
                fields_to_substitute.append('text_to_find')
            # Add other action types and fields here if they need specific ${} substitution

            for field in fields_to_substitute:
                original_value = action.get(field)
                if isinstance(original_value, str): # Ensure it's a string
                    substituted_value = substitute_parameters(original_value)
                    if substituted_value != original_value:
                        logger.info(f"Parameter substitution for field '{field}' in execute_single_action: '{original_value}' -> '{substituted_value}'")
                        action[field] = substituted_value
        except Exception as e:
            logger.error(f"Error applying parameter substitution: {str(e)}")

        # 2. Apply environment variable substitution (env[varname]) to all string parameters in the action
        current_env_id = get_current_environment_id_from_session()
        if current_env_id is not None:
            logger.info(f"Applying environment variable resolution for env ID {current_env_id} to action params: {action}")
            for key, value in action.items():
                if isinstance(value, str):
                    original_action_param_value = value
                    resolved_value = resolve_text_with_env_variables(value, current_env_id)
                    if resolved_value != original_action_param_value:
                        action[key] = resolved_value
                        logger.info(f"  Resolved env var in action param '{key}': '{original_action_param_value}' -> '{resolved_value}'")
            logger.info(f"Action params after env var resolution: {action}")
        else:
            logger.info("No active environment, skipping env var resolution for action params.")

        # Initialize action factory if needed
        if not action_factory:
            from actions.action_factory import ActionFactory
            action_factory = ActionFactory()

        # Initialize player if needed with the current test_idx
        if not player:
            player = Player(device_controller=device_controller, test_cases_dir=TEST_CASES_DIR, test_idx=current_test_idx.value, socketio=socketio)
            logger.info(f"Created player with test_idx={current_test_idx.value} for single action execution")
        else:
            # Ensure the player has the correct attributes
            player.device_controller = device_controller
            player.test_cases_dir = TEST_CASES_DIR
            if 'socketio' in globals() and socketio: # Check if socketio is defined
                 player.socketio = socketio
            player.current_test_idx = current_test_idx.value # current_test_idx is global
            logger.info(f"Updated player's attributes (controller, test_cases_dir, socketio, test_idx) for single action execution")

        # Track execution time
        start_time = time.time()

        # Execute the action - pass only the action parameter
        result = player.execute_action(action)

        # Calculate execution time
        end_time = time.time()
        execution_time_ms = int((end_time - start_time) * 1000)

        # Check if result is a tuple (success, message) instead of a dictionary
        if isinstance(result, tuple):
            success = result[0] if len(result) > 0 else False
            message = result[1] if len(result) > 1 else "Unknown status"
            # Convert to dictionary format for consistent handling
            result = {
                'success': success,
                'message': message,
                'duration': f"{execution_time_ms}ms"
            }
        else:
            # Add duration to the result dictionary
            result['duration'] = f"{execution_time_ms}ms"

        # Add a small delay after action execution to allow UI to update
        try:
            logger.info("Adding delay after action execution to allow UI to update...")
            time.sleep(1)  # Wait for UI to update - 1 second should be sufficient in most cases
            logger.info("Delay completed, UI should be updated now")
        except Exception as delay_error:
            logger.warning(f"Failed to add delay after action: {delay_error}")

        # Always take a screenshot after the action for the report (after UI has updated)
        try:
            # Get suite_id from request if available
            suite_id = request.json.get('suite_id', '')
            logger.info(f"Using suite_id={suite_id} for screenshot")

            # Take screenshot with suite_id
            screenshot_result = device_controller.take_screenshot(suite_id=suite_id)
            logger.info(f"Saved screenshot after action and UI update: {screenshot_result}")

            # Generate a screenshot URL with timestamp for cache busting
            timestamp = int(time.time())
            screenshot_url = f"/screenshot?t={timestamp}"

            # Also save the screenshot to static/screenshots/latest.png for reports
            latest_path = os.path.join(app.root_path, 'static', 'screenshots', 'latest.png')
            if screenshot_result and screenshot_result.get('status') in ['success', 'partial_success'] and screenshot_result.get('path') and os.path.exists(screenshot_result['path']):
                shutil.copy2(screenshot_result['path'], latest_path)
                logger.info(f"Updated latest.png for reports")

                # Also save a timestamped version for the report system
                # Use the current_screenshots_dir from initialize_report_directory

                # If current_screenshots_dir is not set, we'll set it now
                if not current_screenshots_dir:
                    # Initialize the report directory structure
                    initialize_report_directory()

                # Create a standardized filename using step_{test_idx}_{step_idx}.png format
                # Get the current test case and step indices from the request data
                test_idx = request.json.get('test_idx', 0)
                step_idx = request.json.get('step_idx', 0)

                # Create global index holders if they don't exist
                if 'current_test_idx' not in globals():
                    current_test_idx = type('IndexHolder', (), {'value': 0})()
                if 'current_step_idx' not in globals():
                    current_step_idx = type('IndexHolder', (), {'value': 0})()

                # If we're executing actions in sequence, we need to increment the step index
                # Check if this is a new action in a sequence (not the first one)
                if action.get('type') != 'executeTestCase' and action.get('type') != 'executeTestSuite':
                    # Always increment the step index for sequential actions
                    current_step_idx.value += 1
                    test_idx = current_test_idx.value
                    step_idx = current_step_idx.value
                    logger.info(f"Incremented step index for sequential action: test_idx={test_idx}, step_idx={step_idx}")
                else:
                    # For test case/suite execution or if indices are explicitly provided, use those
                    current_test_idx.value = test_idx
                    current_step_idx.value = step_idx
                    logger.info(f"Using provided indices: test_idx={test_idx}, step_idx={step_idx}")

                # Make sure we're using integers
                try:
                    test_idx = int(test_idx)
                    step_idx = int(step_idx)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid test_idx or step_idx, using defaults. test_idx={test_idx}, step_idx={step_idx}")
                    test_idx = current_test_idx.value
                    step_idx = current_step_idx.value

                # Get or generate an action_id for this step
                action_id = action.get('action_id')
                if not action_id:
                    # Generate a new action_id
                    import random
                    import string
                    chars = string.ascii_letters + string.digits
                    action_id = ''.join(random.choice(chars) for _ in range(10))
                    action['action_id'] = action_id
                    logger.info(f"Generated new action_id for screenshot: {action_id}")

                # Create the standardized filename using action_id
                standardized_filename = f"{action_id}.png"

                # Save the screenshot directly to the report folder
                if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                    standardized_path = os.path.join(current_screenshots_dir, standardized_filename)
                    shutil.copy2(screenshot_result['path'], standardized_path)
                    logger.info(f"Saved screenshot directly to report folder: {standardized_path}")
                else:
                    logger.warning(f"Report screenshots directory not available, screenshot will be saved later: {standardized_filename}")

                # Track the mapping between step and screenshot for data.json
                try:
                    # Create a mapping entry in the execution tracking table
                    from utils.database import track_test_execution

                    # Get execution context parameters with fallbacks
                    suite_id = request.json.get('suite_id') or getattr(app, 'current_suite_id', '')
                    filename = request.json.get('filename') or getattr(app, 'current_test_case_name', 'unknown')
                    test_case_id = request.json.get('test_case_id', None)
                    execution_id = request.json.get('execution_id') or getattr(app, 'current_execution_id', None)

                    logger.info(f"Execution tracking parameters - suite_id: {suite_id}, filename: {filename}, test_case_id: {test_case_id}, execution_id: {execution_id}")

                    # Update the execution tracking with the action_id and screenshot path
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=test_idx,
                        step_idx=step_idx,
                        filename=filename,
                        action_type=action.get('type', 'unknown'),
                        action_params=action,
                        action_id=action_id,
                        status='in_progress',
                        in_progress=True,
                        test_case_id=test_case_id,
                        test_execution_id=execution_id
                    )
                    logger.info(f"Updated execution tracking with action_id {action_id} for step {test_idx}_{step_idx}")
                except Exception as track_error:
                    logger.error(f"Error updating execution tracking: {str(track_error)}")
                    logger.exception(track_error)
        except Exception as ss_error:
            logger.warning(f"Error taking screenshot: {str(ss_error)}")

        # Refresh page source after action
        try:
            device_controller.refresh_page_source()
        except Exception as ps_error:
            logger.warning(f"Error refreshing page source: {str(ps_error)}")

        # Check if result contains a screenshot_url
        result_screenshot_url = None
        if isinstance(result, dict):
            result_screenshot_url = result.get('screenshot_url')

        if result.get('success', False):
            logger.info(f"Action executed successfully in {execution_time_ms}ms")

            # Save test run data to test_runs table for Test Runs tab
            try:
                from utils.database import save_test_run_data
                execution_id = request.json.get('execution_id') or getattr(app, 'current_execution_id', '')
                test_case_name = request.json.get('filename') or getattr(app, 'current_test_case_name', 'unknown')
                suite_id = request.json.get('suite_id') or getattr(app, 'current_suite_id', '')

                # Generate or reuse execution_id for session-based test execution
                if not execution_id:
                    # Check if we have a session-based execution ID
                    if not hasattr(app, 'current_execution_id') or not app.current_execution_id:
                        import uuid
                        app.current_execution_id = str(uuid.uuid4())[:10]  # Short unique ID
                        logger.info(f"Generated new session execution_id: {app.current_execution_id}")
                    execution_id = app.current_execution_id
                    logger.info(f"Using session execution_id: {execution_id}")

                save_test_run_data(
                    unique_execution_id=execution_id,
                    test_case_name=test_case_name,
                    action_id=action_id,
                    step_status='pass',
                    testcase_status='running',
                    suite_status='running',
                    testcase_retry=False,
                    multistep='no',
                    suite_id=suite_id,
                    test_idx=test_idx,
                    step_idx=step_idx
                )
                logger.info(f"Saved test run data for successful action: {action_id}")
            except Exception as save_error:
                logger.error(f"Error saving test run data: {str(save_error)}")

            # Generate a timestamp for cache busting
            timestamp = int(time.time())
            # Use the screenshot URL from the result if available, otherwise use the default
            screenshot_url = result_screenshot_url if result_screenshot_url else f"/screenshot?t={timestamp}"
            logger.info(f"Using screenshot URL: {screenshot_url}")
            return jsonify({
                'success': True,
                'message': f"Action executed successfully: {action.get('type')}",
                'duration': f"{execution_time_ms}ms",
                'screenshot_url': screenshot_url,
                'action_id': action_id
            })
        else:
            error_msg = result.get('error') or result.get('message', 'Failed to execute action')
            logger.error(f"Failed to execute action: {error_msg}")

            # Save test run data to test_runs table for Test Runs tab
            try:
                from utils.database import save_test_run_data
                execution_id = request.json.get('execution_id') or getattr(app, 'current_execution_id', '')
                test_case_name = request.json.get('filename') or getattr(app, 'current_test_case_name', 'unknown')
                suite_id = request.json.get('suite_id') or getattr(app, 'current_suite_id', '')

                # Generate or reuse execution_id for session-based test execution
                if not execution_id:
                    # Check if we have a session-based execution ID
                    if not hasattr(app, 'current_execution_id') or not app.current_execution_id:
                        import uuid
                        app.current_execution_id = str(uuid.uuid4())[:10]  # Short unique ID
                        logger.info(f"Generated new session execution_id: {app.current_execution_id}")
                    execution_id = app.current_execution_id
                    logger.info(f"Using session execution_id: {execution_id}")

                save_test_run_data(
                    unique_execution_id=execution_id,
                    test_case_name=test_case_name,
                    action_id=action_id,
                    step_status='fail',
                    testcase_status='running',
                    suite_status='running',
                    testcase_retry=False,
                    multistep='no',
                    suite_id=suite_id,
                    test_idx=test_idx,
                    step_idx=step_idx
                )
                logger.info(f"Saved test run data for failed action: {action_id}")
            except Exception as save_error:
                logger.error(f"Error saving test run data: {str(save_error)}")

            # Generate a timestamp for cache busting
            timestamp = int(time.time())
            # Use the screenshot URL from the result if available, otherwise use the default
            screenshot_url = result_screenshot_url if result_screenshot_url else f"/screenshot?t={timestamp}"
            logger.info(f"Using screenshot URL for error response: {screenshot_url}")
            return jsonify({
                'success': False,
                'error': error_msg,
                'duration': f"{execution_time_ms}ms",
                'screenshot_url': screenshot_url,
                'action_id': action_id
            })

    except Exception as e:
        logger.error(f"Error executing action: {str(e)}")
        traceback.print_exc()

        # Save test run data to test_runs table for Test Runs tab
        try:
            from utils.database import save_test_run_data
            execution_id = request.json.get('execution_id', '') if request.json else ''
            test_case_name = request.json.get('filename', 'unknown') if request.json else 'unknown'
            suite_id = request.json.get('suite_id', '') if request.json else ''

            # Generate or reuse execution_id for session-based test execution
            if not execution_id:
                # Check if we have a session-based execution ID
                if not hasattr(app, 'current_execution_id') or not app.current_execution_id:
                    import uuid
                    app.current_execution_id = str(uuid.uuid4())[:10]  # Short unique ID
                    logger.info(f"Generated new session execution_id: {app.current_execution_id}")
                execution_id = app.current_execution_id
                logger.info(f"Using session execution_id: {execution_id}")

            save_test_run_data(
                unique_execution_id=execution_id,
                test_case_name=test_case_name,
                action_id=action_id if 'action_id' in locals() else 'unknown',
                step_status='error',
                testcase_status='running',
                suite_status='running',
                testcase_retry=False,
                multistep='no',
                suite_id=suite_id,
                test_idx=test_idx if 'test_idx' in locals() else 0,
                step_idx=step_idx if 'step_idx' in locals() else 0
            )
            logger.info(f"Saved test run data for error action")
        except Exception as save_error:
            logger.error(f"Error saving test run data: {str(save_error)}")

        # Generate a timestamp for cache busting
        timestamp = int(time.time())
        screenshot_url = f"/screenshot?t={timestamp}"
        return jsonify({
            'success': False,
            'error': str(e),
            'screenshot_url': screenshot_url,
            'action_id': action_id if 'action_id' in locals() else 'unknown'
        })

@app.route('/api/action/execute_hook', methods=['POST'])
def execute_hook_action():
    """
    Execute hook actions when a regular action fails
    """
    global device_controller, current_device, action_factory, player, current_test_idx, current_suite_id, device_controllers, action_factories, players

    # Try to get device_id from request data first, then fall back to session
    data = request.json or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            action_factory = action_factories.get(session_device_key)
            player = players.get(session_device_key)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using session-specific controller for hook execution: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            action_factory = action_factories.get(device_id)
            player = players.get(device_id)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using legacy controller for hook execution: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({
                'success': False,
                'error': 'No device connected'
            })
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({
            'success': False,
            'error': 'No device connected'
        })

    try:
        # Get the hook actions and failed action from the request
        data = request.json
        if not data or 'failed_action' not in data:
            return jsonify({
                'success': False,
                'error': 'Missing failed action'
            })

        # Get the test case ID from the request
        test_case_id = data.get('test_case_id', '')
        logger.info(f"Hook action execution for test case: {test_case_id}")

        # Check if we have a single hook action or multiple hook actions
        if 'action' in data:
            # Single hook action (backward compatibility)
            hook_actions = [data['action']]
        elif 'actions' in data:
            # Multiple hook actions
            all_hook_actions = data['actions']

            # Filter hook actions by test case ID if provided
            if test_case_id:
                # Get the test case from the test case manager
                if test_case_manager:
                    try:
                        # Load the test case to get its actions
                        test_case = test_case_manager.load_test_case(test_case_id)
                        if test_case and 'actions' in test_case:
                            # Find all hook actions in this test case
                            test_case_hook_actions = [action for action in test_case['actions'] if action.get('type') == 'hookAction']
                            logger.info(f"Found {len(test_case_hook_actions)} hook actions in test case {test_case_id}")

                            # Use only the hook actions from this test case
                            hook_actions = test_case_hook_actions
                            logger.info(f"Using {len(hook_actions)} hook actions from test case {test_case_id}")
                        else:
                            logger.warning(f"Test case {test_case_id} not found or has no actions, using all hook actions")
                            hook_actions = all_hook_actions
                    except Exception as e:
                        logger.error(f"Error loading test case {test_case_id}: {str(e)}")
                        logger.error(traceback.format_exc())
                        # Fallback to using all hook actions
                        hook_actions = all_hook_actions
                else:
                    logger.warning("Test case manager not available, using all hook actions")
                    hook_actions = all_hook_actions
            else:
                logger.warning("No test case ID provided, using all hook actions")
                hook_actions = all_hook_actions
        else:
            return jsonify({
                'success': False,
                'error': 'Missing hook action(s)'
            })

        failed_action = data['failed_action']
        failed_action_index = data.get('failed_action_index', -1)
        force_screenshot = data.get('force_screenshot', False)

        logger.info(f"Executing {len(hook_actions)} hook action(s) for test case: {test_case_id}")
        logger.info(f"Failed action: {failed_action}")

        # Log detailed information about each hook action
        for i, hook_action in enumerate(hook_actions):
            hook_type = hook_action.get('hook_type', 'unknown')
            hook_data = hook_action.get('hook_data', {})
            logger.info(f"Hook action {i+1}/{len(hook_actions)}: type={hook_type}, data={hook_data}")

        # Initialize action factory if needed
        if not action_factory:
            from actions.action_factory import ActionFactory
            action_factory = ActionFactory(device_controller)

        # Track execution time
        start_time = time.time()

        # Track results for all hook actions
        all_results = []
        any_hook_succeeded = False
        final_result = None
        final_screenshot_url = None

        # Import the function to check hook execution count
        from utils.database import get_hook_execution_count, track_test_execution

        # Get the current test index from the global variable
        # current_test_idx is already available as a global variable

        # Execute each hook action in sequence
        for hook_index, hook_action in enumerate(hook_actions):
            # Extract the hook type and data
            hook_type = hook_action.get('hook_type', 'unknown')
            hook_data = hook_action.get('hook_data', {})

            if not hook_type:
                logger.error(f"Hook action {hook_index+1}/{len(hook_actions)} missing hook_type")
                all_results.append({
                    'success': False,
                    'error': 'Hook action missing hook_type'
                })
                continue

            # Check if this hook action has been executed three times already
            hook_execution_count = get_hook_execution_count(
                suite_id=current_suite_id,
                test_idx=current_test_idx.value,
                action_type='hookAction'
            )

            if hook_execution_count >= 3:
                logger.warning(f"Hook action has been executed {hook_execution_count} times already, skipping the rest of the hook actions")
                all_results.append({
                    'success': False,
                    'error': f'Hook action execution limit reached ({hook_execution_count}/3)'
                })

                # Break the loop to skip the rest of the hook actions
                break

            logger.info(f"Executing Hook Action {hook_index+1}/{len(hook_actions)} via dedicated handler...")

            # Convert the hook action to a regular action
            # Create a new action with the hook type and data
            recovery_action = {
                'type': hook_type
            }

            # Handle specific action types and populate recovery_action
            if hook_type == 'tap':
                method = hook_data.get('method', 'coordinates')
                recovery_action['method'] = method
                if method == 'coordinates':
                    recovery_action['x'] = hook_data.get('x', 0)
                    recovery_action['y'] = hook_data.get('y', 0)
                elif method == 'locator':
                    recovery_action['locator_type'] = hook_data.get('locator_type', '')
                    recovery_action['locator_value'] = hook_data.get('locator_value', '')
                elif method == 'image':
                    recovery_action['image_filename'] = hook_data.get('image_filename', '')
                    recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                    recovery_action['timeout'] = hook_data.get('timeout', 10)
                elif method == 'text': # This was method == 'text', assuming it's for tap on text
                    recovery_action['text'] = hook_data.get('text', '') # or text_to_find based on action
                    recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                    recovery_action['timeout'] = hook_data.get('timeout', 10)
            elif hook_type == 'text': # For input text action
                recovery_action['text'] = hook_data.get('text', '')
            # Add other specific hook_type to recovery_action mappings as needed
            else:
                # For other action types, just copy all hook_data
                for key, value in hook_data.items():
                    recovery_action[key] = value

            logger.info(f"Constructed recovery_action before substitutions: {recovery_action}")

            # 1. Apply existing parameter substitution (${paramname}) to relevant fields
            try:
                from utils.parameter_utils import substitute_parameters
                # Iterate over a copy of items for safe modification
                for key, value in list(recovery_action.items()): 
                    if isinstance(value, str):
                        original_value = value
                        substituted_value = substitute_parameters(original_value)
                        if substituted_value != original_value:
                            logger.info(f"Parameter substitution in hook recovery_action field '{key}': '{original_value}' -> '{substituted_value}'")
                            recovery_action[key] = substituted_value
            except Exception as e:
                logger.error(f"Error applying parameter substitution to hook recovery_action: {str(e)}")

            # 2. Apply environment variable substitution (env[varname]) to all string parameters
            current_env_id = get_current_environment_id_from_session()
            if current_env_id is not None:
                logger.info(f"Applying environment variable resolution for env ID {current_env_id} to recovery_action: {recovery_action}")
                # Iterate over a copy of items for safe modification
                for key, value in list(recovery_action.items()): 
                    if isinstance(value, str):
                        original_action_param_value = value
                        resolved_value = resolve_text_with_env_variables(value, current_env_id)
                        if resolved_value != original_action_param_value:
                            recovery_action[key] = resolved_value
                            logger.info(f"  Resolved env var in recovery_action param '{key}': '{original_action_param_value}' -> '{resolved_value}'")
                logger.info(f"Recovery_action params after env var resolution: {recovery_action}")
            else:
                logger.info("No active environment, skipping env var resolution for recovery_action params.")

            logger.info(f"Converted hook action to: {recovery_action}") # Log final recovery_action
            logger.info(f"Action failed. Executing Hook Action {hook_index+1}/{len(hook_actions)} ({hook_type}) for recovery...")

            # Execute the action directly using its type (e.g., 'tap') and the resolved recovery_action parameters
            result = action_factory.execute_action(hook_type, recovery_action) # Pass the modified recovery_action

            # Process the result
            if isinstance(result, dict):
                success = result.get('status') == 'success' or result.get('success', False)
                if success:
                    any_hook_succeeded = True
            elif isinstance(result, tuple) and len(result) >= 2:
                success = result[0]
                if success:
                    any_hook_succeeded = True
            else:
                success = bool(result)
                if success:
                    any_hook_succeeded = True

            # Store the result for this hook action
            all_results.append(result)

            # Keep track of the last result for the response
            final_result = result

            # Track the hook action execution in the database
            try:
                # Track the execution with action details
                track_test_execution(
                    suite_id=current_suite_id,
                    test_idx=current_test_idx.value,
                    step_idx=hook_index,  # Use hook_index as step_idx
                    filename=test_case_id or 'unknown',
                    status='success' if success else 'failed',
                    in_progress=False,
                    action_type='hookAction',  # Use 'hookAction' as action_type
                    action_params=hook_action
                )
                logger.info(f"Tracked hook action execution in database: suite_id={current_suite_id}, test_idx={current_test_idx.value}, step_idx={hook_index}")
            except Exception as track_error:
                logger.error(f"Error tracking hook action execution: {str(track_error)}")

            # Add a small delay between hook actions
            time.sleep(0.5)

        # Calculate execution time
        end_time = time.time()
        execution_time_ms = int((end_time - start_time) * 1000)

        # Add a small delay after all hook actions to allow UI to update
        try:
            logger.info("Adding delay after hook actions execution to allow UI to update...")
            time.sleep(1)
        except Exception as delay_error:
            logger.warning(f"Failed to add delay after hook actions: {delay_error}")

        # Take a screenshot after all hook actions
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None
        screenshot_url = None

        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)
                # Generate a URL for the screenshot
                screenshot_url = f"/screenshots/{os.path.basename(screenshot_path)}?t={int(time.time())}"

        # Prepare the response
        if any_hook_succeeded:
            logger.info(f"At least one hook action succeeded in {execution_time_ms}ms")

            # Create a summary of all hook actions
            hook_actions_summary = []
            for i, result in enumerate(all_results):
                if i >= len(hook_actions):
                    continue

                hook_action = hook_actions[i]
                hook_type = hook_action.get('hook_type', 'unknown')

                # Determine success status
                if isinstance(result, dict):
                    success = result.get('status') == 'success' or result.get('success', False)
                    message = result.get('message', '')
                elif isinstance(result, tuple) and len(result) >= 2:
                    success = result[0]
                    message = result[1]
                else:
                    success = bool(result)
                    message = "Hook action executed successfully" if success else "Hook action failed"

                hook_actions_summary.append({
                    'index': i + 1,
                    'type': hook_type,
                    'success': success,
                    'message': message
                })

            return jsonify({
                'success': True,
                'message': f"Executed {len(hook_actions)} hook actions, at least one succeeded",
                'duration': f"{execution_time_ms}ms",
                'hook_actions_summary': hook_actions_summary,
                'screenshot_url': screenshot_url
            })
        else:
            logger.error(f"All hook actions failed in {execution_time_ms}ms")

            # Create a summary of all hook actions
            hook_actions_summary = []
            error_messages = []

            for i, result in enumerate(all_results):
                if i >= len(hook_actions):
                    continue

                hook_action = hook_actions[i]
                hook_type = hook_action.get('hook_type', 'unknown')

                # Determine error message
                if isinstance(result, dict):
                    error_msg = result.get('error') or result.get('message', '')
                    error_messages.append(error_msg)
                elif isinstance(result, tuple) and len(result) >= 2:
                    error_messages.append(result[1])
                else:
                    error_messages.append("Hook action failed")

                hook_actions_summary.append({
                    'index': i + 1,
                    'type': hook_type,
                    'success': False,
                    'message': error_messages[-1]
                })

            return jsonify({
                'success': False,
                'error': "All hook actions failed: " + "; ".join(error_messages),
                'duration': f"{execution_time_ms}ms",
                'hook_actions_summary': hook_actions_summary,
                'screenshot_url': screenshot_url
            })

    except Exception as e:
        logger.error(f"Error executing hook action: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/page_source', methods=['GET'])
def get_page_source():
    """Get the current page source XML from the app"""
    global device_controller, current_device_id, device_controllers

    # Check device connection using new system first
    if current_device_id and current_device_id in device_controllers:
        device_controller = device_controllers[current_device_id]

    if not device_controller or not device_controller.driver:
        return jsonify({
            "success": False,
            "error": "No device connected"
        }), 400

    try:
        # Get the page source from Appium
        page_source = device_controller.driver.page_source

        # Get the platform name from driver capabilities instead
        platform = "unknown"
        try:
            if hasattr(device_controller.driver, 'capabilities'):
                caps = device_controller.driver.capabilities
                if caps and 'platformName' in caps:
                    platform = caps['platformName'].lower()
        except Exception as platform_err:
            logger.warning(f"Error getting platform from capabilities: {platform_err}")

        return jsonify({
            "success": True,
            "source": page_source,
            "platform": platform
        })
    except Exception as e:
        logger.error(f"Error getting page source: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"Error getting page source: {str(e)}"
        }), 500

@app.route('/api/session_info', methods=['GET'])
def get_session_info():
    """Get the current Appium session information"""
    global device_controller

    if not device_controller or not device_controller.driver:
        return jsonify({
            "success": False,
            "error": "No device connected"
        }), 400

    try:
        # Get the session ID and capabilities
        session_id = device_controller.driver.session_id
        capabilities = device_controller.driver.capabilities

        return jsonify({
            "success": True,
            "session_id": session_id,
            "capabilities": capabilities
        })
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"Error getting session info: {str(e)}"
        }), 500

@app.route('/api/test_cases/files', methods=['GET'])
def list_test_case_files():
    """List test case files in the test_cases directory"""
    try:
        test_cases_dir = os.path.join(os.path.abspath(os.getcwd()), 'test_cases')

        if not os.path.exists(test_cases_dir):
            return jsonify({
                "status": "error",
                "error": "Test cases directory does not exist"
            })

        # Get all JSON files in the test_cases directory
        files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]

        return jsonify({
            "status": "success",
            "files": files
        })
    except Exception as e:
        logger.error(f"Error listing test case files: {str(e)}")
        return jsonify({"status": "error", "error": str(e)})

@app.route('/api/test_cases/load_file/<filename>', methods=['GET'])
def load_test_case_file(filename):
    """Load a test case from a file"""
    try:
        test_cases_dir = os.path.join(os.path.abspath(os.getcwd()), 'test_cases')
        file_path = os.path.join(test_cases_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({
                "status": "error",
                "error": f"Test case file not found: {filename}"
            })

        # Read the JSON file
        with open(file_path, 'r') as f:
            test_case_data = json.load(f)

        # Validate and process actions
        actions = []
        if 'actions' in test_case_data and test_case_data['actions']:
            for action in test_case_data['actions']:
                # Ensure action type is valid
                if 'type' in action:
                    # Special handling for hookAction
                    if action['type'] == 'hookAction':
                        logger.info(f"Processing hookAction in test case {filename}")
                        # Ensure hook_type is present
                        if 'hook_type' not in action:
                            logger.warning(f"hookAction missing hook_type in {filename}")
                            action['hook_type'] = 'tap'  # Default to tap as fallback
                    actions.append(action)
                else:
                    logger.warning(f"Action missing type in {filename}, skipping")

        return jsonify({
            "status": "success",
            "actions": actions
        })
    except Exception as e:
        logger.error(f"Error loading test case: {str(e)}")
        return jsonify({"status": "error", "error": str(e)})

@app.route('/api/session/info', methods=['GET'])
def get_session_info_route():
    """Get information about the current Appium session."""
    if not device_controller or not device_controller.device_id:
        return jsonify({'success': False, 'error': 'Device not connected'}), 400

    try:
        session_info = device_controller.get_session_info()
        if session_info:
            return jsonify({'success': True, 'session_info': session_info})
        else:
            # This might happen if connected via Airtest where session info is limited
            # Or if something went wrong storing info after Appium connect
            # Return success but indicate limited info
            basic_info = {
               'session_id': 'N/A (Airtest or Info Missing)',
               'capabilities': device_controller.session_capabilities or {'error': 'Capabilities not available'}
            }
            return jsonify({'success': True, 'session_info': basic_info, 'message': 'Limited session info available'})
    except Exception as e:
        app.logger.error(f"Error getting session info: {e}")
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/appium/inspect_element', methods=['POST'])
def inspect_element():
    """Inspect element at coordinates and return detailed information for native or webview elements."""
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    if not device_id:
        return jsonify({'success': False, 'error': 'No device connected'})

    # Create session-specific key
    session_device_key = f"{session_id}_{client_session_id}_{device_id}"

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]

    if not device_controller or not device_controller.driver:
        return jsonify({'success': False, 'error': 'No device connected to Appium'})

    try:
        data = request.json
        x = data.get('x')
        y = data.get('y')

        if x is None or y is None:
            return jsonify({'success': False, 'error': 'X and Y coordinates are required'})

        # Try to get element with retry logic (keeps existing retry)
        max_retries = 3
        retry_delay = 2  # seconds
        last_error = None

        for attempt in range(max_retries):
            try:
                # Get element info - this now handles both native and webview
                element_info = device_controller.get_element_at_position(int(x), int(y))

                if not element_info:
                    # If after all retries, no element is found, return error
                    if attempt == max_retries - 1:
                        return jsonify({
                            'success': False,
                            'error': 'No element found at position (checked native and webview)',
                            'coordinates': {'x': x, 'y': y}
                        })
                    else:
                        # Allow retry if not the last attempt
                        logger.warning(f"Attempt {attempt + 1}: No element found, retrying...")
                        time.sleep(retry_delay)
                        continue

                # Successfully found element info (either native or webview)
                # The structure returned by get_element_at_position should now be consistent:
                # {'type': 'native'|'webview', 'attributes': {...}, 'strategies': {...}, ...}

                response_data = {
                    'success': True,
                    'element': element_info, # Pass the whole structure
                    'coordinates': {'x': x, 'y': y}
                }

                # Log the result before returning
                logger.info(f"Inspect Element successful: Type='{element_info.get('type')}'")

                return jsonify(response_data)

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Inspect Element Attempt {attempt + 1} failed: {last_error}")
                traceback.print_exc() # Log full traceback for debugging

                # Existing crash detection and recovery logic
                if "UiAutomator2 has crashed" in last_error or \
                   "instrumentation process is not running" in last_error or \
                   "Could not proxy command" in last_error or \
                   "An unknown server-side error occurred" in last_error:
                    logger.info("Appium/UiAutomator2 potentially crashed, attempting recovery...")
                    # Try reconnecting which includes server restart logic
                    try:
                        if hasattr(device_controller, 'reconnect_device'):
                             reconnected = device_controller.reconnect_device()
                             if reconnected:
                                 logger.info("Recovery successful, retrying inspect...")
                                 # Add a small delay before retrying after reconnect
                                 time.sleep(2)
                             else:
                                 logger.error("Recovery attempt failed.")
                                 # If recovery fails, break the retry loop
                                 break
                        else:
                            logger.error("Recovery mechanism (reconnect_device) not found.")
                            break
                    except Exception as recovery_err:
                        logger.error(f"Error during recovery attempt: {recovery_err}")
                        break # Stop retrying if recovery itself fails

                elif attempt < max_retries - 1:
                    logger.info(f"Retrying inspect element after delay...")
                    time.sleep(retry_delay)
                else:
                     # Last attempt failed, break loop
                     break

        # If loop finishes without success (either no element found or unrecoverable error)
        return jsonify({
            'success': False,
            'error': f"Failed to inspect element after {max_retries} attempts. Last error: {last_error}",
            'coordinates': {'x': x, 'y': y}
        })

    except Exception as e:
        logger.error(f"Error in /appium/inspect_element route: {str(e)}")
        traceback.print_exc()
        return jsonify({'success': False, 'error': f"Unexpected error: {str(e)}"}) # Return generic error

@app.route('/api/test_cases/load/<filename>', methods=['GET'])
def load_specific_test_case(filename):
    """Load a specific test case by filename"""
    global test_case_manager

    try:
        # Load the test case using TestCaseManager
        test_case = test_case_manager.load_test_case(filename)

        if not test_case:
            return jsonify({
                "status": "error",
                "error": f"Test case not found: {filename}"
            }), 404

        # Process and validate actions, especially hook actions
        if 'actions' in test_case and test_case['actions']:
            for action in test_case['actions']:
                # Ensure action type is valid
                if 'type' in action:
                    # Special handling for hookAction
                    if action['type'] == 'hookAction':
                        logger.info(f"Processing hookAction in test case {filename}")
                        # Ensure hook_type is present
                        if 'hook_type' not in action:
                            logger.warning(f"hookAction missing hook_type in {filename}")
                            action['hook_type'] = 'tap'  # Default to tap as fallback
                else:
                    logger.warning(f"Action missing type in {filename}")
                    action['type'] = 'unknown'  # Add a default type

        return jsonify({
            "status": "success",
            "test_case": test_case
        })
    except Exception as e:
        logger.error(f"Error loading test case {filename}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/test_cases_for_multi_step', methods=['GET'])
def get_test_cases_for_multi_step():
    """Get all test cases for multi-step action dropdown"""
    global test_case_manager

    try:
        # Get all test cases
        test_cases_list = test_case_manager.get_test_cases()

        # Format test cases for dropdown
        formatted_test_cases = []
        for test_case in test_cases_list:
            # Load the full test case to get the steps
            full_test_case = test_case_manager.load_test_case(test_case['filename'])
            if full_test_case:
                formatted_test_cases.append({
                    "id": test_case['filename'],
                    "name": test_case['name'],
                    "steps_count": len(full_test_case.get("actions", [])),
                    "description": full_test_case.get("description", "")
                })

        return jsonify({"test_cases": formatted_test_cases})
    except Exception as e:
        logger.error(f"Error getting test cases for multi-step: {str(e)}")
        return jsonify({"error": str(e)}), 500

# This endpoint was removed because it was a duplicate of the one above
# The original endpoint at line ~1800 is now the only one handling /api/reference_images

@app.route('/api/device/health_check', methods=['GET'])
def check_device_health():
    """Check the health of the device connection"""
    global device_controller, current_device, current_device_id, device_controllers

    try:
        # Check using new device management system first
        if current_device_id and current_device_id in device_controllers:
            controller = device_controllers[current_device_id]
            logger.info(f"Health check - using new system: device_id={current_device_id}, controller exists: {controller is not None}")

            # Check if the controller is still connected
            if controller and hasattr(controller, 'driver') and controller.driver:
                try:
                    # Try to get session info to verify connection
                    session_info = controller.driver.session
                    if session_info:
                        logger.info(f"Device {current_device_id} is healthy - session: {session_info}")
                        return jsonify({
                            "success": True,
                            "healthy": True,
                            "message": f"Device {current_device_id} is connected and healthy",
                            "device_id": current_device_id,
                            "session_id": session_info
                        })
                except Exception as e:
                    logger.warning(f"Device {current_device_id} session check failed: {e}")

        # Fallback to old system for backward compatibility
        logger.info(f"Health check - fallback to old system: current_device={current_device}, device_controller exists: {device_controller is not None}")

        # If we're not connected to any device, return error
        if not current_device_id and (not current_device or not device_controller):
            logger.warning(f"No device currently connected - current_device_id: {current_device_id}, current_device: {current_device}, device_controller: {device_controller is not None}")

            return jsonify({
                "success": False,
                "healthy": False,
                "message": "No device currently connected"
            })

        # Check if the session is active and responsive
        logger.info(f"Checking health of device connection: {current_device}")
        is_healthy = device_controller.is_session_active()

        if is_healthy:
            logger.info(f"Device connection is healthy: {current_device}")

            # Removed socketio emit for health status

            return jsonify({
                "success": True,
                "healthy": True,
                "message": "Device connection is healthy"
            })
        else:
            logger.warning(f"Device connection is unhealthy: {current_device}")

            # Removed socketio emit for health status

            return jsonify({
                "success": True,
                "healthy": False,
                "message": "Device connection is unresponsive"
            })
    except Exception as e:
        logger.error(f"Error checking device health: {str(e)}")
        logger.error(traceback.format_exc())

        # Removed socketio emit for health status

        return jsonify({
            "success": False,
            "healthy": False,
            "message": f"Error checking device health: {str(e)}"
        })

@app.route('/api/device/reconnect', methods=['POST'])
def reconnect_device():
    """Reconnect to the device to recover from server crashes or connection issues"""
    global device_controller, current_device

    try:
        # If we're not connected to any device, return error
        if not current_device:
            return jsonify({"status": "error", "message": "No device currently connected"})

        # Log the reconnection attempt
        logger.info(f"[DEVICE RECOVERY] Attempting to reconnect to device: {current_device}")

        # First try to disconnect the device
        logger.info(f"[DEVICE RECOVERY] Trying to disconnect device first: {current_device}")
        try:
            # This might fail if the device is not connected, but that's okay
            device_controller.disconnect()
            logger.info(f"[DEVICE RECOVERY] Successfully disconnected device")
        except Exception as e:
            logger.warning(f"[DEVICE RECOVERY] Failed to disconnect device, but continuing: {str(e)}")

        # Try to recover UiAutomator2 server via ADB
        try:
            # Stop UiAutomator2 server processes
            subprocess.run(['adb', '-s', current_device, 'shell', 'am', 'force-stop', 'io.appium.uiautomator2.server'],
                           check=False, timeout=5)
            subprocess.run(['adb', '-s', current_device, 'shell', 'am', 'force-stop', 'io.appium.uiautomator2.server.test'],
                           check=False, timeout=5)

            # Small delay to let the system clean up
            time.sleep(2)
            logger.info(f"[DEVICE RECOVERY] Successfully reset UiAutomator2 processes")
        except Exception as e:
            logger.warning(f"[DEVICE RECOVERY] Failed to reset UiAutomator2 processes: {str(e)}")

        # Try to reconnect
        logger.info(f"[DEVICE RECOVERY] Connecting to device: {current_device}")
        success = device_controller.connect_to_device(current_device)

        if success:
            # Re-initialize Airtest specifically if available
            if hasattr(device_controller, '_init_airtest'):
                logger.info(f"[DEVICE RECOVERY] Initializing Airtest for device: {current_device}")

                # Force cleanup of any existing Airtest devices
                try:
                    from airtest.core.helper import G
                    if G.DEVICE:
                        logger.info(f"[DEVICE RECOVERY] Found existing G.DEVICE, cleaning up: {G.DEVICE}")
                        try:
                            old_device = G.DEVICE
                            G.DEVICE = None
                            G.device_list.pop(old_device, None)
                        except Exception as e:
                            logger.warning(f"[DEVICE RECOVERY] Error during G.DEVICE cleanup: {str(e)}")
                except Exception as e:
                    logger.warning(f"[DEVICE RECOVERY] Error accessing G: {str(e)}")

                # Now initialize Airtest
                airtest_success = device_controller._init_airtest(current_device)
                logger.info(f"[DEVICE RECOVERY] Airtest initialization result: {airtest_success}")

            # Reinitialize the player with the current test_idx
            global player, current_test_idx
            if 'current_test_idx' not in globals():
                current_test_idx = type('IndexHolder', (), {'value': 0})()

            # Recreate the player with the current test_idx
            player = Player(device_controller=device_controller, test_cases_dir=TEST_CASES_DIR, test_idx=current_test_idx.value)
            logger.info(f"[DEVICE RECOVERY] Reinitialized player with test_idx={current_test_idx.value}")

            # Take a fresh screenshot to verify the connection works
            screenshot_result = device_controller.take_screenshot()
            screenshot_success = screenshot_result is not None and screenshot_result.get('status') == 'success'

            # Get screenshot URL if available
            screenshot_url = None
            if screenshot_success and screenshot_result.get('path'):
                screenshot_path = screenshot_result.get('path')
                screenshot_url = f"/screenshots/{os.path.basename(screenshot_path)}"
                logger.info(f"[DEVICE RECOVERY] Generated screenshot URL: {screenshot_url}")

            if screenshot_success:
                logger.info(f"[DEVICE RECOVERY] Successfully took a screenshot, connection is working")
            else:
                logger.warning(f"[DEVICE RECOVERY] Failed to take a screenshot after reconnection")

            logger.info(f"[DEVICE RECOVERY] Successfully reconnected to device: {current_device}")

            # Removed socketio emit for recovery status

            return jsonify({
                "status": "success",
                "message": f"Successfully reconnected to device: {current_device}",
                "success": True,
                "screenshot_url": screenshot_url
            })
        else:
            logger.error(f"[DEVICE RECOVERY] Failed to reconnect to device: {current_device}")

            # Removed socketio emit for recovery status

            return jsonify({
                "status": "error",
                "message": f"Failed to reconnect to device: {current_device}",
                "success": False
            })
    except Exception as e:
        logger.error(f"[DEVICE RECOVERY] Error reconnecting to device: {str(e)}")

        # Removed socketio emit for recovery status

        return jsonify({
            "status": "error",
            "message": f"Error reconnecting to device: {str(e)}",
            "success": False
        })

@app.route('/api/device/uninstall_app', methods=['POST'])
def uninstall_app():
    global device_controller

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json
    package_id = data.get('package_id')

    if not package_id:
        return jsonify({"status": "error", "error": "Missing package_id parameter"}), 400

    try:
        result = device_controller.uninstall_app(package_id)

        # Take screenshot after action
        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None
        if screenshot_result and screenshot_result.get('status') == 'success':
            screenshot_path = screenshot_result.get('path')
            if screenshot_path:
                screenshot_data = encode_image_to_base64(screenshot_path)

        # Handle different result formats
        if isinstance(result, dict):
            # If result is a dict, use its status and message
            status = result.get('status', 'success')
            message = result.get('message', f"Operation completed for app {package_id}")

            response = {
                "status": status,
                "message": message,
                "screenshot": screenshot_data
            }

            # Return appropriate HTTP status code
            if status == 'error':
                return jsonify(response), 500
            else:
                return jsonify(response)
        elif result is True:
            # If result is True, return success
            return jsonify({
                "status": "success",
                "message": f"App {package_id} uninstalled",
                "screenshot": screenshot_data
            })
        else:
            # For any other result (including False), return error
            return jsonify({
                "status": "error",
                "error": f"Failed to uninstall app {package_id}",
                "screenshot": screenshot_data
            }), 500
    except Exception as e:
        logger.error(f"Error uninstalling app: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/device/fix_emulator', methods=['POST'])
def fix_emulator_issues():
    """Apply fixes for known UiAutomator2 issues with emulators"""
    global device_controller, current_device

    if not device_controller or not current_device:
        return jsonify({"status": "error", "message": "No device connected"}), 400

    try:
        logger.info("Applying emulator fixes")

        # Check if the device is an emulator
        is_emulator = 'emulator' in current_device or 'localhost' in current_device
        if not is_emulator:
            logger.info("Connected device is not an emulator, skipping fixes")
            return jsonify({
                "status": "success",
                "message": "Device is not an emulator, no fixes needed"
            })

        # Apply the fixes
        if hasattr(device_controller, 'fix_emulator_issues'):
            success = device_controller.fix_emulator_issues()
            if success:
                logger.info("Successfully applied emulator fixes")

                # Take a screenshot to verify connection is still working
                screenshot_result = device_controller.take_screenshot()
                if screenshot_result is not None:
                    return jsonify({
                        "status": "success",
                        "message": "Successfully applied emulator fixes",
                        "screenshot_url": f"/screenshots/latest.png?t={int(time.time())}"
                    })
                else:
                    # Connection might be broken, try to recover
                    logger.warning("Failed to take screenshot after applying fixes, attempting recovery")
                    if hasattr(device_controller, 'reconnect_device'):
                        recovery_success = device_controller.reconnect_device()
                        if recovery_success:
                            return jsonify({
                                "status": "success",
                                "message": "Applied fixes and recovered connection",
                                "recovery_needed": True
                            })
                        else:
                            return jsonify({
                                "status": "error",
                                "message": "Applied fixes but device connection was lost and recovery failed"
                            }), 500
                    else:
                        return jsonify({
                            "status": "warning",
                            "message": "Applied fixes but screenshot verification failed"
                        })
            else:
                logger.error("Failed to apply emulator fixes")
                return jsonify({
                    "status": "error",
                    "message": "Failed to apply emulator fixes"
                }), 500
        else:
            logger.error("Device controller does not support fix_emulator_issues method")
            return jsonify({
                "status": "error",
                "message": "Device controller does not support emulator fixes"
            }), 500

    except Exception as e:
        logger.error(f"Error applying emulator fixes: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Error applying emulator fixes: {str(e)}"
        }), 500

@app.route('/api/recording/rename', methods=['POST'])
def rename_recording():
    """Rename a test case by filename"""
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    new_name = data.get('new_name')

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    if not new_name:
        return jsonify({"status": "error", "error": "No new name provided"}), 400

    try:
        # Call the manager's method to rename the test case
        result = test_case_manager.rename_test_case(filename, new_name)

        if result:
            logger.info(f"Renamed test case {filename} to '{new_name}'")
            return jsonify({
                "status": "success",
                "filename": result['filename'],
                "test_case": result['test_case']
            })
        else:
            logger.error(f"Failed to rename test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to rename test case file"}), 500
    except Exception as e:
        logger.error(f"Error renaming test case: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/recording/duplicate', methods=['POST'])
def duplicate_recording():
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    # new_name = data.get('new_name') # Removed

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    # Removed check for new_name
    # if not new_name: ...

    try:
        # Call the manager's method which now handles naming internally
        new_filename = test_case_manager.duplicate_test_case(filename)

        if new_filename:
            # Load the duplicated file to get the actual name saved within it
            new_test_case_data = test_case_manager.load_test_case(new_filename)
            actual_new_name = new_test_case_data.get('name', '') if new_test_case_data else ''

            logger.info(f"Duplicated test case {filename} to {new_filename} (Name: {actual_new_name})")
            return jsonify({
                "status": "success",
                "new_filename": new_filename,
                "new_name": actual_new_name # Return the name from the file
            })
        else:
            logger.error(f"Failed to duplicate test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to duplicate test case file"}), 500

    except Exception as e:
        logger.error(f"Error duplicating test case file {filename}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/action/swipeTillVisible', methods=['POST'])
def swipe_till_visible_action():
    global device_controller, action_factory
    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    data = request.json
    vector_start = data.get('vector_start')
    vector_end = data.get('vector_end')
    duration = data.get('duration', 300)
    count = data.get('count', 1)
    interval = data.get('interval', 0.5)
    locator_type = data.get('locator_type')
    locator_value = data.get('locator_value')

    # Always get image parameters
    image_filename = data.get('image_filename')
    threshold = data.get('threshold', 0.7)
    timeout = data.get('timeout', 20)

    # If image_filename is present and locator_value is missing or empty, use image as locator
    if image_filename and (not locator_value or locator_value == ""):
        locator_type = 'image'
        locator_value = image_filename
        logger.info(f"Using image as locator: {image_filename}")

    if not vector_start or not vector_end or not locator_type or not locator_value:
        return jsonify({"status": "error", "error": "Missing required parameters"}), 400

    try:
        params = {
            'vector_start': vector_start,
            'vector_end': vector_end,
            'duration': duration,
            'count': count,
            'interval': interval,
            'locator_type': locator_type,
            'locator_value': locator_value
        }

        # Always include image parameters if available
        if image_filename:
            params['image_filename'] = image_filename
            params['threshold'] = threshold
            params['timeout'] = timeout

        if action_factory:
            result = action_factory.execute_action('swipeTillVisible', params)
        else:
            from app.actions.swipe_till_visible_action import SwipeTillVisibleAction
            action = SwipeTillVisibleAction(device_controller)
            result = action.execute(params)

        screenshot_result = device_controller.take_screenshot()
        screenshot_data = None
        if screenshot_result is not None:
            screenshot_data = encode_image_to_base64(screenshot_result['path'])

        recorder = getattr(device_controller, 'recorder', None)
        if recorder:
            recorder.add_action('swipeTillVisible', params)

        return jsonify({
            "status": result.get("status", "success"),
            "message": result.get("message", "Swipe Till Visible executed"),
            "screenshot": screenshot_data
        })
    except Exception as e:
        logger.error(f"Error executing swipeTillVisible: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Test Suites Routes
@app.route('/api/test_suites/list', methods=['GET'])
def list_test_suites():
    try:
        test_suites = test_suites_manager.load_test_suites()
        return jsonify({
            'status': 'success',
            'test_suites': test_suites
        })
    except Exception as e:
        logger.error(f"Error listing test suites: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/<suite_id>', methods=['GET'])
def get_test_suite(suite_id):
    try:
        test_suites = test_suites_manager.load_test_suites()
        suite = next((s for s in test_suites if s['id'] == suite_id), None)

        if not suite:
            return jsonify({
                'status': 'error',
                'error': 'Test suite not found'
            }), 404

        return jsonify({
            'status': 'success',
            'test_suite': suite
        })
    except Exception as e:
        logger.error(f"Error getting test suite: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/create', methods=['POST'])
def create_test_suite():
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        test_cases = data.get('test_cases', [])

        if not name:
            return jsonify({
                'status': 'error',
                'error': 'Test suite name is required'
            }), 400

        if not test_cases:
            return jsonify({
                'status': 'error',
                'error': 'At least one test case is required'
            }), 400

        new_suite = test_suites_manager.create_test_suite(name, description, test_cases)
        return jsonify({
            'status': 'success',
            'test_suite': new_suite
        })
    except Exception as e:
        logger.error(f"Error creating test suite: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/<suite_id>/update', methods=['POST'])
def update_test_suite_route(suite_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'error': 'Missing request body'}), 400

        # Basic validation (can be enhanced)
        if 'name' not in data:
             return jsonify({'status': 'error', 'error': 'Missing required field: name'}), 400

        # Ensure test_cases is present, but allow empty array for auto-save during editing
        if 'test_cases' not in data:
            data['test_cases'] = []

        updated_suite = test_suites_manager.update_test_suite(suite_id, data)

        if updated_suite:
            logger.info(f"Successfully updated test suite {suite_id}")
            return jsonify({
                'status': 'success',
                'test_suite': updated_suite # Return the updated suite data
            })
        else:
            logger.warning(f"Failed to update test suite {suite_id}")
            return jsonify({
                'status': 'error',
                'error': 'Test suite not found or update failed'
            }), 404 # 404 is appropriate if suite ID doesn't exist

    except Exception as e:
        logger.error(f"Error updating test suite {suite_id}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/<suite_id>/duplicate', methods=['POST'])
def duplicate_test_suite(suite_id):
    """Duplicate a test suite by ID"""
    try:
        # Call the manager's method to duplicate the test suite
        new_suite = test_suites_manager.duplicate_test_suite(suite_id)

        if new_suite:
            logger.info(f"Successfully duplicated test suite {suite_id} to {new_suite['id']}")
            return jsonify({
                'status': 'success',
                'test_suite': new_suite
            })
        else:
            logger.error(f"Failed to duplicate test suite {suite_id}")
            return jsonify({
                'status': 'error',
                'error': 'Test suite not found or duplication failed'
            }), 404
    except Exception as e:
        logger.error(f"Error duplicating test suite: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/<suite_id>/rename', methods=['POST'])
def rename_test_suite(suite_id):
    """Rename a test suite by ID"""
    try:
        data = request.get_json()
        new_name = data.get('new_name')

        if not new_name:
            return jsonify({
                'status': 'error',
                'error': 'New name is required'
            }), 400

        # Call the manager's method to rename the test suite
        result = test_suites_manager.rename_test_suite(suite_id, new_name)

        if result:
            logger.info(f"Renamed test suite {suite_id} to '{new_name}'")
            return jsonify({
                'status': 'success',
                'id': result['id'],
                'filename': result['filename'],
                'suite_data': result['suite_data'],
                'old_id': result['old_id']
            })
        else:
            logger.error(f"Failed to rename test suite: {suite_id}")
            return jsonify({
                'status': 'error',
                'error': 'Failed to rename test suite'
            }), 500

    except Exception as e:
        logger.error(f"Error renaming test suite {suite_id}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/test_suites/<suite_id>', methods=['DELETE'])
def delete_test_suite(suite_id):
    try:
        success = test_suites_manager.delete_test_suite(suite_id)
        if success:
            return jsonify({
                'status': 'success'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': 'Test suite not found'
            }), 404
    except Exception as e:
        logger.error(f"Error deleting test suite: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/delete_test_case/<filename>', methods=['DELETE'])
def delete_test_case_route(filename):
    global test_case_manager
    try:
        logger.info(f"Attempting to delete test case via DELETE /api/ route: {filename}")
        success = test_case_manager.delete_test_case(filename)
        if success:
            logger.info(f"Successfully deleted {filename}")
            # Return a success response matching frontend expectation
            return jsonify({"success": True, "message": "Test case deleted successfully", "deleted_name": filename})
        else:
            logger.warning(f"delete_test_case method returned False for {filename}")
            # Consider 404 if file not found vs 500 for other errors
            return jsonify({"success": False, "message": "Failed to delete test case (not found or permission issue)"}), 404
    except Exception as e:
        logger.exception(f"Error in /api/delete_test_case route for {filename}: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test_suites/<suite_id>/run', methods=['POST'])
def run_test_suite(suite_id):
    logger.info(f"Attempting to run test suite with ID: {suite_id}")
    global device_controller, action_factory, player, test_suites_manager, TEST_CASES_DIR, socketio # Added TEST_CASES_DIR and socketio

    # Try to get device_id from request data first, then fall back to session
    data = request.get_json() or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            player = players.get(session_device_key)
            logger.debug(f"Using session-specific controller for test suite execution: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            player = players.get(device_id)
            logger.debug(f"Using legacy controller for test suite execution: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({
                'status': 'error',
                'error': 'Device not connected. Please connect a device first.'
            }), 400
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({
            'status': 'error',
            'error': 'Device not connected. Please connect a device first.'
        }), 400

    # Ensure the global player instance is up-to-date
    if player:
        player.device_controller = device_controller
        player.test_cases_dir = TEST_CASES_DIR
        if 'socketio' in globals() and socketio: # Check if socketio is defined and not None
            player.socketio = socketio
        logger.info("Ensured global player instance is updated with current controller, test_cases_dir, and socketio for suite run.")
    else:
        # This case should ideally not happen if connect_device ensures player is initialized
        logger.error("Global player is not initialized. Cannot run test suite. Please connect a device first.")
        return jsonify({'status': 'error', 'error': 'Player not initialized. Connect device first.'}), 500

    if not device_controller or not device_controller.is_connected():
        logger.error("Device not connected. Cannot run test suite.")
        return jsonify({
            'status': 'error',
            'error': 'Device not connected. Please connect a device first.'
        }), 400

    # Set up execution context for test runs tracking
    import uuid
    execution_id = str(uuid.uuid4())[:10]  # Short unique ID
    app.current_execution_id = execution_id
    app.current_suite_id = suite_id
    app.current_test_idx = 0
    app.current_test_case_name = f"Test Suite {suite_id}"

    # Set the global flag to indicate test suite execution
    global is_test_suite_execution
    is_test_suite_execution = True
    logger.info(f"Set execution context - ID: {execution_id}, Suite: {suite_id}, is_test_suite_execution: True")

    try:
        suites = test_suites_manager.load_test_suites()
        suite = next((s for s in suites if s['id'] == suite_id), None)

        if not suite:
            logger.error(f"Test suite with ID {suite_id} not found.")
            return jsonify({'status': 'error', 'error': 'Test suite not found'}), 404

        logger.info(f"Found test suite: {suite.get('name')}")
        test_cases_to_run_filenames = suite.get('test_cases', [])
        if not test_cases_to_run_filenames:
            logger.warning(f"Test suite {suite.get('name')} has no test cases.")
            return jsonify({'status': 'warning', 'message': 'Test suite has no test cases'}), 200

        if player.is_playing:
            logger.warning("Player is already running another suite or test case.")
            return jsonify({'status': 'error', 'error': 'Player is busy'}), 409
        
        # Initialize a new report for this suite run
        report_dir_path, report_url = player.initialize_suite_report(suite.get('name', 'UnnamedSuite'))
        logger.info(f"Report for suite {suite.get('name')} will be saved to {report_dir_path} and accessible at {report_url}")
        
        # Ensure player has report_generator initialized for the suite
        if not hasattr(player, 'report_generator') or player.report_generator is None:
            from utils.report_generator import ReportGenerator
            player.report_generator = ReportGenerator(report_dir_path, suite.get('name', 'UnnamedSuite'), suite_id)
            logger.info("Initialized ReportGenerator in Player for the new suite run.")
        else:
            player.report_generator.reset_for_new_report(report_dir_path, suite.get('name', 'UnnamedSuite'), suite_id)
            logger.info("Reset existing ReportGenerator in Player for the new suite run.")


        # Run the test suite asynchronously
        threading.Thread(target=player.run_test_suite_async, args=(suite, test_cases_to_run_filenames, report_dir_path)).start()

        return jsonify({
            'status': 'success',
            'message': f'Test suite {suite.get("name")} execution started.',
            'report_id': os.path.basename(report_dir_path), # or a more specific ID
            'report_url': report_url
        })

    except Exception as e:
        logger.error(f"Error running test suite {suite_id}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'error': str(e)}), 500

@app.route('/api/test_suites/<suite_id>/retry_case/<path:filename>/<int:case_index_in_suite>', methods=['POST'])
def retry_test_case_in_suite(suite_id, filename, case_index_in_suite):
    logger.info(f"Attempting to retry test case: {filename} (index {case_index_in_suite}) in suite: {suite_id}")
    global device_controller, action_factory, player, test_suites_manager, test_case_manager, TEST_CASES_DIR, socketio

    try:
        # Ensure the global player instance is up-to-date
        if player:
            player.device_controller = device_controller
            player.test_cases_dir = TEST_CASES_DIR
            if 'socketio' in globals() and socketio:
                 player.socketio = socketio
            logger.info("Ensured global player instance is updated with current controller, test_cases_dir, and socketio.")
        else:
            logger.error("Global player is not initialized. Cannot retry test case.")
            return jsonify({'status': 'error', 'error': 'Player not initialized. Connect device first.'}), 500

        # Validate device connection
        if not device_controller or not device_controller.is_session_active():
            logger.error("Device not connected. Cannot retry test case.")
            return jsonify({
                'status': 'error',
                'error': 'Device not connected.'
            }), 400

        # Validate player status
        if player.is_playing:
            logger.warning("Player is already running another suite or test case.")
            return jsonify({'status': 'error', 'error': 'Player is busy'}), 409

        # Get suite name for context
        suite_name = "RetryTestCase"
        try:
            suites = test_suites_manager.load_test_suites()
            for suite in suites:
                if suite.get('id') == suite_id:
                    suite_name = suite.get('name', 'RetryTestCase')
                    break
            logger.info(f"Found suite name: {suite_name}")
        except Exception as e:
            logger.warning(f"Could not find suite name, using default: {e}")

        # Normalize test case filename
        base_filename = os.path.basename(filename)
        if not base_filename.endswith('.json'):
            base_filename += '.json'
        
        logger.info(f"Looking for test case with filename: {base_filename}")
        
        # Try to load the test case from file
        test_case_data = None
        try:
            # First try direct load
            test_case_data = test_case_manager.load_test_case(base_filename)
            if test_case_data:
                logger.info(f"Successfully loaded test case from file: {base_filename}")
            else:
                logger.warning(f"test_case_manager.load_test_case returned None for {base_filename}")
        except Exception as e:
            logger.warning(f"Could not load test case from file: {e}")
        
        # If direct load failed, try database lookup
        if not test_case_data:
            try:
                from utils.database import get_test_suite, get_test_steps_for_suite
                import json
                import uuid
                
                logger.info(f"Attempting to load test case from database for suite {suite_id}")
                suite_data = get_test_suite(suite_id)
                
                if suite_data and 'test_cases' in suite_data and len(suite_data['test_cases']) > case_index_in_suite:
                    db_test_case = suite_data['test_cases'][case_index_in_suite]
                    
                    if db_test_case:
                        logger.info(f"Found test case in database: {db_test_case.get('name')}")
                        
                        # Create test case structure
                        test_case_data = {
                            'name': db_test_case.get('name', os.path.splitext(base_filename)[0]),
                            'description': db_test_case.get('description', ''),
                            'actions': []
                        }
                        
                        # Get actions from test steps
                        steps = get_test_steps_for_suite(suite_id)
                        if steps:
                            test_steps = [step for step in steps if step.get('test_idx') == case_index_in_suite]
                            logger.info(f"Found {len(test_steps)} steps for test case")
                            
                            for step in test_steps:
                                try:
                                    action = {
                                        'type': step.get('action_type', 'unknown'),
                                        'action_id': step.get('action_id', str(uuid.uuid4())),
                                        'parameters': {}
                                    }
                                    
                                    # Extract parameters from metadata
                                    if step.get('metadata'):
                                        metadata = json.loads(step.get('metadata', '{}'))
                                        if isinstance(metadata, dict):
                                            # Copy parameters from metadata
                                            if 'parameters' in metadata and isinstance(metadata['parameters'], dict):
                                                action['parameters'] = metadata['parameters']
                                            else:
                                                # Look for parameters directly in the metadata
                                                for k, v in metadata.items():
                                                    if k not in ('type', 'action_id', 'parameters'):
                                                        action['parameters'][k] = v
                                    
                                    test_case_data['actions'].append(action)
                                except Exception as action_err:
                                    logger.error(f"Error processing action from step: {action_err}")
                        else:
                            logger.warning("No steps found for test case in database")
                else:
                    logger.warning(f"Test case index {case_index_in_suite} not found in suite data")
            except Exception as db_err:
                logger.error(f"Error accessing test case from database: {str(db_err)}")
                logger.error(traceback.format_exc())
        
        # If we still don't have test data, create a minimal structure
        if not test_case_data:
            logger.warning(f"Creating minimal test case data for {base_filename}")
            test_case_data = {
                'name': os.path.splitext(base_filename)[0],
                'description': 'Retry test case',
                'actions': []
            }
        
        # Ensure we have at least an empty actions list
        if 'actions' not in test_case_data or not isinstance(test_case_data['actions'], list):
            test_case_data['actions'] = []
        
        logger.info(f"Test case has {len(test_case_data.get('actions', []))} actions")
        
        if len(test_case_data['actions']) == 0:
            logger.warning("No actions found for test case. Retry may not be effective.")
        
        # Initialize report
        test_case_name = test_case_data.get('name', os.path.splitext(base_filename)[0])
        report_name_prefix = f"Retry_{test_case_name}"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_name = f"{report_name_prefix}_{timestamp}"
        
        # Create report directory using Settings tab configuration
        try:
            from app.utils.directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if not reports_dir:
                raise Exception("Reports directory not configured in Settings tab")

            # Make sure it's an absolute path
            if not os.path.isabs(reports_dir):
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                reports_dir = os.path.join(base_dir, reports_dir)

            logger.info(f"Using reports directory from Settings tab: {reports_dir}")
        except Exception as e:
            logger.error(f"Error getting reports directory from Settings: {str(e)}")
            raise Exception("Reports directory not configured in Settings tab. Please configure it before running tests.")

        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
            
        report_dir_path = os.path.join(reports_dir, report_name)
        os.makedirs(report_dir_path, exist_ok=True)
        
        # Create screenshots directory inside report directory
        screenshots_dir = os.path.join(report_dir_path, 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)
        
        # Create assets directory inside report directory
        assets_dir = os.path.join(report_dir_path, 'assets')
        os.makedirs(assets_dir, exist_ok=True)
        
        report_url = f"/reports/{report_name}"
        logger.info(f"Report directory initialized at: {report_dir_path}")
        
        # Create report generator
        from utils.report_generator import ReportGenerator
        report_generator = ReportGenerator(report_dir_path, suite_name, suite_id)
        
        # Run in background thread
        def execute_test_case():
            try:
                # Reset player state
                if hasattr(player, 'reset'):
                    player.reset()
                else:
                    # Manual reset if method doesn't exist
                    player.is_playing = False
                    player.current_action_index = 0
                    player.execution_stopped = False
                
                # Set up report
                player.report_generator = report_generator
                player.report_dir_path = report_dir_path
                player.screenshots_dir = screenshots_dir
                
                # Set the suite context
                player.suite_id = suite_id
                player.suite_name = suite_name
                
                logger.info(f"Starting single test case execution for {test_case_name}")
                
                # Run the test case
                player.run_single_test_case_async(
                    test_case_data, 
                    report_dir_path,
                    suite_name,
                    suite_id,
                    case_index_in_suite
                )
                
                logger.info(f"Completed retry execution of test case: {test_case_name}")
            except Exception as e:
                logger.error(f"Error in test case execution thread: {str(e)}")
                logger.error(traceback.format_exc())
                
                # Ensure player is reset
                player.is_playing = False
                
                # Try to emit error notification
                if hasattr(socketio, 'emit'):
                    socketio.emit('action_result', {
                        'status': 'error',
                        'message': f"Error executing test case: {str(e)}",
                        'completed': True
                    })
        
        # Start execution thread
        execution_thread = threading.Thread(target=execute_test_case)
        execution_thread.daemon = True  # Allow thread to exit when main thread exits
        execution_thread.start()
        
        logger.info(f"Started test case execution thread for {test_case_name}")

        return jsonify({
            'status': 'success',
            'message': f'Test case {test_case_name} retry started.',
            'report_id': os.path.basename(report_dir_path),
            'report_url': report_url
        })

    except Exception as e:
        logger.error(f"Error retrying test case {filename} in suite {suite_id}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'error': str(e)}), 500

@app.route('/api/reports', methods=['GET'])
def get_reports():
    try:
        # Get all reports from the database
        from utils.database import get_all_reports
        reports = get_all_reports()

        if not reports:
            logger.info("No reports found in the database")
            return jsonify({
                'status': 'success',
                'reports': []
            })

        return jsonify({
            'status': 'success',
            'reports': reports
        })
    except Exception as e:
        logger.error(f"Error getting reports: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

# REPORTS
@app.route('/api/reports/delete/<path:filename>', methods=['DELETE'])
def delete_report(filename):
    try:
        # Get the reports directory path
        config_data = load_config()
        reports_dir = config_data.get('reports_dir')

        if not reports_dir:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports')

        logger.info(f"Delete report called with filename: {filename}")
        logger.info(f"Reports directory: {reports_dir}")

        # Handle special case for "undefined" reports
        if filename == "undefined" or filename == "undefined/mainreport.html":
            logger.info("Detected 'undefined' report - will delete from database only")

            # Delete from database by name
            from utils.database import delete_report_by_name
            deleted = delete_report_by_name("undefined")

            if deleted:
                return jsonify({
                    'status': 'success',
                    'message': 'Undefined report deleted from database successfully'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to delete undefined report from database'
                }), 500

        # Check if this is a new-style report (directory/mainreport.html)
        if '/' in filename and filename.endswith('/mainreport.html'):
            # Extract the directory name
            dir_name = filename.split('/')[0]
            report_dir = os.path.join(reports_dir, dir_name)

            logger.info(f"Detected directory-style report: {dir_name}, path: {report_dir}")

            # Check if the directory exists
            if os.path.exists(report_dir) and os.path.isdir(report_dir):
                # Delete the entire directory
                shutil.rmtree(report_dir)
                logger.info(f"Deleted report directory: {report_dir}")

                # Also delete the ZIP file if it exists
                zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                    logger.info(f"Deleted report ZIP file: {zip_path}")

                # Also delete from database
                from utils.database import delete_report_by_dir
                delete_report_by_dir(dir_name)

                return jsonify({
                    'status': 'success',
                    'message': f'Report directory {dir_name} deleted successfully'
                })
            else:
                logger.warning(f"Report directory not found: {report_dir}")

                # Try to delete from database anyway
                from utils.database import delete_report_by_dir
                deleted = delete_report_by_dir(dir_name)

                if deleted:
                    return jsonify({
                        'status': 'success',
                        'message': f'Report {dir_name} deleted from database (directory not found)'
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': f'Report directory not found: {dir_name}'
                    }), 404
        # Check if this is just a directory name (testsuite_execution_YYYYMMDD_HHMMSS)
        elif filename.startswith('testsuite_execution_') or re.match(r'^\d{8}_\d{6}$', filename):
            # This is just a directory name
            dir_name = filename
            report_dir = os.path.join(reports_dir, dir_name)

            logger.info(f"Detected directory name: {dir_name}, path: {report_dir}")

            # Check if the directory exists
            if os.path.exists(report_dir) and os.path.isdir(report_dir):
                # Delete the entire directory
                shutil.rmtree(report_dir)
                logger.info(f"Deleted report directory: {report_dir}")

                # Also delete the ZIP file if it exists
                zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                    logger.info(f"Deleted report ZIP file: {zip_path}")

                return jsonify({
                    'status': 'success',
                    'message': f'Report directory {dir_name} deleted successfully'
                })
            else:
                logger.warning(f"Report directory not found: {report_dir}")
                return jsonify({
                    'status': 'error',
                    'message': 'Report directory not found'
                }), 404
        else:
            # Legacy report file
            # Secure the filename to prevent path traversal
            safe_filename = secure_filename(os.path.basename(filename))
            file_path = os.path.join(reports_dir, safe_filename)

            logger.info(f"Detected legacy report file: {safe_filename}, path: {file_path}")

            # Check if file exists
            if not os.path.exists(file_path):
                logger.warning(f"Report file not found: {file_path}")
                return jsonify({
                    'status': 'error',
                    'message': 'Report file not found'
                }), 404

            # Delete the file
            os.remove(file_path)
            logger.info(f"Deleted report file: {file_path}")

            return jsonify({
                'status': 'success',
                'message': f'Report {safe_filename} deleted successfully'
            })
    except Exception as e:
        logger.error(f"Error deleting report: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/reports/get_test_data/<suite_id>', methods=['GET'])
def get_test_data(suite_id):
    """Get test data from the database"""
    try:
        from utils.database import get_test_suite
        suite_data = get_test_suite(suite_id)

        if not suite_data:
            return jsonify({
                'status': 'error',
                'message': f"Test suite not found: {suite_id}"
            }), 404

        return jsonify({
            'status': 'success',
            'data': suite_data
        })
    except Exception as e:
        logger.error(f"Error getting test data: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/reports/generate/<suite_id>', methods=['GET'])
def generate_report_from_suite_id(suite_id):
    """Generate a report from a test suite ID"""
    try:
        # Get the test suite data from the database
        from utils.database import get_test_suite
        suite_data = get_test_suite(suite_id)

        if not suite_data:
            return jsonify({
                'status': 'error',
                'message': f"Test suite not found: {suite_id}"
            }), 404

        # Import the report generator
        from utils.reportGenerator import generateReport

        # Generate the report
        logger.info(f"Generating report for test suite: {suite_id}")
        report_path, zip_path = generateReport(suite_data)

        if report_path and os.path.exists(report_path):
            # Get the report directory (parent of the report file)
            report_dir = os.path.dirname(report_path)
            # Use the directory name as the report identifier
            report_id = os.path.basename(report_dir)
            # Return the report URL and ZIP URL
            report_url = f"/reports/{report_id}/mainreport.html"
            zip_url = f"/api/reports/download_zip/{os.path.basename(zip_path)}"

            # Update the test suite in the database with the report directory
            from utils.database import update_test_suite
            suite_data['report_dir'] = report_dir
            update_test_suite(suite_id, suite_data)

            return jsonify({
                'status': 'success',
                'report_url': report_url,
                'zip_url': zip_url,
                'report_id': report_id,
                'message': 'Report generated successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to generate report'
            }), 500

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """Generate a report from test data provided by the client"""
    try:
        # Get the test data from the request
        test_data = request.json

        if not test_data:
            return jsonify({
                'status': 'error',
                'message': 'No test data provided'
            }), 400

        # Get the reports directory from Settings tab configuration
        try:
            from utils.directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if not reports_dir:
                return jsonify({
                    'status': 'error',
                    'message': 'Reports directory not configured in Settings tab'
                }), 400

            # Ensure absolute path
            if not os.path.isabs(reports_dir):
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                reports_dir = os.path.join(base_dir, reports_dir)

        except Exception as e:
            logger.error(f"Error getting reports directory from database: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to get reports directory configuration'
            }), 500

        # Import the report generator
        from utils.reportGenerator import generateReport

        # Generate the report with the configured reports directory
        logger.info(f"Generating report with test data: {test_data}")
        logger.info(f"Using reports directory: {reports_dir}")
        report_path, zip_path = generateReport(test_data, reports_dir)

        if report_path and os.path.exists(report_path):
            # Get the report directory (parent of the report file)
            report_dir = os.path.dirname(report_path)
            # Use the directory name as the report identifier
            report_id = os.path.basename(report_dir)
            # Return the report URL and ZIP URL
            report_url = f"/reports/{report_id}/mainreport.html"
            zip_url = f"/api/reports/download_zip/{os.path.basename(zip_path)}"

            return jsonify({
                'status': 'success',
                'report_url': report_url,
                'zip_url': zip_url,
                'report_id': report_id,
                'message': 'Report generated successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to generate report'
            }), 500

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/reports/<path:filename>')
def serve_report(filename):
    """Serve report files from the reports directory with the new structure"""
    try:
        # Define directories to check
        directories_to_check = []

        # Get the reports directory using the utility function
        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        logger.info(f"Serving report file: {filename}")
        directories_to_check.append(reports_dir)

        # Also check the alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
            logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
            directories_to_check.append(alt_reports_dir)

        # Parse the path to handle the new directory structure
        path_parts = filename.split('/')

        # Handle the new structure: reports/testsuite_execution_timestamp/...
        if len(path_parts) >= 2 and path_parts[0].startswith('testsuite_execution_'):
            # This is a file within a timestamped report directory
            report_dir = path_parts[0]
            rest_of_path = '/'.join(path_parts[1:])

            # Try to find the file in any of the directories
            file_path = None
            full_report_dir = None

            for current_dir in directories_to_check:
                temp_report_dir = os.path.join(current_dir, report_dir)
                temp_path = os.path.join(temp_report_dir, rest_of_path)
                if os.path.exists(temp_path):
                    file_path = temp_path
                    full_report_dir = temp_report_dir
                    logger.info(f"Found report file: {file_path}")
                    break

            # If not found, return 404
            if not file_path or not full_report_dir:
                logger.error(f"Report file not found: {filename}")
                return jsonify({"error": "Report file not found"}), 404

            # Handle assets directory specially
            if rest_of_path.startswith('assets/'):
                asset_filename = os.path.basename(rest_of_path)
                assets_dir = os.path.join(full_report_dir, 'assets')
                return send_from_directory(assets_dir, asset_filename)

            # Handle screenshots directory specially
            if rest_of_path.startswith('screenshots/'):
                screenshot_filename = os.path.basename(rest_of_path)
                screenshots_dir = os.path.join(full_report_dir, 'screenshots')
                logger.info(f"Serving screenshot: {screenshot_filename} from {screenshots_dir}")
                response = send_from_directory(screenshots_dir, screenshot_filename)
                # Set cache control headers to prevent caching
                response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response

            # For regular files in the report directory
            return send_from_directory(full_report_dir, rest_of_path)

        # Legacy handling for old report structure
        # Try to find the file in any of the directories
        file_path = None
        for current_dir in directories_to_check:
            temp_path = os.path.join(current_dir, filename)
            if os.path.exists(temp_path):
                file_path = temp_path
                logger.info(f"Found report file: {file_path}")
                break

        # If not found, return 404
        if not file_path:
            logger.error(f"Report file not found: {filename}")
            return jsonify({"error": "Report file not found"}), 404

        # Handle assets directory specially
        if filename.startswith('assets/'):
            asset_filename = os.path.basename(filename)

            # Try to find the asset in any of the directories
            for current_dir in directories_to_check:
                assets_dir = os.path.join(current_dir, 'assets')
                asset_path = os.path.join(assets_dir, asset_filename)
                if os.path.exists(asset_path):
                    return send_from_directory(assets_dir, asset_filename)

            return jsonify({"error": "Asset file not found"}), 404

        # Handle screenshots directory specially
        if filename.startswith('screenshots/'):
            # Extract the relative path within screenshots
            screenshot_path = '/'.join(filename.split('/')[1:])  # Remove 'screenshots/' prefix

            # Try to find the screenshot in any of the directories
            for current_dir in directories_to_check:
                screenshots_dir = os.path.join(current_dir, 'screenshots')
                screenshot_full_path = os.path.join(screenshots_dir, screenshot_path)
                if os.path.exists(screenshot_full_path):
                    logger.info(f"Serving legacy screenshot: {screenshot_path} from {screenshots_dir}")
                    response = send_from_directory(screenshots_dir, screenshot_path)
                    # Set cache control headers to prevent caching
                    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                    response.headers['Pragma'] = 'no-cache'
                    response.headers['Expires'] = '0'
                    return response

            return jsonify({"error": "Screenshot file not found"}), 404

        # For regular files, determine which directory to use
        for current_dir in directories_to_check:
            if file_path.startswith(current_dir):
                relative_path = os.path.relpath(file_path, current_dir)
                return send_from_directory(current_dir, relative_path)

        # If we get here, just try to send the file directly
        return send_file(file_path)
    except Exception as e:
        logger.error(f"Error serving report file {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": f"Error serving report file: {str(e)}"}), 500

# Ensure directories exist
os.makedirs(REFERENCE_IMAGES_DIR, exist_ok=True)
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# Ensure reports directory exists
os.makedirs(reports_dir, exist_ok=True)
logger.info(f"Ensured reports directory exists: {reports_dir}")

@app.route('/api/upload_media', methods=['POST'])
def upload_media():
    """Upload a media file to the files_to_push directory"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'No file part in the request'})

        file = request.files['file']

        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'No file selected'})

        # Secure the filename to prevent any security issues
        filename = secure_filename(file.filename)

        # Get the files_to_push directory from config
        try:
            files_to_push_dir = config.FILES_TO_PUSH_DIR
        except AttributeError:
            # If FILES_TO_PUSH_DIR is not defined in config, create a default directory
            files_to_push_dir = Path(__file__).resolve().parent.parent / 'files_to_push'
            logger.warning(f"FILES_TO_PUSH_DIR not found in config, using default: {files_to_push_dir}")

        os.makedirs(files_to_push_dir, exist_ok=True)

        # Save the file
        filepath = os.path.join(str(files_to_push_dir), filename)
        file.save(filepath)

        logger.info(f"Uploaded file: {filename} to {filepath}")

        return jsonify({
            'status': 'success',
            'message': 'File uploaded successfully',
            'filename': filename,
            'filepath': filepath
        })
    except Exception as e:
        logger.error(f"Error uploading media file: {str(e)}")
        return jsonify({'status': 'error', 'message': f"Error uploading file: {str(e)}"})

# Import the global values database
from utils.global_values_db import global_values_db

# Settings API endpoints
@app.route('/api/settings', methods=['GET'])
def get_settings():
    """Get current settings from config.json, config.py, and the global values database"""
    try:
        # Load settings from config.json
        settings = load_config()

        # Get directory paths from database
        paths = directory_paths_db.get_all_paths()

        # Add directory paths to settings
        settings['test_cases_dir'] = paths.get('TEST_CASES', 'test_cases')
        settings['reports_dir'] = paths.get('REPORTS', 'reports')
        settings['reference_images_dir'] = paths.get('REFERENCE_IMAGES', 'reference_images')
        settings['test_suites_dir'] = paths.get('TEST_SUITES', 'test_suites')
        settings['files_to_push_dir'] = paths.get('FILES_TO_PUSH', 'files_to_push')

        # Get global values from the database
        db_values = global_values_db.get_all_values()

        # If database has values, use them
        if db_values:
            settings['global_values'] = db_values
            logger.info(f"Loaded global values from database: {len(db_values)} values")
        else:
            # Fallback to config.py for backward compatibility
            if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
                settings['global_values'] = config.GLOBAL_VALUES
                logger.info(f"Loaded global values from config.py: {len(config.GLOBAL_VALUES)} values")

                # Initialize the database with these values
                global_values_db.save_values(config.GLOBAL_VALUES)
                logger.info("Initialized database with global values from config.py")
            else:
                settings['global_values'] = {}
                logger.info("No global values found in database or config.py")

        return jsonify(settings)
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/settings', methods=['POST'])
def update_settings():
    """Update settings in config.json, config.py, and the global values database"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        logger.info(f"Received settings update: {data}")

        # Handle global values separately
        global_values = None
        if 'global_values' in data:
            global_values = data.pop('global_values')  # Remove from data to handle separately
            logger.info(f"Extracted global values: {len(global_values)} values")

        # If there are other settings besides global values, update them
        if data:
            logger.info(f"Updating directory settings: {data}")

            # Save to config.json
            save_config(data)

            # Update config.py with new directory paths
            config_path = Path(__file__).resolve().parent.parent / 'config.py'
            if config_path.exists():
                with open(config_path, 'r') as f:
                    content = f.read()

                # Helper function to update directory path in config.py
                def update_directory_path(content, dir_key, dir_path):
                    # Try to find the directory key in the DIRECTORIES dictionary
                    pattern = fr"'({dir_key})':\s*BASE_DIR\s*/\s*['\"]([^'\"]+)['\"]"
                    replacement = fr"'\1': BASE_DIR / '{dir_path}'"

                    match = re.search(pattern, content)
                    if match:
                        # Update existing entry
                        return re.sub(pattern, replacement, content)
                    else:
                        # Add new entry to DIRECTORIES dictionary
                        pattern = r"(DIRECTORIES\s*=\s*\{[^\}]+)(\})"
                        replacement = fr"\1    '{dir_key}': BASE_DIR / '{dir_path}',\n\2"
                        return re.sub(pattern, replacement, content)

                # Update reference images directory if provided
                if 'reference_images_dir' in data:
                    content = update_directory_path(content, 'REFERENCE_IMAGES', data['reference_images_dir'])

                # Update files to push directory if provided
                if 'files_to_push_dir' in data:
                    # Try to update FILES_TO_PUSH_DIR variable
                    files_pattern = r"FILES_TO_PUSH_DIR\s*=\s*BASE_DIR\s*/\s*['\"]([^'\"]+)['\"]"
                    files_replacement = f"FILES_TO_PUSH_DIR = BASE_DIR / '{data['files_to_push_dir']}'"

                    if re.search(files_pattern, content):
                        # Update existing entry
                        content = re.sub(files_pattern, files_replacement, content)
                    else:
                        # Add new entry after DIRECTORIES section
                        pattern = r"(# Ensure all directories exist)"
                        replacement = f"# Files to push directory (for media uploads)\n{files_replacement}\n\n\\1"
                        content = re.sub(pattern, replacement, content)

                # Write updated content back to config.py
                with open(config_path, 'w') as f:
                    f.write(content)
            else:
                logger.warning("config.py not found, skipping directory updates")

        # Handle global values if provided
        if global_values is not None:
            logger.info(f"Saving global values to database: {global_values}")

            # Save to the database
            if global_values_db.save_values(global_values):
                logger.info("Global values saved successfully to database")
            else:
                logger.error("Failed to save global values to database")
                return jsonify({"error": "Failed to save global values to database"}), 500

            # For backward compatibility, also update config.py
            try:
                config_path = Path(__file__).resolve().parent.parent / 'config.py'
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        content = f.read()

                    # Update global values in config.py
                    if 'GLOBAL_VALUES' in content:
                        # Update existing GLOBAL_VALUES dictionary
                        global_values_pattern = r"GLOBAL_VALUES\s*=\s*\{[^\}]*\}"
                        global_values_str = "GLOBAL_VALUES = {\n"
                        for key, value in global_values.items():
                            # Format value based on type
                            if isinstance(value, str):
                                global_values_str += f"    '{key}': '{value}',\n"
                            else:
                                global_values_str += f"    '{key}': {value},\n"
                        global_values_str += "}"

                        content = re.sub(global_values_pattern, global_values_str, content)
                    else:
                        # Add new GLOBAL_VALUES section before APPIUM_CONFIG
                        pattern = r"(# Appium configuration)"
                        global_values_str = "# Global values for test parameterization\nGLOBAL_VALUES = {\n"
                        for key, value in global_values.items():
                            # Format value based on type
                            if isinstance(value, str):
                                global_values_str += f"    '{key}': '{value}',\n"
                            else:
                                global_values_str += f"    '{key}': {value},\n"
                        global_values_str += "}\n\n\\1"

                        content = re.sub(pattern, global_values_str, content)

                    # Write updated content back to config.py
                    with open(config_path, 'w') as f:
                        f.write(content)

                    logger.info("Global values also updated in config.py for backward compatibility")
            except Exception as e:
                logger.warning(f"Error updating global values in config.py (non-critical): {str(e)}")
                # Continue even if config.py update fails, as the database is the primary storage now

        return jsonify({"status": "success", "message": "Settings updated successfully"})
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500


# Environment Variables API endpoints (Legacy - moved to Environments tab)
@app.route('/api/environment_variables', methods=['GET'])
def get_environment_variables():
    """Get all environment variables from the database - Legacy endpoint"""
    try:
        # Environment variables are now managed through the Environments tab
        # Return empty list for backward compatibility
        return jsonify({
            'status': 'success',
            'variables': []
        })
    except Exception as e:
        logger.error(f"Error getting environment variables: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/environment_variables', methods=['POST'])
def save_environment_variable():
    """Save or update an environment variable"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        name = data.get('name')
        value = data.get('value')
        description = data.get('description')

        if not name or value is None:
            return jsonify({
                'status': 'error',
                'message': 'Name and value are required'
            }), 400

        from utils.database import save_environment_variable as save_env_var
        success = save_env_var(name, value, description)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Environment variable "{name}" saved successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to save environment variable'
            }), 500

    except Exception as e:
        logger.error(f"Error saving environment variable: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/environment_variables/<name>', methods=['DELETE'])
def delete_environment_variable(name):
    """Delete an environment variable"""
    try:
        from utils.database import delete_environment_variable as delete_env_var
        success = delete_env_var(name)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Environment variable "{name}" deleted successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to delete environment variable'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting environment variable: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/action/enabled', methods=['POST'])
def update_action_enabled():
    """Update the enabled state of an action"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        action_id = data.get('action_id')
        enabled = data.get('enabled', True)

        if not action_id:
            return jsonify({
                'status': 'error',
                'message': 'Action ID is required'
            }), 400

        # Update the enabled state in the database
        from utils.database import update_action_enabled_state
        success = update_action_enabled_state(action_id, enabled)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Action {action_id} {"enabled" if enabled else "disabled"} successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to update action enabled state'
            }), 500

    except Exception as e:
        logger.error(f"Error updating action enabled state: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/capture_image_area', methods=['POST'])
def capture_image_area():
    """
    Captures a specified area from the latest screenshot based on selection
    coordinates relative to the displayed image size.
    """
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()

    # Try to get device_id from request body first, then fall back to session
    data = request.json or {}
    device_id = data.get('device_id') or request.headers.get('X-Device-ID') or get_session_device_id()

    if not device_id:
        logger.error("Capture Image Area Error: No device ID in request or session")
        return jsonify({"status": "error", "error": "No device connected"}), 400

    # Create session-specific key using the same format as other endpoints
    session_device_key = f"{device_id}_{client_session_id}"

    # Debug logging
    logger.info(f"Capture Image Area - Device ID: {device_id}")
    logger.info(f"Capture Image Area - Client Session ID: {client_session_id}")
    logger.info(f"Capture Image Area - Session Device Key: {session_device_key}")
    logger.info(f"Capture Image Area - Available controllers: {list(device_controllers.keys())}")

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
        logger.info(f"Using session-specific controller for capture: {session_device_key}")
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]
        logger.info(f"Using legacy controller for capture: {device_id}")
    else:
        # Try to find any controller for this device_id (regardless of session)
        # Look for any key that starts with the device_id
        for key, controller in device_controllers.items():
            if key.startswith(device_id + "_") or key.startswith(device_id):
                device_controller = controller
                logger.info(f"Using any available controller for device {device_id}: {key}")
                break

        # If still not found, try to find the most recent controller for this device
        if not device_controller:
            matching_controllers = [(key, controller) for key, controller in device_controllers.items()
                                  if device_id in key]
            if matching_controllers:
                # Use the first matching controller
                key, device_controller = matching_controllers[0]
                logger.info(f"Using first matching controller for device {device_id}: {key}")

    if not device_controller:
        logger.error(f"Capture Image Area Error: No device controller found for device {device_id}")
        logger.error(f"Available controllers: {list(device_controllers.keys())}")
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        # data is already parsed above for device_id extraction
        selection_x = data.get('selection_x')
        selection_y = data.get('selection_y')
        selection_width = data.get('selection_width')
        selection_height = data.get('selection_height')
        displayed_width = data.get('displayed_width')
        displayed_height = data.get('displayed_height')
        natural_width = data.get('natural_width')
        natural_height = data.get('natural_height')
        image_name = data.get('name')
        save_debug = data.get('save_debug', True) # Default to True for debugging

        # Basic validation
        required_fields = [
            selection_x, selection_y, selection_width, selection_height,
            displayed_width, displayed_height, natural_width, natural_height, image_name
        ]
        if any(field is None for field in required_fields):
            logger.error(f"Capture Image Area Error: Missing required fields in request: {data}")
            return jsonify({"status": "error", "error": "Missing required fields"}), 400

        if displayed_width <= 0 or displayed_height <= 0 or natural_width <= 0 or natural_height <= 0:
             logger.error(f"Capture Image Area Error: Invalid image dimensions received: displayed=({displayed_width}x{displayed_height}), natural=({natural_width}x{natural_height})")
             return jsonify({"status": "error", "error": "Invalid image dimensions"}), 400

        # Secure the filename
        image_name = secure_filename(image_name)
        if not image_name.lower().endswith('.png'):
            image_name += '.png'

        logger.info(f"Received capture area request for '{image_name}':")
        logger.info(f"  Selection (relative to displayed): x={selection_x:.2f}, y={selection_y:.2f}, w={selection_width:.2f}, h={selection_height:.2f}")
        logger.info(f"  Displayed Size: w={displayed_width}, h={displayed_height}")
        logger.info(f"  Natural Size: w={natural_width}, h={natural_height}")

        # Call the controller method to handle cropping and saving
        result = device_controller.capture_image_area(
            selection_x=float(selection_x),
            selection_y=float(selection_y),
            selection_width=float(selection_width),
            selection_height=float(selection_height),
            displayed_width=float(displayed_width),
            displayed_height=float(displayed_height),
            natural_width=float(natural_width),
            natural_height=float(natural_height),
            image_name=image_name,
            save_debug=save_debug
        )

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error in /api/capture_image_area: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "error": f"Internal server error: {str(e)}"}), 500

@app.route('/api/ios/set_clipboard', methods=['POST'])
def set_clipboard():
    """Set the content of the iOS device's clipboard."""
    global device_controller
    global current_device

    if not device_controller or not current_device:
        logger.error("Set Clipboard Error: No device connected")
        return jsonify({"status": "error", "message": "No device connected"}), 400

    try:
        data = request.json
        content = data.get('content')

        if content is None:
            logger.error("Set Clipboard Error: No content provided")
            return jsonify({"status": "error", "message": "No clipboard content provided"}), 400

        # Get platform info to ensure it's iOS
        session_info = device_controller.get_session_info()
        if session_info and 'capabilities' in session_info:
            platform_name = session_info['capabilities'].get('platformName', '').lower()
            if platform_name != 'ios':
                logger.error(f"Set Clipboard Error: Not an iOS device (platform: {platform_name})")
                return jsonify({"status": "error", "message": "This function is only available for iOS devices"}), 400

        # Call the controller's set_clipboard method
        result = device_controller.set_clipboard(content)

        if result["status"] == "success":
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error in set_clipboard: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"Internal server error: {str(e)}"}), 500

@app.route('/api/ios/paste_clipboard', methods=['POST'])
def paste_clipboard():
    """Paste the current clipboard content at the current cursor position (iOS only)."""
    global device_controller
    global current_device

    if not device_controller or not current_device:
        logger.error("Paste Clipboard Error: No device connected")
        return jsonify({"status": "error", "message": "No device connected"}), 400

    try:
        # Get platform info to ensure it's iOS
        session_info = device_controller.get_session_info()
        if session_info and 'capabilities' in session_info:
            platform_name = session_info['capabilities'].get('platformName', '').lower()
            if platform_name != 'ios':
                logger.error(f"Paste Clipboard Error: Not an iOS device (platform: {platform_name})")
                return jsonify({"status": "error", "message": "This function is only available for iOS devices"}), 400

        # Call the controller's paste_clipboard method
        result = device_controller.paste_clipboard()

        if result["status"] == "success":
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error in paste_clipboard: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"Internal server error: {str(e)}"}), 500

@app.route('/api/text_detection', methods=['POST'])
def detect_text():
    """Detect text in a screenshot and return the coordinates"""
    global device_controller, current_device

    if not device_controller or not current_device:
        return jsonify({"status": "error", "error": "No device connected"})

    try:
        data = request.get_json()
        text_to_find = data.get('text_to_find')

        if not text_to_find:
            return jsonify({"status": "error", "error": "Missing text_to_find parameter"})

        # Take a screenshot
        screenshot_result = device_controller.take_screenshot()

        if not screenshot_result or screenshot_result.get('status') != 'success':
            return jsonify({"status": "error", "error": "Failed to take screenshot"})

        screenshot_path = screenshot_result.get('path')

        if not screenshot_path or not os.path.exists(screenshot_path):
            return jsonify({"status": "error", "error": "Screenshot file not found"})

        # Get device dimensions
        device_dimensions = None
        if hasattr(device_controller, 'driver') and device_controller.driver:
            try:
                dimensions = device_controller.driver.get_window_size()
                if dimensions:
                    device_dimensions = {
                        "width": dimensions.get("width"),
                        "height": dimensions.get("height")
                    }
                    logger.info(f"Got device dimensions: {device_dimensions}")
            except Exception as e:
                logger.warning(f"Could not get device dimensions: {str(e)}")

        # Import the text detector
        from utils.text_detector import get_text_coordinates

        # Create output directory for debug images
        output_dir = os.path.join(os.path.dirname(screenshot_path), 'text_detection')
        os.makedirs(output_dir, exist_ok=True)

        # Detect text in the screenshot
        coordinates = get_text_coordinates(
            screenshot_path,
            text_to_find,
            device_width=device_dimensions['width'] if device_dimensions else None,
            device_height=device_dimensions['height'] if device_dimensions else None,
            output_dir=output_dir
        )

        if not coordinates:
            return jsonify({"status": "error", "error": f"Text '{text_to_find}' not found in screenshot"})

        # Return the coordinates
        return jsonify({
            "status": "success",
            "text": text_to_find,
            "coordinates": {
                "x": coordinates[0],
                "y": coordinates[1]
            },
            "screenshot_path": screenshot_path
        })

    except Exception as e:
        logger.error(f"Error detecting text: {str(e)}")
        traceback.print_exc()
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/test_cases/update_info', methods=['POST'])
def update_test_case_info():
    """Update test case information (description and notes)"""
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    description = data.get('description')
    notes = data.get('notes')
    labels = data.get('labels', [])

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    try:
        # Load the existing test case
        test_case = test_case_manager.load_test_case(filename)
        if not test_case:
            return jsonify({"status": "error", "error": f"Test case not found: {filename}"}), 404

        # Update the information
        test_case['description'] = description
        test_case['notes'] = notes
        test_case['labels'] = labels
        test_case['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Save the updated test case
        saved_filename = test_case_manager.save_test_case(test_case, filename=filename, is_save_as=False)

        if saved_filename:
            logger.info(f"Updated test case information for {filename}")
            return jsonify({
                "status": "success",
                "message": "Test case information updated successfully"
            })
        else:
            logger.error(f"Failed to save updated test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to save test case file"}), 500
    except Exception as e:
        logger.error(f"Error updating test case information: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/reports/latest', methods=['GET'])
def get_latest_report():
    """Get the URL for the latest report"""
    try:
        print("=== LATEST REPORT ENDPOINT CALLED ===")
        logger.info("Latest report endpoint called")

        # Get the reports directory using the utility function
        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        print(f"=== REPORTS DIRECTORY: {reports_dir} ===")
        logger.info(f"Reports directory: {reports_dir}")

        # Import the getLatestReportUrl function from reportGenerator
        from utils.reportGenerator import getLatestReportUrl
        print("=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===")
        logger.info("Successfully imported getLatestReportUrl function")

        # Get the latest report URL
        print("=== ABOUT TO CALL getLatestReportUrl() ===")
        logger.info("About to call getLatestReportUrl()")
        report_url = getLatestReportUrl()
        print(f"=== getLatestReportUrl() RETURNED: {report_url} ===")
        logger.info(f"getLatestReportUrl() returned: {report_url}")

        if report_url:
            logger.info(f"Found latest report: {report_url}")

            # Extract report ID from the URL for export functionality
            report_id = None
            if '/reports/' in report_url:
                # For URLs like /reports/testsuite_execution_20231214_123456/mainreport.html
                url_parts = report_url.split('/')
                if len(url_parts) >= 3:
                    potential_id = url_parts[2]  # Get the directory name part
                    if potential_id.startswith('testsuite_execution_'):
                        report_id = potential_id
                        logger.info(f"Extracted report ID: {report_id}")

            response_data = {
                'status': 'success',
                'report_url': report_url
            }

            # Add report_id if we found one, for export functionality
            if report_id:
                response_data['success'] = True  # Add success field for export-run.js compatibility
                response_data['report'] = {'report_id': report_id}
                logger.info(f"Added report ID to response: {report_id}")

            return jsonify(response_data)
        else:
            logger.info("No reports found")
            return jsonify({
                'status': 'success',
                'message': 'No reports found',
                'report_url': None
            })
    except Exception as e:
        logger.error(f"Error getting latest report: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/reports/list', methods=['GET'])
def list_reports():
    """Get a list of all available reports"""
    try:
        logger.info("List reports endpoint called")

        # Define directories to check
        directories_to_check = []

        # Get the reports directory using the utility function
        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        logger.info(f"Using reports directory from utility function: {reports_dir}")
        directories_to_check.append(reports_dir)

        # Also check the alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
            logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
            directories_to_check.append(alt_reports_dir)

        # Try to get reports from database first
        try:
            from utils.database import get_all_reports
            db_reports = get_all_reports()
            if db_reports and len(db_reports) > 0:
                logger.info(f"Retrieved {len(db_reports)} reports from database")
                return jsonify({
                    'status': 'success',
                    'reports': db_reports
                })
            else:
                logger.info("No reports found in database, falling back to directory scan")
        except Exception as db_error:
            logger.error(f"Error retrieving reports from database: {str(db_error)}")
            logger.error(traceback.format_exc())
            logger.info("Falling back to directory scan for reports")

        # Scan directories for reports
        report_dirs = []

        # Scan all directories
        for current_dir in directories_to_check:
            if not os.path.exists(current_dir):
                logger.warning(f"Reports directory not found: {current_dir}")
                continue

            logger.info(f"Scanning for reports in: {current_dir}")

            try:
                # Get all directories in the reports directory
                for item in os.listdir(current_dir):
                    item_path = os.path.join(current_dir, item)

                    # Check if this is a report directory
                    if os.path.isdir(item_path) and (item.startswith('testsuite_execution_') or re.match(r'^\d{8}_\d{6}$', item)):
                        # This is a report directory
                        if item not in report_dirs:  # Avoid duplicates
                            report_dirs.append(item)
                            logger.info(f"Found report directory: {item}")
            except Exception as scan_error:
                logger.error(f"Error scanning directory {current_dir}: {str(scan_error)}")
                continue

        # Build report data
        reports = []
        for dir_name in report_dirs:
            # Extract timestamp from directory name
            timestamp = None
            if dir_name.startswith('testsuite_execution_'):
                timestamp = dir_name[len('testsuite_execution_'):]
            else:
                timestamp = dir_name

            # Convert timestamp to date format
            try:
                date_obj = datetime.strptime(timestamp, '%Y%m%d_%H%M%S')
                formatted_date = date_obj.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as date_error:
                logger.warning(f"Error parsing date from {timestamp}: {str(date_error)}")
                formatted_date = timestamp

            # Build report URL
            # Use relative URL for the frontend
            report_url = f"/reports/{dir_name}/mainreport.html"
            zip_url = f"/api/reports/download_zip/{dir_name}.zip"

            # Check if mainreport.html exists in any of the directories
            has_report = False
            mainreport_path = None

            for current_dir in directories_to_check:
                temp_path = os.path.join(current_dir, dir_name, "mainreport.html")
                if os.path.exists(temp_path):
                    mainreport_path = temp_path
                    has_report = True
                    logger.info(f"Found report file: {temp_path}")
                    break

            # Check if zip file exists in any of the directories
            has_zip = False
            zip_path = None

            for current_dir in directories_to_check:
                temp_path = os.path.join(current_dir, f"{dir_name}.zip")
                if os.path.exists(temp_path):
                    zip_path = temp_path
                    has_zip = True
                    logger.info(f"Found zip file: {temp_path}")
                    break

            logger.info(f"Report {dir_name}: mainreport exists: {has_report}, zip exists: {has_zip}")

            # Try to determine status by checking for pass/fail indicators in the report
            status = 'Unknown'
            suite_name = 'Unknown Suite'

            # Check if data.json exists to get more info
            data_json_path = None
            for current_dir in directories_to_check:
                temp_path = os.path.join(current_dir, dir_name, "data.json")
                if os.path.exists(temp_path):
                    data_json_path = temp_path
                    break

            # Extract additional data from data.json if available
            report_name = f"Test Run {formatted_date}"
            total_tests = None
            passed_count = None
            failed_count = None

            if data_json_path:
                try:
                    with open(data_json_path, 'r') as f:
                        data = json.load(f)
                        if 'status' in data:
                            status = data['status']
                        if 'suite_name' in data:
                            suite_name = data['suite_name']
                        # Extract proper report name
                        if 'name' in data:
                            report_name = data['name']
                        elif 'suite_name' in data:
                            report_name = data['suite_name']
                        # Extract test counts
                        if 'testCases' in data:
                            total_tests = len(data['testCases'])
                        if 'passed' in data:
                            passed_count = data['passed']
                        if 'failed' in data:
                            failed_count = data['failed']
                except Exception as json_error:
                    logger.warning(f"Error reading data.json for {dir_name}: {str(json_error)}")

            reports.append({
                'id': dir_name,
                'name': report_name,
                'timestamp': formatted_date,
                'formatted_date': formatted_date,
                'report_dir': dir_name,
                'report_url': report_url if has_report else None,
                'zip_url': zip_url if has_zip else None,
                'has_report': has_report,
                'has_zip': has_zip,
                'status': status,
                'suite_name': suite_name,
                'total_tests': total_tests,
                'passed': passed_count,
                'failed': failed_count,
                'filename': dir_name  # Add filename for compatibility with frontend
            })

        # Sort reports by timestamp (newest first)
        reports.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        logger.info(f"Found {len(reports)} reports in directory scan")
        return jsonify({
            'status': 'success',
            'reports': reports
        })
    except Exception as e:
        logger.error(f"Error in list_reports endpoint: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

def _reconstruct_execution_data_from_database(execution_id, execution_tracking):
    """
    Reconstruct execution data from database tracking records.

    Args:
        execution_id (str): The execution ID
        execution_tracking (list): List of execution tracking records

    Returns:
        dict: Reconstructed execution data in the expected format
    """
    try:
        from utils.database import resolve_execution_id_to_suite_id

        # Resolve to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(execution_id)

        # Group execution data by test case
        test_cases_data = {}
        suite_info = None

        for record in execution_tracking:
            test_case_id = record.get('test_case_id') or record.get('filename', 'unknown')
            if not test_case_id:
                continue

            if test_case_id not in test_cases_data:
                test_cases_data[test_case_id] = {
                    'id': test_case_id,
                    'name': record.get('filename', f'Test Case {test_case_id}'),
                    'steps': [],
                    'status': record.get('status', 'Unknown'),
                    'execution_time': record.get('execution_time', 0)
                }

                # Get suite info from first record
                if not suite_info:
                    suite_info = {
                        'id': actual_suite_id,
                        'name': f'Test Suite {actual_suite_id}',
                        'execution_id': execution_id,
                        'timestamp': record.get('start_time', datetime.datetime.now().isoformat())
                    }

            # Add step data
            step_data = {
                'action_id': record.get('action_id', ''),
                'action_type': record.get('action_type', 'Unknown'),
                'status': record.get('status', 'Unknown'),
                'execution_time': record.get('execution_time', 0),
                'screenshot': record.get('screenshot', ''),
                'error_message': record.get('last_error', ''),
                'step_index': record.get('step_idx', 0)
            }

            test_cases_data[test_case_id]['steps'].append(step_data)

        # Calculate final status for each test case based on all steps
        for test_case_id, test_case_data in test_cases_data.items():
            steps = test_case_data['steps']
            final_status = _determine_final_test_case_status_from_steps(steps)
            test_case_data['status'] = final_status
            logger.debug(f"Test case {test_case_id} final status: {final_status} (based on {len(steps)} steps)")

        # Convert to expected format
        reconstructed_data = {
            'id': actual_suite_id,
            'name': suite_info['name'] if suite_info else f'Test Suite {actual_suite_id}',
            'execution_id': execution_id,
            'timestamp': suite_info['timestamp'] if suite_info else datetime.datetime.now().isoformat(),
            'testCases': list(test_cases_data.values()),
            'report_id': execution_id,
            'reconstructed_from_database': True
        }

        logger.info(f"Successfully reconstructed execution data with {len(test_cases_data)} test cases")
        return reconstructed_data

    except Exception as e:
        logger.error(f"Error reconstructing execution data from database: {e}")
        logger.error(traceback.format_exc())
        return {}

@app.route('/api/execution/import/<execution_id>', methods=['GET'])
def import_execution_data(execution_id):
    """Import execution data from database first, fallback to data.json file"""
    try:
        from utils.directory_utils import get_reports_directory
        from utils.database import get_execution_tracking_for_suite, get_test_steps_for_suite

        logger.info(f"Importing execution data for: {execution_id}")

        # PRIORITY 1: Try to reconstruct from database first
        execution_data = None
        try:
            execution_tracking = get_execution_tracking_for_suite(execution_id)
            if execution_tracking:
                logger.info(f"Found {len(execution_tracking)} execution tracking records in database")
                execution_data = _reconstruct_execution_data_from_database(execution_id, execution_tracking)
                if execution_data:
                    logger.info(f"Successfully reconstructed execution data from database for: {execution_data.get('name', 'Unknown')}")
                    return jsonify({
                        'status': 'success',
                        'execution_data': execution_data,
                        'execution_id': execution_id,
                        'source': 'database'
                    })
        except Exception as db_error:
            logger.warning(f"Could not reconstruct from database: {db_error}")

        # FALLBACK: Try data.json file (legacy support)
        reports_dir = get_reports_directory()
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        logger.info(f"Falling back to data.json file: {data_json_path}")

        if not os.path.exists(data_json_path):
            return jsonify({
                'status': 'error',
                'message': f'No execution data found in database or data.json file for execution: {execution_id}'
            }), 404

        # Read the execution data from file
        with open(data_json_path, 'r') as f:
            execution_data = json.load(f)

        logger.info(f"Successfully loaded execution data from file for: {execution_data.get('name', 'Unknown')}")

        return jsonify({
            'status': 'success',
            'execution_data': execution_data,
            'execution_id': execution_id,
            'source': 'file'
        })

    except Exception as e:
        logger.error(f"Error importing execution data: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/execution/import-to-database/<execution_id>', methods=['POST'])
def import_execution_data_to_database(execution_id):
    """Import execution data from a data.json file into the database"""
    try:
        from utils.directory_utils import get_reports_directory
        from utils.database import track_test_execution

        reports_dir = get_reports_directory()
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        logger.info(f"Importing execution data to database from: {data_json_path}")

        if not os.path.exists(data_json_path):
            return jsonify({
                'status': 'error',
                'message': f'Execution data file not found: {data_json_path}'
            }), 404

        # Read the execution data
        with open(data_json_path, 'r') as f:
            execution_data = json.load(f)

        # Extract execution metadata
        test_execution_id = execution_data.get('test_execution_id', execution_id)
        test_suite_id = execution_data.get('test_suite_id', execution_id)
        suite_name = execution_data.get('name', 'Unknown Suite')

        logger.info(f"Importing execution data: {suite_name} (execution_id: {test_execution_id})")

        # Import each test case and its steps
        imported_count = 0
        for test_case_idx, test_case in enumerate(execution_data.get('testCases', [])):
            test_case_id = test_case.get('test_case_id')
            test_case_name = test_case.get('name', f'Test Case {test_case_idx}')
            test_case_filename = test_case.get('filename', f'{test_case_name}.json')
            test_case_status = test_case.get('status', 'unknown')

            logger.info(f"Importing test case: {test_case_name} (ID: {test_case_id})")

            # Import each step/action
            for step_idx, step in enumerate(test_case.get('steps', [])):
                action_id = step.get('action_id')
                action_type = step.get('action_type', 'unknown')
                step_status = step.get('status', 'unknown')
                retry_count = step.get('retry_count', 0)

                # Import this step into the database
                track_test_execution(
                    suite_id=test_suite_id,
                    test_idx=test_case_idx,
                    filename=test_case_filename,
                    status=step_status,
                    retry_count=retry_count,
                    max_retries=0,
                    error=step.get('error'),
                    in_progress=False,
                    step_idx=step_idx,
                    action_type=action_type,
                    action_params={'description': step.get('description', '')},
                    action_id=action_id,
                    test_case_id=test_case_id,
                    test_execution_id=test_execution_id
                )
                imported_count += 1

        logger.info(f"Successfully imported {imported_count} execution steps to database")

        return jsonify({
            'status': 'success',
            'message': f'Successfully imported {imported_count} execution steps to database',
            'imported_count': imported_count
        })

    except Exception as e:
        logger.error(f"Error importing execution data to database: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/get_execution_context', methods=['GET'])
def get_execution_context():
    """Get the current execution context (suite_id, execution_id, etc.)"""
    try:
        context = {
            'suite_id': getattr(app, 'current_suite_id', None),
            'execution_id': getattr(app, 'current_execution_id', None),
            'test_idx': getattr(app, 'current_test_idx', None),
            'test_case_name': getattr(app, 'current_test_case_name', None),
            'is_test_suite_execution': globals().get('is_test_suite_execution', False)
        }

        logger.info(f"Returning execution context: {context}")
        return jsonify({
            'status': 'success',
            'context': context
        })
    except Exception as e:
        logger.error(f"Error getting execution context: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/get_test_execution_status', methods=['POST'])
def get_test_execution_status():
    """Get the most recent execution status for a specific test case"""
    try:
        data = request.get_json()
        suite_id = data.get('suite_id')
        filename = data.get('filename')
        test_case_name = data.get('test_case_name')

        if not suite_id:
            return jsonify({'error': 'suite_id is required'}), 400

        from utils.database import get_final_test_case_status

        # Get the final status considering all retries
        status_data = get_final_test_case_status(
            suite_id=suite_id,
            test_case_id=data.get('test_case_id'),
            filename=filename,
            test_idx=data.get('test_idx')
        )

        if status_data and status_data.get('status') != 'unknown':
            return jsonify({
                'status': status_data.get('status', 'unknown'),
                'total_actions': status_data.get('total_actions', 0),
                'actions': status_data.get('actions', {})
            })
        else:
            return jsonify({
                'status': 'unknown',
                'message': 'No execution data found'
            })

    except Exception as e:
        logger.error(f"Error getting test execution status: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/execution/get-suite-status-from-db', methods=['POST'])
def get_suite_status_from_db():
    """Get the current status of all test cases in a suite directly from database"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        from utils.database import get_final_test_case_status, get_test_execution_data

        # Get all test cases for this execution
        execution_data = get_test_execution_data(execution_id)
        if not execution_data:
            return jsonify({'error': 'No execution data found'}), 404

        # Group by test case to get unique test cases
        test_cases = {}
        for entry in execution_data:
            filename = entry.get('filename')
            test_idx = entry.get('test_idx')
            if filename and filename not in test_cases:
                test_cases[filename] = {
                    'filename': filename,
                    'test_idx': test_idx,
                    'test_case_id': entry.get('test_case_id')
                }

        # Get current status for each test case from database using UUID-based system
        test_case_statuses = {}
        for filename, test_case_info in test_cases.items():
            # Use the resolved suite_id from the execution data
            resolved_suite_id = execution_data[0].get('suite_id', execution_id) if execution_data else execution_id

            # PREFERRED: Try UUID-based lookup first
            test_case_id = test_case_info.get('test_case_id')
            status_data = None

            if test_case_id:
                status_data = get_final_test_case_status(
                    suite_id=resolved_suite_id,
                    test_case_id=test_case_id
                )

            # FALLBACK: Use filename-based lookup if UUID fails
            if not status_data or status_data.get('status') == 'unknown':
                status_data = get_final_test_case_status(
                    suite_id=resolved_suite_id,
                    filename=filename,
                    test_idx=test_case_info.get('test_idx')
                )

            test_case_statuses[filename] = {
                'status': status_data.get('status', 'unknown'),
                'actions': status_data.get('actions', {}),
                'total_actions': status_data.get('total_actions', 0)
            }

        return jsonify({
            'status': 'success',
            'execution_id': execution_id,
            'test_cases': test_case_statuses
        })

    except Exception as e:
        logger.error(f"Error getting suite status from database: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/reports/generate-from-db', methods=['POST'])
def generate_report_from_database():
    """Generate HTML report directly from database instead of data.json"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        from utils.database import get_test_execution_data, get_final_test_case_status
        from utils.reportGenerator import generate_report_from_database_data
        from utils.directory_utils import get_reports_directory

        # Get execution data from database
        execution_data = get_test_execution_data(execution_id)
        if not execution_data:
            return jsonify({'error': 'No execution data found'}), 404

        # Group by test case to get unique test cases
        test_cases = {}
        for entry in execution_data:
            filename = entry.get('filename')
            test_idx = entry.get('test_idx')
            if filename and filename not in test_cases:
                test_cases[filename] = {
                    'filename': filename,
                    'test_idx': test_idx,
                    'test_case_id': entry.get('test_case_id'),
                    'name': filename.replace('.json', ''),
                    'steps': []
                }

        # Get current status and steps for each test case from database
        for filename, test_case_info in test_cases.items():
            status_data = get_final_test_case_status(
                suite_id=execution_id,
                test_case_id=test_case_info.get('test_case_id'),
                filename=filename,
                test_idx=test_case_info.get('test_idx')
            )

            test_case_info['status'] = status_data.get('status', 'unknown')
            test_case_info['actions'] = status_data.get('actions', {})
            test_case_info['total_actions'] = status_data.get('total_actions', 0)

        # Generate report using database data
        reports_dir = get_reports_directory()
        report_path = generate_report_from_database_data(execution_id, test_cases, reports_dir)

        if report_path:
            return jsonify({
                'status': 'success',
                'report_path': report_path,
                'message': 'Report generated successfully from database'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to generate report from database'
            }), 500

    except Exception as e:
        logger.error(f"Error generating report from database: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/execution/get-data-json', methods=['POST'])
def get_execution_data_json():
    """Get the data.json file for a specific execution"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()

        # Find the execution report directory
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        # If the original execution folder doesn't exist, check for export folders
        if not os.path.exists(data_json_path):
            # Look for export folders that contain this execution ID
            for folder_name in os.listdir(reports_dir):
                if folder_name.startswith(f'export_{execution_id}') and os.path.isdir(os.path.join(reports_dir, folder_name)):
                    export_data_json_path = os.path.join(reports_dir, folder_name, 'data.json')
                    if os.path.exists(export_data_json_path):
                        data_json_path = export_data_json_path
                        break

        if os.path.exists(data_json_path):
            with open(data_json_path, 'r') as f:
                data_json = json.load(f)

            return jsonify({
                'status': 'success',
                'data': data_json
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'data.json not found'
            }), 404

    except Exception as e:
        logger.error(f"Error getting data.json: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/execution/update-retry-results', methods=['POST'])
def update_retry_results():
    """Update execution results with retry data using database-only approach"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')
        retry_results = data.get('retry_results')

        if not execution_id or not retry_results:
            return jsonify({
                'status': 'error',
                'message': 'Missing execution_id or retry_results'
            }), 400

        logger.info(f"Updating retry results for execution_id: {execution_id}")
        logger.info(f"Retry results: {retry_results}")

        # Import database functions
        from utils.database import track_test_execution

        # Extract retry information
        test_case_name = retry_results.get('testCaseName', 'unknown')
        test_case_index = retry_results.get('test_case_index', 0)
        test_case_status = retry_results.get('status', 'unknown')
        retry_actions = retry_results.get('actions', [])

        logger.info(f"Processing retry results for test case: {test_case_name}")
        logger.info(f"Test case index: {test_case_index}")
        logger.info(f"Retry status: {test_case_status}")
        logger.info(f"Number of retry actions: {len(retry_actions)}")

        # Update database with retry results
        # Use execution_id as suite_id for database tracking
        suite_id = execution_id

        # Update each retry action in the database
        for step_idx, retry_action in enumerate(retry_actions):
            track_test_execution(
                suite_id=suite_id,
                test_idx=test_case_index,
                filename=test_case_name,
                status=retry_action.get('status', 'unknown'),
                retry_count=retry_action.get('retry_count', 0),
                max_retries=0,
                error=retry_action.get('error'),
                in_progress=False,
                step_idx=step_idx,
                action_type='retry_update',
                action_params={'action_id': retry_action.get('action_id')},
                action_id=retry_action.get('action_id'),
                test_case_id=retry_results.get('test_case_id'),
                test_execution_id=execution_id
            )

        logger.info(f"Successfully updated database with retry results for: {test_case_name}")

        return jsonify({
            'status': 'success',
            'message': f'Updated database with retry results for {test_case_name}',
            'test_case_updated': test_case_name
        })

    except Exception as e:
        logger.error(f"Error updating retry results: {str(e)}")
        logger.exception(e)
        return jsonify({
            'status': 'error',
            'message': f'Error updating retry results: {str(e)}'
        }), 500

@app.route('/api/test_cases/<test_case_name>', methods=['GET'])
def get_test_case_by_name(test_case_name):
    """Get a test case by name for retry functionality"""
    try:
        from utils.directory_utils import get_test_cases_directory
        test_cases_dir = get_test_cases_directory()

        # Try different file name variations
        possible_names = [
            f"{test_case_name}.json",
            f"{test_case_name}",
            test_case_name
        ]

        test_case_file = None
        for name in possible_names:
            potential_path = os.path.join(test_cases_dir, name)
            if os.path.exists(potential_path):
                test_case_file = potential_path
                break

        if not test_case_file:
            # Try to find a file that starts with the test case name
            for filename in os.listdir(test_cases_dir):
                if filename.startswith(test_case_name) and filename.endswith('.json'):
                    test_case_file = os.path.join(test_cases_dir, filename)
                    break

        if not test_case_file:
            return jsonify({
                'status': 'error',
                'message': f'Test case not found: {test_case_name}'
            }), 404

        # Load the test case
        with open(test_case_file, 'r') as f:
            test_case = json.load(f)

        logger.info(f"Loaded test case {test_case_name} with {len(test_case.get('actions', []))} actions")

        return jsonify(test_case)

    except Exception as e:
        logger.error(f"Error loading test case {test_case_name}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



# Import the directory paths database
from utils.directory_paths_db import directory_paths_db

# Routes for directory paths management
@app.route('/api/directory_paths', methods=['GET'])
def get_directory_paths():
    """Get all directory paths from the database"""
    try:
        # Get paths from the database
        paths = directory_paths_db.get_all_paths()

        # If no paths are in the database, get them from config.py
        if not paths:
            try:
                # Import config to get current directory paths
                import config

                # Add directory paths from config.py if they exist
                if hasattr(config, 'DIRECTORIES'):
                    for key, path_obj in config.DIRECTORIES.items():
                        try:
                            # Try to make the path relative to BASE_DIR
                            relative_path = str(path_obj.relative_to(config.BASE_DIR))
                            paths[key] = relative_path
                        except ValueError:
                            # If it can't be made relative, use absolute path
                            paths[key] = str(path_obj)

                # Add files_to_push directory if it exists
                if hasattr(config, 'FILES_TO_PUSH_DIR'):
                    try:
                        # Try to make the path relative to BASE_DIR
                        relative_path = str(config.FILES_TO_PUSH_DIR.relative_to(config.BASE_DIR))
                        paths['FILES_TO_PUSH'] = relative_path
                    except ValueError:
                        # If it can't be made relative, use absolute path
                        paths['FILES_TO_PUSH'] = str(config.FILES_TO_PUSH_DIR)

                # Initialize the database with these paths
                directory_paths_db.save_all_paths(paths)
                logger.info("Initialized directory paths database from config.py")
            except Exception as e:
                logger.error(f"Error getting directory paths from config.py: {str(e)}")

        # Format paths for the frontend
        formatted_paths = {
            'test_cases_dir': paths.get('TEST_CASES', 'test_cases'),
            'reports_dir': paths.get('REPORTS', 'reports'),
            'reference_images_dir': paths.get('REFERENCE_IMAGES', 'reference_images'),
            'test_suites_dir': paths.get('TEST_SUITES', 'test_suites'),
            'files_to_push_dir': paths.get('FILES_TO_PUSH', 'files_to_push'),
            'temp_files_dir': paths.get('TEMP_FILES', 'temp')
        }

        return jsonify(formatted_paths)
    except Exception as e:
        logger.error(f"Error getting directory paths: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/directory_paths', methods=['POST'])
def update_directory_paths():
    """Update directory paths in the database and config.py"""
    try:
        logger.info("Received directory paths update request")
        data = request.get_json()
        if not data:
            logger.error("No data provided in directory paths update request")
            return jsonify({"error": "No data provided"}), 400

        logger.info(f"Received directory paths update: {data}")

        # Format keys for the database
        paths_dict = {}
        if 'test_cases_dir' in data:
            paths_dict['TEST_CASES'] = data['test_cases_dir']
        if 'reports_dir' in data:
            paths_dict['REPORTS'] = data['reports_dir']
        if 'reference_images_dir' in data:
            paths_dict['REFERENCE_IMAGES'] = data['reference_images_dir']
        if 'test_suites_dir' in data:
            paths_dict['TEST_SUITES'] = data['test_suites_dir']
        if 'files_to_push_dir' in data:
            paths_dict['FILES_TO_PUSH'] = data['files_to_push_dir']
        if 'temp_files_dir' in data:
            paths_dict['TEMP_FILES'] = data['temp_files_dir']

        # Save paths to the database
        logger.info(f"Saving paths to database: {paths_dict}")
        if directory_paths_db.save_all_paths(paths_dict):
            logger.info("Directory paths saved successfully to database")
        else:
            logger.error("Failed to save directory paths to database")
            return jsonify({"error": "Failed to save directory paths to database"}), 500

        # Paths were saved successfully, force config.py to reload from database next time
        logger.info("Directory paths updated successfully")
        return jsonify({"success": True, "message": "Directory paths updated successfully"})
    except Exception as e:
        logger.error(f"Error updating directory paths: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/validate_directory', methods=['POST'])
def validate_directory():
    """Validate a directory path and count its contents"""
    try:
        logger.info("Received directory validation request")
        data = request.get_json()
        logger.info(f"Validation request data: {data}")

        if not data or 'name' not in data or 'path' not in data:
            logger.error("Invalid validation request: missing 'name' or 'path'")
            return jsonify({"error": "Invalid request. 'name' and 'path' required"}), 400

        name = data['name']
        path = data['path']
        logger.info(f"Validating directory: {name} = {path}")

        # Determine the internal key name
        key_mapping = {
            'test_cases_dir': 'TEST_CASES',
            'reports_dir': 'REPORTS',
            'reference_images_dir': 'REFERENCE_IMAGES',
            'test_suites_dir': 'TEST_SUITES',
            'files_to_push_dir': 'FILES_TO_PUSH',
            'temp_files_dir': 'TEMP_FILES'
        }

        # Get the internal key name
        internal_key = key_mapping.get(name, name)

        # Temporarily save the path to the database
        old_path = directory_paths_db.get_path(internal_key)
        directory_paths_db.save_path(internal_key, path)

        # Count directory contents
        counts = directory_paths_db.count_directory_contents(internal_key)

        # Restore the old path if this was just a validation check
        if old_path and 'just_validate' in data and data['just_validate']:
            logger.info(f"Restoring original path for {internal_key}: {old_path}")
            directory_paths_db.save_path(internal_key, old_path)

        logger.info(f"Validation successful for {name}. Counts: {counts}")
        return jsonify({"success": True, "counts": counts})
    except Exception as e:
        logger.error(f"Error validating directory: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/clear_temp_files', methods=['POST'])
def clear_temp_files():
    """Clear all files and folders in the temp directory"""
    try:
        data = request.get_json()
        temp_path = data.get('temp_path', 'temp')

        # Get the absolute path
        if not os.path.isabs(temp_path):
            # Get the base directory from config
            try:
                import config
                base_dir = config.BASE_DIR
            except ImportError:
                base_dir = Path(__file__).resolve().parent.parent
            temp_path = os.path.join(base_dir, temp_path)

        logger.info(f"Clearing temp files in directory: {temp_path}")

        if not os.path.exists(temp_path):
            return jsonify({
                'success': True,
                'message': 'Temp directory does not exist',
                'deleted_count': 0
            })

        if not os.path.isdir(temp_path):
            return jsonify({
                'success': False,
                'error': 'Temp path is not a directory'
            }), 400

        # Count items before deletion
        deleted_count = 0
        for item in os.listdir(temp_path):
            item_path = os.path.join(temp_path, item)
            try:
                if os.path.isdir(item_path):
                    import shutil
                    shutil.rmtree(item_path)
                    deleted_count += 1
                    logger.info(f"Deleted directory: {item_path}")
                else:
                    os.remove(item_path)
                    deleted_count += 1
                    logger.info(f"Deleted file: {item_path}")
            except Exception as e:
                logger.error(f"Error deleting {item_path}: {str(e)}")

        logger.info(f"Successfully cleared {deleted_count} items from temp directory")
        return jsonify({
            'success': True,
            'message': f'Successfully cleared temp directory',
            'deleted_count': deleted_count
        })

    except Exception as e:
        logger.error(f"Error clearing temp files: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reports/download/<path:filename>', methods=['GET'])
def download_report(filename):
    """Download a report file"""
    try:
        # Define directories to check
        directories_to_check = []

        # Get the reports directory using the utility function
        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        logger.info(f"Download report called with filename: {filename}")
        directories_to_check.append(reports_dir)

        # Also check the alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
            logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
            directories_to_check.append(alt_reports_dir)

        # Secure the filename to prevent path traversal
        safe_filename = secure_filename(os.path.basename(filename))

        # Try to find the file in any of the directories
        file_path = None
        for current_dir in directories_to_check:
            temp_path = os.path.join(current_dir, safe_filename)
            if os.path.exists(temp_path):
                file_path = temp_path
                logger.info(f"Found report file: {file_path}")
                break

        # If not found, try looking in subdirectories
        if not file_path:
            logger.warning(f"Report file not found in main directories, checking subdirectories")
            for current_dir in directories_to_check:
                for root, dirs, files in os.walk(current_dir):
                    if safe_filename in files:
                        file_path = os.path.join(root, safe_filename)
                        logger.info(f"Found report file in subdirectory: {file_path}")
                        break
                if file_path:
                    break

        # If still not found, return 404
        if not file_path:
            logger.error(f"Report file not found: {safe_filename}")
            return jsonify({
                'status': 'error',
                'message': 'Report file not found'
            }), 404

        # Return the file as an attachment
        return send_file(file_path, as_attachment=True, download_name=safe_filename)
    except Exception as e:
        logger.error(f"Error downloading report: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/reports/download_zip/<path:filename>', methods=['GET'])
def download_zip_report(filename):
    """Download a ZIP report file"""
    try:
        # Define directories to check
        directories_to_check = []

        # Get the reports directory using the utility function
        from utils.directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        logger.info(f"Download ZIP report called with filename: {filename}")
        directories_to_check.append(reports_dir)

        # Also check the alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
            logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
            directories_to_check.append(alt_reports_dir)

        # Secure the filename to prevent path traversal
        safe_filename = secure_filename(os.path.basename(filename))

        # Try to find the file in any of the directories
        file_path = None
        for current_dir in directories_to_check:
            temp_path = os.path.join(current_dir, safe_filename)
            if os.path.exists(temp_path):
                file_path = temp_path
                logger.info(f"Found ZIP report file: {file_path}")
                break

        # If not found, try looking in subdirectories
        if not file_path:
            logger.warning(f"ZIP report file not found in main directories, checking subdirectories")
            for current_dir in directories_to_check:
                for root, dirs, files in os.walk(current_dir):
                    if safe_filename in files:
                        file_path = os.path.join(root, safe_filename)
                        logger.info(f"Found ZIP report file in subdirectory: {file_path}")
                        break
                if file_path:
                    break

        # If still not found, check if we need to create a zip file
        if not file_path:
            # Check if this is a request for a report directory zip
            if safe_filename.startswith('testsuite_execution_') and safe_filename.endswith('.zip'):
                report_dir_name = safe_filename[:-4]  # Remove .zip extension

                # Try to find the report directory
                report_dir_path = None
                for current_dir in directories_to_check:
                    temp_dir_path = os.path.join(current_dir, report_dir_name)
                    if os.path.exists(temp_dir_path) and os.path.isdir(temp_dir_path):
                        report_dir_path = temp_dir_path
                        logger.info(f"Found report directory: {report_dir_path}")
                        break

                # If report directory found, create a zip file
                if report_dir_path:
                    import zipfile
                    import tempfile

                    # Create a temporary zip file
                    temp_zip_fd, temp_zip_path = tempfile.mkstemp(suffix='.zip')
                    os.close(temp_zip_fd)

                    try:
                        # Create a zip file with the report directory contents
                        with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                            for root, dirs, files in os.walk(report_dir_path):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path, report_dir_path)
                                    zipf.write(file_path, arcname)

                        # Set the file path to the temporary zip file
                        file_path = temp_zip_path
                        logger.info(f"Created ZIP file for report directory: {file_path}")
                    except Exception as zip_error:
                        logger.error(f"Error creating ZIP file: {str(zip_error)}")
                        if os.path.exists(temp_zip_path):
                            os.remove(temp_zip_path)
                        raise

        # If still not found, return 404
        if not file_path:
            logger.error(f"ZIP report file not found: {safe_filename}")
            return jsonify({
                'status': 'error',
                'message': 'ZIP report file not found'
            }), 404

        # Return the file as an attachment
        return send_file(file_path, as_attachment=True, download_name=safe_filename, mimetype='application/zip')
    except Exception as e:
        logger.error(f"Error downloading ZIP report: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# --- Environment Management API Endpoints ---

@app.route('/api/environments', methods=['POST'])
def create_environment_route():
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "Environment name is required"}), 400
    name = data['name'].strip()
    env_id = directory_paths_db.create_environment(name)
    if env_id:
        environment = directory_paths_db.get_environment_by_id(env_id)
        return jsonify({"message": f"Environment '{name}' created successfully", "environment": environment}), 201
    return jsonify({"error": f"Failed to create environment '{name}'. It might already exist."}), 409

@app.route('/api/environments', methods=['GET'])
def get_all_environments_route():
    environments = directory_paths_db.get_all_environments()
    return jsonify(environments), 200

@app.route('/api/environments/<int:env_id>', methods=['PUT'])
def update_environment_route(env_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "New environment name is required"}), 400
    new_name = data['name'].strip()
    success = directory_paths_db.update_environment_name(env_id, new_name)
    if success:
        updated_env = directory_paths_db.get_environment_by_id(env_id)
        return jsonify({"message": f"Environment ID {env_id} updated to '{new_name}'", "environment": updated_env}), 200
    # Check if it failed due to name conflict vs not found
    existing_with_name = directory_paths_db.get_environment_by_name(new_name)
    if existing_with_name and existing_with_name['id'] != env_id:
        return jsonify({"error": f"Failed to update environment. Name '{new_name}' already exists."}), 409
    return jsonify({"error": f"Failed to update environment ID {env_id}. Environment not found or name conflict."}), 404

@app.route('/api/environments/<int:env_id>', methods=['DELETE'])
def delete_environment_route(env_id):
    success = directory_paths_db.delete_environment(env_id)
    if success:
        return jsonify({"message": f"Environment ID {env_id} deleted successfully"}), 200
    return jsonify({"error": f"Failed to delete environment ID {env_id}. Not found."}), 404

@app.route('/api/environments/<int:env_id>/variables', methods=['POST'])
def add_variable_route(env_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "Variable name is required"}), 400
    
    name = data['name'].strip()
    var_type = data.get('type', 'default')
    initial_value = data.get('initial_value', '')
    current_value = data.get('current_value', '')

    # Check if env exists
    if not directory_paths_db.get_environment_by_id(env_id):
        return jsonify({"error": f"Environment ID {env_id} not found."}), 404

    var_id = directory_paths_db.add_environment_variable(env_id, name, var_type, initial_value, current_value)
    if var_id:
        # Fetch the newly created variable to return it
        # This requires a new DB method or constructing the dict here
        # For now, let's assume we can construct it if needed or just return ID
        return jsonify({"message": "Variable added successfully", "variable_id": var_id, "environment_id": env_id, "name": name, "type": var_type, "initial_value": initial_value, "current_value": current_value }), 201
    return jsonify({"error": f"Failed to add variable '{name}'. It might already exist in this environment."}), 409

@app.route('/api/environments/<int:env_id>/variables', methods=['GET'])
def get_variables_route(env_id):
    # Check if env exists
    if not directory_paths_db.get_environment_by_id(env_id):
        return jsonify({"error": f"Environment ID {env_id} not found."}), 404

    variables = directory_paths_db.get_variables_for_environment(env_id)
    return jsonify(variables), 200

@app.route('/api/environments/<int:env_id>/variables/<int:var_id>', methods=['PATCH'])
def update_environment_variable_route(env_id, var_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Check if env exists
    if not directory_paths_db.get_environment_by_id(env_id):
        return jsonify({"error": f"Environment ID {env_id} not found."}), 404

    # Get current variable to check if it belongs to this environment
    variables = directory_paths_db.get_variables_for_environment(env_id)
    variable = next((v for v in variables if v['id'] == var_id), None)
    if not variable:
        return jsonify({"error": f"Variable ID {var_id} not found in environment {env_id}."}), 404

    # Update only the provided fields
    name = data.get('name', variable['name'])
    var_type = data.get('type', variable['type'])
    initial_value = data.get('initial_value', variable['initial_value'])
    current_value = data.get('current_value', variable['current_value'])

    success = directory_paths_db.update_environment_variable(var_id, name, var_type, initial_value, current_value)
    if success:
        return jsonify({"message": f"Variable ID {var_id} updated successfully"}), 200
    return jsonify({"error": f"Failed to update variable ID {var_id}. Variable not found or name conflict within its environment."}), 404

@app.route('/api/environment_variables/<int:var_id>', methods=['PUT'])
def update_variable_route(var_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip(): # Name is mandatory
        return jsonify({"error": "Variable name is required"}), 400

    name = data['name'].strip()
    var_type = data.get('type', 'default') # Default if not provided
    initial_value = data.get('initial_value', '')
    current_value = data.get('current_value', '')

    success = directory_paths_db.update_environment_variable(var_id, name, var_type, initial_value, current_value)
    if success:
        # Fetch the updated variable to return it
        # Requires a get_variable_by_id method or constructing the dict here
        return jsonify({"message": "Variable updated successfully", "variable": {"id": var_id, "name": name, "type": var_type, "initial_value": initial_value, "current_value": current_value}}), 200
    # Distinguish between not found and name conflict
    # This logic is a bit complex here, the DB method update_environment_variable already logs warnings.
    # For the API, a generic failure might be okay, or more specific checks could be added.
    return jsonify({"error": f"Failed to update variable ID {var_id}. Variable not found or name conflict within its environment."}), 404 # Or 409 for conflict

@app.route('/api/environment_variables/<int:var_id>', methods=['DELETE'])
def delete_variable_route(var_id):
    success = directory_paths_db.delete_environment_variable(var_id)
    if success:
        return jsonify({"message": f"Variable ID {var_id} deleted successfully"}), 200
    return jsonify({"error": f"Failed to delete variable ID {var_id}. Not found."}), 404

@app.route('/api/environments/<int:env_id>/export', methods=['GET'])
def export_environment_route(env_id):
    """Export an environment with all its variables"""
    try:
        # Get environment details
        environment = directory_paths_db.get_environment_by_id(env_id)
        if not environment:
            return jsonify({"error": f"Environment ID {env_id} not found."}), 404

        # Get all variables for this environment
        variables = directory_paths_db.get_variables_for_environment(env_id)

        # Create export data structure
        export_data = {
            "name": environment['name'],
            "variables": variables,
            "exported_at": datetime.now().isoformat(),
            "version": "1.0"
        }

        return jsonify({"success": True, "data": export_data}), 200

    except Exception as e:
        logger.error(f"Error exporting environment {env_id}: {str(e)}")
        return jsonify({"error": f"Failed to export environment: {str(e)}"}), 500

@app.route('/api/environments/import', methods=['POST'])
def import_environment_route():
    """Import an environment from exported data"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        if 'name' not in data or not data['name'].strip():
            return jsonify({"error": "Environment name is required"}), 400

        name = data['name'].strip()
        variables = data.get('variables', [])

        # Check if environment with this name already exists
        existing_env = directory_paths_db.get_environment_by_name(name)
        if existing_env:
            # Generate a unique name
            counter = 1
            original_name = name
            while existing_env:
                name = f"{original_name} (Copy {counter})"
                existing_env = directory_paths_db.get_environment_by_name(name)
                counter += 1

        # Create the environment
        env_id = directory_paths_db.create_environment(name)
        if not env_id:
            return jsonify({"error": f"Failed to create environment '{name}'"}), 500

        # Import variables
        imported_variables = []
        for var_data in variables:
            if 'name' not in var_data or not var_data['name'].strip():
                continue  # Skip invalid variables

            var_name = var_data['name'].strip()
            var_type = var_data.get('type', 'default')
            initial_value = var_data.get('initial_value', '')
            current_value = var_data.get('current_value', '')

            var_id = directory_paths_db.add_environment_variable(env_id, var_name, var_type, initial_value, current_value)
            if var_id:
                imported_variables.append({
                    "id": var_id,
                    "name": var_name,
                    "type": var_type,
                    "initial_value": initial_value,
                    "current_value": current_value
                })

        # Get the created environment
        environment = directory_paths_db.get_environment_by_id(env_id)

        return jsonify({
            "success": True,
            "data": {
                "environment": environment,
                "imported_variables_count": len(imported_variables),
                "total_variables_in_import": len(variables)
            }
        }), 201

    except Exception as e:
        logger.error(f"Error importing environment: {str(e)}")
        return jsonify({"error": f"Failed to import environment: {str(e)}"}), 500

# --- New Endpoints for Current Environment and Hover Resolution ---

@app.route('/api/environments/current', methods=['POST'])
def set_current_environment():
    data = request.get_json()
    env_id = data.get('environment_id')
    if env_id is None:
        # Clear it if env_id is None (e.g. "Select" is chosen)
        session.pop('current_environment_id', None)
        logger.info("Current environment selection cleared.")
        return jsonify({"message": "Current environment selection cleared."}), 200
    
    # Verify if env_id is a valid integer
    try:
        env_id = int(env_id)
    except ValueError:
        logger.error(f"Invalid environment_id format: {env_id}")
        return jsonify({"error": "Invalid environment ID format."}), 400

    # Optionally, verify if the environment exists in the DB
    env_details = directory_paths_db.get_environment_by_id(env_id)
    if not env_details:
        logger.error(f"Attempted to set current environment to non-existent ID: {env_id}")
        return jsonify({"error": f"Environment with ID {env_id} not found."}), 404
        
    session['current_environment_id'] = env_id
    logger.info(f"Current environment set to ID: {env_id} ({env_details.get('name')})")
    return jsonify({"message": f"Current environment set to ID: {env_id} ({env_details.get('name')})", "environment": env_details}), 200

@app.route('/api/environments/current', methods=['GET'])
def get_current_environment():
    env_id = get_current_environment_id_from_session()
    if env_id is None:
        return jsonify({"error": "No environment currently selected in session.", "environment_id": None, "name": None}), 200 # Return 200 as it's a valid state
    
    env_details = directory_paths_db.get_environment_by_id(env_id)
    if not env_details:
        # This case might happen if an env was deleted after being set in session
        session.pop('current_environment_id', None) # Clear invalid session ID
        logger.warning(f"Previously selected environment ID {env_id} no longer exists. Cleared from session.")
        return jsonify({"error": f"Previously selected environment ID {env_id} no longer exists.", "environment_id": None, "name": None}), 404
        
    return jsonify(env_details), 200

@app.route('/api/environments/resolve_hover_variable', methods=['POST'])
def resolve_hover_variable():
    data = request.get_json()
    raw_placeholder = data.get('placeholder') # e.g., "env[variable_name]"
    environment_id_override = data.get('environment_id') # Optional: for specific context if needed

    if not raw_placeholder or not raw_placeholder.startswith("env[") or not raw_placeholder.endswith("]"):
        return jsonify({"error": "Invalid placeholder format. Expected env[variable_name]"}), 400

    variable_name = raw_placeholder[4:-1] # Extract 'variable_name'

    current_env_id = environment_id_override if environment_id_override is not None else get_current_environment_id_from_session()

    if current_env_id is None:
        return jsonify({"error": "No environment selected to resolve variable from.", "variable_name": variable_name, "resolved_value": raw_placeholder}), 400 # Or 200 with info

    try:
        current_env_id = int(current_env_id)
    except ValueError:
        return jsonify({"error": "Invalid environment ID format for resolving."}), 400
        
    resolved_value = get_resolved_variable_value(variable_name, current_env_id)
    
    if resolved_value is not None:
        return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": resolved_value, "status": "resolved"}), 200
    else:
        # Check if env exists at all
        env_exists = directory_paths_db.get_environment_by_id(current_env_id)
        if not env_exists:
            return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": raw_placeholder, "status": "error", "message": f"Environment ID {current_env_id} not found."}), 404
        
        # Env exists, but variable doesn't
        return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": raw_placeholder, "status": "not_found", "message": f"Variable '{variable_name}' not found in environment ID {current_env_id}."}), 200

@app.route('/api/reference_images/validate', methods=['POST'])
def validate_reference_image():
    """Validate if an image exists in the reference images directory"""
    try:
        data = request.get_json()
        image_name = data.get('image_name')
        
        if not image_name:
            return jsonify({
                'status': 'error',
                'message': 'No image name provided'
            }), 400
            
        # Check if it's an environment variable
        if image_name.startswith('env[') and image_name.endswith(']'):
            # Extract the variable name
            var_name = image_name[4:-1]
            
            # Get the current environment ID from session
            env_id = get_current_environment_id_from_session()
            
            if env_id:
                # Use the environment_resolver to get the resolved value
                resolved_value = get_resolved_variable_value(var_name, env_id)
                
                if resolved_value:
                    image_name = resolved_value
                else:
                    return jsonify({
                        'status': 'error',
                        'message': f'Environment variable not found: {var_name}'
                    }), 404
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'No active environment selected'
                }), 400
        
        # Get the full path to the image
        full_path = os.path.join(REFERENCE_IMAGES_DIR, image_name)
        
        # Check if the file exists
        if os.path.exists(full_path) and os.path.isfile(full_path):
            return jsonify({
                'status': 'success',
                'message': 'Image exists',
                'path': image_name
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Image not found: {image_name}'
            }), 404
    except Exception as e:
        logger.error(f"Error validating reference image: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error validating reference image: {str(e)}"
        }), 500

@app.route('/api/validate_screenshot_name', methods=['POST'])
def validate_screenshot_name():
    """
    Validate if a screenshot name is unique for test case creation
    """
    try:
        data = request.get_json()
        screenshot_name = data.get('screenshot_name')

        if not screenshot_name:
            return jsonify({
                'valid': False,
                'message': 'Screenshot name is required'
            }), 400

        # Import and use the validation method from TakeScreenshotAction
        from actions.take_screenshot_action import TakeScreenshotAction
        result = TakeScreenshotAction.validate_screenshot_name(screenshot_name)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error validating screenshot name: {str(e)}")
        return jsonify({
            'valid': True,
            'message': 'Unable to validate, but proceeding'
        }), 200

# ... (the rest of app.py, including action execution routes that will be modified next) ...

# Example of where to integrate resolve_text_with_env_variables
# This will be done more thoroughly in the next step.
# For instance, in `/api/action/text`:
# @app.route('/api/action/text', methods=['POST'])
# def text_action():
#     data = request.get_json()
#     text_to_input = data.get('text')
#     # ... other params ...
# 
#     current_env_id = get_current_environment_id_from_session()
#     if current_env_id is not None:
#         text_to_input = resolve_text_with_env_variables(text_to_input, current_env_id)
#         # also resolve other string parameters if they can contain env vars
#
#     # ... rest of the text_action logic ...
#     return jsonify({"status": "success", "message": f"Text input action prepared for '{text_to_input}'"}), 200

# Make sure to set a secret key for session management
# app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'your_very_secret_key_here') # Already added earlier

# Add this new endpoint after the existing report endpoints

@app.route('/api/reports/export/<report_id>', methods=['POST'])
def export_custom_report(report_id):
    """
    Generate a custom report for the given report ID and return a download URL
    
    This endpoint creates a custom HTML report and packages it as a ZIP file
    with screenshots and action logs. The format follows the Action List view
    but without control buttons and with screenshot links.
    
    Args:
        report_id (str): The ID of the report to export
        
    Returns:
        JSON response with success status, message, and download URL
    """
    logger.info(f"Received request to export custom report for {report_id}")
    
    try:
        # Generate the custom report
        success, message, filename = generate_custom_report(report_id, app.root_path)
        
        if success and filename:
            # Create download URL
            download_url = f'/api/reports/download_export/{filename}'
            
            return jsonify({
                'success': True,
                'message': message,
                'download_url': download_url,
                'filename': filename
            })
        else:
            return jsonify({
                'success': False,
                'message': message or 'Failed to generate custom report'
            })
    except Exception as e:
        logger.exception(f"Error in export_custom_report: {e}")
        return jsonify({
            'success': False,
            'message': f'Error generating custom report: {str(e)}'
        })


@app.route('/api/reports/download_export/<path:filename>', methods=['GET'])
def download_export_report(filename):
    """
    Download a custom report export
    
    Args:
        filename (str): The filename of the export to download
        
    Returns:
        The file for download or an error response
    """
    try:
        # Try multiple possible directories for the export file
        possible_directories = []
        
        # Try standard reports directory first
        try:
            from utils.directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            possible_directories.append(reports_dir)
            logger.info(f"Checking main reports directory: {reports_dir}")
        except Exception as e:
            logger.warning(f"Error getting reports directory from utility function: {e}")
        
        # Add fallback directories
        fallback_reports_dir = os.path.join(app.root_path, '..', 'reports')
        fallback_reports_dir = os.path.abspath(fallback_reports_dir)
        possible_directories.append(fallback_reports_dir)
        logger.info(f"Checking fallback reports directory: {fallback_reports_dir}")
        
        # Also check alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir):
            possible_directories.append(alt_reports_dir)
            logger.info(f"Checking alternate reports directory: {alt_reports_dir}")
            
        # Check each possible directory for the file
        file_found = False
        file_path = None
        
        for dir_path in possible_directories:
            if not os.path.exists(dir_path):
                logger.warning(f"Directory does not exist: {dir_path}")
                continue
                
            temp_path = os.path.join(dir_path, filename)
            logger.info(f"Checking for file at: {temp_path}")
            
            if os.path.exists(temp_path) and os.path.isfile(temp_path):
                file_path = temp_path
                file_found = True
                logger.info(f"Found export file at: {file_path}")
                break
        
        if not file_found or not file_path:
            # If not found, do one more thorough search
            logger.warning(f"Export file not found in standard locations, performing deeper search")
            for dir_path in possible_directories:
                if not os.path.exists(dir_path):
                    continue
                    
                # Search for any file with similar name (if filename contains timestamp)
                export_prefix = None
                if '_20' in filename:  # Check if it has a timestamp part
                    export_prefix = filename.split('_20')[0]
                    
                if export_prefix:
                    for entry in os.listdir(dir_path):
                        if entry.startswith(export_prefix) and entry.endswith('.zip'):
                            file_path = os.path.join(dir_path, entry)
                            file_found = True
                            logger.info(f"Found similar export file: {file_path}")
                            break
                
                if file_found:
                    break
        
        if not file_found or not file_path:
            logger.error(f"Export file not found after searching all directories: {filename}")
            
            # List all available zip files in reports directories for debugging
            all_zip_files = []
            for dir_path in possible_directories:
                if os.path.exists(dir_path):
                    for entry in os.listdir(dir_path):
                        if entry.endswith('.zip'):
                            all_zip_files.append(os.path.join(dir_path, entry))
            
            if all_zip_files:
                logger.error(f"Available ZIP files in report directories:")
                for zip_file in all_zip_files[:10]:  # Show first 10 files
                    logger.error(f"  - {zip_file}")
            else:
                logger.error("No ZIP files found in any reports directory")
            
            return jsonify({
                'success': False,
                'message': 'Export file not found. Please try exporting again.'
            }), 404
        
        logger.info(f"Sending export file for download: {file_path}")
        
        # Return the file for download with appropriate content type
        return send_file(
            file_path,
            mimetype='application/zip',
            as_attachment=True,
            download_name=os.path.basename(file_path)
        )
    except Exception as e:
        logger.exception(f"Error in download_export_report: {e}")
        return jsonify({
            'success': False,
            'message': f'Error downloading export: {str(e)}'
        }), 500

@app.route('/api/environments/export', methods=['GET'])
def export_environments():
    """Export all environments and their variables as JSON"""
    try:
        # Get all environments
        environments = directory_paths_db.get_all_environments()
        
        # Get current environment ID
        current_env_id = get_current_environment_id_from_session()
        
        # Build export data structure
        export_data = {
            'environments': [],
            'exported_at': datetime.now().isoformat(),
            'version': '1.0'
        }
        
        # For each environment, get its variables
        for env in environments:
            variables = directory_paths_db.get_variables_for_environment(env['id'])
            
            # Convert variables to simpler format for export
            simplified_vars = []
            for var in variables:
                simplified_vars.append({
                    'name': var['name'],
                    'initial_value': var['initial_value'],
                    'current_value': var['current_value']
                })
            
            # Add environment with its variables to export data
            export_data['environments'].append({
                'name': env['name'],
                'variables': simplified_vars,
                'is_active': env['id'] == current_env_id
            })
        
        return jsonify(export_data), 200
    except Exception as e:
        logger.error(f"Error exporting environments: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': f"Error exporting environments: {str(e)}"
        }), 500

@app.route('/api/environments/import', methods=['POST'])
def import_environments():
    """Import environments and their variables from JSON"""
    try:
        # Get import data from request
        import_data = request.get_json()
        
        if not import_data or 'environments' not in import_data or not isinstance(import_data['environments'], list):
            return jsonify({
                'error': 'Invalid import data format'
            }), 400
        
        results = {
            'environments_created': 0,
            'environments_updated': 0,
            'variables_created': 0,
            'variables_updated': 0,
            'errors': []
        }
        
        # Get all existing environments for comparison
        existing_envs = directory_paths_db.get_all_environments()
        
        # Process each environment in the import data
        for env_data in import_data['environments']:
            if not env_data.get('name') or not isinstance(env_data.get('variables', []), list):
                results['errors'].append(f"Skipping environment with missing name or invalid variables")
                continue
            
            # Check if environment already exists
            existing_env = next((e for e in existing_envs if e['name'] == env_data['name']), None)
            
            env_id = None
            if existing_env:
                # Environment exists, use its ID
                env_id = existing_env['id']
                results['environments_updated'] += 1
            else:
                # Create new environment
                env_id = directory_paths_db.create_environment(env_data['name'])
                if not env_id:
                    results['errors'].append(f"Failed to create environment '{env_data['name']}'")
                    continue
                results['environments_created'] += 1
            
            # Get existing variables for this environment
            existing_vars = directory_paths_db.get_variables_for_environment(env_id)
            
            # Process each variable
            for var_data in env_data['variables']:
                if not var_data.get('name'):
                    results['errors'].append(f"Skipping variable with missing name in environment '{env_data['name']}'")
                    continue
                
                # Check if variable already exists
                existing_var = next((v for v in existing_vars if v['name'] == var_data['name']), None)
                
                if existing_var:
                    # Update existing variable
                    success = directory_paths_db.update_environment_variable(
                        existing_var['id'],
                        var_data['name'],
                        'string',  # Always string as per requirement
                        var_data.get('initial_value', ''),
                        var_data.get('current_value', '')
                    )
                    if success:
                        results['variables_updated'] += 1
                    else:
                        results['errors'].append(f"Failed to update variable '{var_data['name']}' in environment '{env_data['name']}'")
                else:
                    # Create new variable
                    var_id = directory_paths_db.add_environment_variable(
                        env_id,
                        var_data['name'],
                        'string',  # Always string as per requirement
                        var_data.get('initial_value', ''),
                        var_data.get('current_value', '')
                    )
                    if var_id:
                        results['variables_created'] += 1
                    else:
                        results['errors'].append(f"Failed to create variable '{var_data['name']}' in environment '{env_data['name']}'")
            
            # If this was the active environment in the export, make it active now
            if env_data.get('is_active', False):
                session['current_environment_id'] = env_id
        
        return jsonify({
            'message': 'Import completed successfully',
            'results': results
        }), 200
    except Exception as e:
        logger.error(f"Error importing environments: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': f"Error importing environments: {str(e)}"
        }), 500



@app.route('/api/test-execution/complete', methods=['POST'])
def complete_test_execution():
    """Mark a test execution as completed and update its final status"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')
        final_status = data.get('status', 'completed')  # 'completed', 'failed', etc.

        if not execution_id:
            # Use session execution ID if not provided
            execution_id = getattr(app, 'current_execution_id', None)

        if execution_id:
            # Update the test execution status in the database
            from app.utils.database import update_test_execution_status
            success = update_test_execution_status(execution_id, final_status)

            # Clear the session execution ID if this matches the current session
            if hasattr(app, 'current_execution_id') and app.current_execution_id == execution_id:
                app.current_execution_id = None
                logger.info(f"Cleared session execution_id: {execution_id}")

            if success:
                logger.info(f"Test execution {execution_id} marked as {final_status}")
                return jsonify({
                    'status': 'success',
                    'message': f'Test execution marked as {final_status}'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to update test execution status'
                }), 500
        else:
            return jsonify({
                'status': 'error',
                'message': 'No execution ID provided or found in session'
            }), 400

    except Exception as e:
        logger.error(f"Error completing test execution: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/test-execution/reset', methods=['POST'])
def reset_test_execution():
    """Reset the current session execution ID to start a new test execution"""
    try:
        # Clear the current session execution ID
        if hasattr(app, 'current_execution_id'):
            old_execution_id = app.current_execution_id
            app.current_execution_id = None
            logger.info(f"Reset session execution_id: {old_execution_id}")

        return jsonify({
            'status': 'success',
            'message': 'Test execution session reset'
        })

    except Exception as e:
        logger.error(f"Error resetting test execution: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

def _determine_final_test_case_status_from_steps(steps):
    """
    Determine the final status of a test case using unique ID retry logic.

    Args:
        steps (list): List of test steps

    Returns:
        str: Final status ('passed', 'failed', or 'unknown')
    """
    if not steps:
        return 'unknown'

    # Group steps by action_id to handle retries properly
    action_groups = {}

    for step in steps:
        action_id = step.get('action_id') or step.get('clean_action_id') or 'unknown'
        if action_id not in action_groups:
            action_groups[action_id] = []
        action_groups[action_id].append(step)

    # Determine final status for each unique action
    final_action_statuses = []

    for action_id, action_steps in action_groups.items():
        # Use the last step as the most recent execution for this action
        most_recent_step = action_steps[-1]
        step_status = most_recent_step.get('status', 'unknown').lower()

        # Map status to standardized values
        if step_status in ['passed', 'pass']:
            final_action_statuses.append('passed')
        elif step_status in ['failed', 'fail']:
            final_action_statuses.append('failed')
        else:
            final_action_statuses.append('unknown')

    # Determine overall test case status
    if any(status == 'failed' for status in final_action_statuses):
        return 'failed'
    elif any(status == 'passed' for status in final_action_statuses):
        return 'passed'
    else:
        return 'unknown'

# Healenium API endpoints
@app.route('/api/healenium/config', methods=['GET'])
def get_healenium_config():
    """Get Healenium configuration"""
    try:
        from utils.healenium_config import healenium_config
        config_data = healenium_config.get_config_dict()
        return jsonify(config_data)
    except Exception as e:
        logger.error(f"Error getting Healenium config: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/healenium/config', methods=['POST'])
def save_healenium_config():
    """Save Healenium configuration"""
    try:
        from utils.healenium_config import healenium_config
        config_data = request.get_json()

        if healenium_config.save_config(config_data):
            return jsonify({"success": True, "message": "Healenium configuration saved successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to save Healenium configuration"}), 500

    except Exception as e:
        logger.error(f"Error saving Healenium config: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/healenium/status', methods=['GET'])
def get_healenium_status():
    """Get Healenium container and service status"""
    try:
        from utils.healenium_config import healenium_config

        container_status = healenium_config.check_healenium_containers()
        health_status = healenium_config.check_healenium_health()

        return jsonify({
            "container_status": container_status,
            "health_status": health_status,
            "docker_available": healenium_config.check_docker_availability()
        })

    except Exception as e:
        logger.error(f"Error getting Healenium status: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/healenium/start', methods=['POST'])
def start_healenium():
    """Start Healenium containers"""
    try:
        from utils.healenium_config import healenium_config

        if healenium_config.start_healenium_containers():
            return jsonify({"success": True, "message": "Healenium containers started successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to start Healenium containers"}), 500

    except Exception as e:
        logger.error(f"Error starting Healenium containers: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/healenium/stop', methods=['POST'])
def stop_healenium():
    """Stop Healenium containers"""
    try:
        from utils.healenium_config import healenium_config

        if healenium_config.stop_healenium_containers():
            return jsonify({"success": True, "message": "Healenium containers stopped successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to stop Healenium containers"}), 500

    except Exception as e:
        logger.error(f"Error stopping Healenium containers: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    # Get port from config
    try:
        import config
        port = getattr(config, 'FLASK_PORT', 8080)
    except ImportError:
        port = 8080
    app.run(host='0.0.0.0', port=port, debug=True)

